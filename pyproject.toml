[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "thesis_writing"
version = "1.2.8.dev3"
description = "Lib for thesis writing work."
authors = ["feifanuniv <<EMAIL>>"]
readme = "README.md"
packages = [
    {include = "thesis_writing", from = "src"}
]


[tool.poetry.dependencies]
jinja2 = ">=3.1.4"
python = "^3.12"
python-docx = "~1.1.2"
pydantic = "~2.7.4"
portalocker = "^2.3.2"
openai = ">=1.40.0,<2.0.0"
langchain = ">=0.3,<0.4"
langchain-core = ">=0.3,<0.4"
langchain_community = ">=0.3,<0.4"
langchain-openai = "0.2.9"
langgraph = ">=0.2.22,<0.3"
python-dotenv = "^1.0.1"
langfuse = "~2.53.9"
elasticsearch = "~8.15.0"
langchain-text-splitters = ">=0.3,<0.4"
transformers = "~4.43.3"
tiktoken = "~0.7.0"
pytest = "^8.2.2"
pytest-asyncio = "^0.23.7"
trafilatura = "^1.12.2"
beautifulsoup4 = "^4.12.3"
markdown-to-json = "^2.1.2"
pyecharts = "^2.0.7"
snapshot-selenium = "^0.0.2"
diagrams = "^0.24.1"
setuptools = "^75.8.0"
pandas = "^2.2.3"
plotly = "^6.0.0"
defusedxml = "^0.7.1"
playwright = "^1.51.0"
cn2an = "^0.5.23"

[[tool.poetry.source]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple/"
priority = "primary"

