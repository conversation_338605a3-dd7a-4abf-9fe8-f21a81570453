from typing import List

import pandas as pd

from thesis_writing.entity.major import Major
from thesis_writing.utils.resource_util import get_resource_path


# 读取resource下的major.csv文件，返回major信息列表
def get_major_list() -> List[Major]:
    major_df = pd.read_csv(get_resource_path("major.csv"), encoding="utf-8",
                           dtype={"discipline_code": str, "major_code": str})
    major_list = []
    for index, row in major_df.iterrows():
        major = Major(id=row["id"], discipline=row["discipline"], discipline_code=row["discipline_code"],
                      name=row["name"], major_code=row["major_code"])
        major_list.append(major)
    return major_list


class MajorUtil:
    major_list = get_major_list()
    major_dict = {major.name: major for major in major_list}

    # 根据专业代码查找专业信息
    @classmethod
    def get_major_by_code(cls, major_code: str) -> Major | None:
        for major in cls.major_list:
            if major.major_code == major_code:
                return major
        return None

    # 根据专业名称查找专业信息
    @classmethod
    def get_major_by_name(cls, major_name: str) -> Major | None:
        return cls.major_dict.get(major_name)


if __name__ == '__main__':
    code = MajorUtil.get_major_by_code("020301K")
    print(code)
