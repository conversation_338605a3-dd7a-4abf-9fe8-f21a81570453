import json


def dict_to_markdown_json(dict_input: dict) -> str:
    json_dumps = json.dumps(dict_input, ensure_ascii=False, indent=4)
    return "\n".join(["```json", json_dumps, "```"])


def dict_to_markdown(data: dict | list, level: int = 2) -> str:
    """
    Parsing dict to markdown using dfs
    :param data: KV dict
    :param level: Current markdown header level
    """

    row_list = []

    # for list
    if isinstance(data, list):
        for item in data:
            if isinstance(item, dict):
                row_list.append(dict_to_markdown(item, level + 1))
            elif isinstance(item, list):
                row_list.append(dict_to_markdown(item, level + 1))
            else:
                row_list.append(str(item))
        return "\n\n".join(row_list)

    # for dict
    for key, value in data.items():
        row_list.append("#" * level + " " + key)
        if isinstance(value, dict):
            row_list.append(dict_to_markdown(value, level + 1))
        elif isinstance(value, list):
            row_list.append(dict_to_markdown(value, level + 1))
        else:
            row_list.append(str(value))  # prevent join from non-str value
    return "\n\n".join(row_list)


def dicts_to_str(*feedback_dicts) -> str:
    key_info_txt = ''
    for feedback_dict in feedback_dicts:
        key_info_txt = key_info_txt + ''.join(f'{key}: {value}\n' for key, value in feedback_dict.items())

    return key_info_txt
