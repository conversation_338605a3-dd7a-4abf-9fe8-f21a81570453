from thesis_writing.entity.enums import WritingPlanContentType
import re
import cn2an
from typing import List, Optional,Union

def check_writing_plan_content_type(result):
    content_type_list = [content_type.value for content_type in WritingPlanContentType]
    has_conclusion = False
    for node in result.writing_plan_nodes:
        if node.content_type not in content_type_list:
            return False
        if node.content_type == WritingPlanContentType.Conclusion.value:
            has_conclusion = True
        if node.content_type == WritingPlanContentType.Chapter.value and has_conclusion:
            return False
    return True

def strip_existing_numbering(title: str, rule: str=None) -> str:
    strip_regex = re.compile(
        r"^\s*"
        r"("
        r"第\s*[零〇一二三四五六七八九十百千万\d]+\s*[章节]"
        r"|"
        r"\d+(?:\.\d+)*\s*[.．]?"
        r"|"
        r"[零〇一二三四五六七八九十百千万]+\s*[、.、．]"
        r"|"
        r"[\(（【]\s*(?:[零〇一二三四五六七八九十百千万]|\d+)\s*[\)）】]"
        r")"
        r"\s*"
    )
    cleaned_title = strip_regex.sub("", title, count=1).strip()
    if rule is not None and rule.strip() != "":
        pattern_parts = []
        for char in rule:
            if char == 'n':
                pattern_parts.append(r"\d+")
            elif char == 'N':
                pattern_parts.append(r"[零〇一二三四五六七八九十百千万]+")
            elif char == ' ':
                pattern_parts.append(r"\s")
            else:
                pattern_parts.append(re.escape(char))
        final_pattern = r"^" + r"".join(pattern_parts)
        rule_regex = re.compile(final_pattern)
        cleaned_title2 = rule_regex.sub("", title, count=1).strip()
        if len(cleaned_title2) < len(cleaned_title):
            return cleaned_title2
    return cleaned_title

def format_number_string(rule: str, counters: List[int]) -> str:
    formatted_rule = rule
    num_idx = 0

    num_count = sum(1 for char in rule if char in 'nN')
    if num_count < len(counters):
        num_idx = len(counters) - num_count

    for i, char in enumerate(rule):
        if num_idx >= len(counters):
            break
        if char == 'n':
            placeholder = f"__NUM_N__{num_idx}__"
            formatted_rule = formatted_rule.replace(char, placeholder, 1)
            num_idx += 1
        elif char == 'N':
            placeholder = f"__NUM_CHINESE__{num_idx}__"
            formatted_rule = formatted_rule.replace(char, placeholder, 1)
            num_idx += 1
    if len(counters) < num_idx:
        raise ValueError(f"Number of counters {len(counters)} is less than required {num_idx} in rule: {rule}")
    for i in range(num_idx):
        formatted_rule = formatted_rule.replace(f"__NUM_N__{i}__", str(counters[i]))
        formatted_rule = formatted_rule.replace(f"__NUM_CHINESE__{i}__", cn2an.an2cn(str(counters[i])))
    return formatted_rule

def apply_numbering_recursive(
    node,
    level: int,
    counters: List[int],
    rules: List[Optional[str]]
):
    current_rule = rules[level - 1] if level <= len(rules) else None
    if current_rule:
        clean_title = strip_existing_numbering(node.title, rule=current_rule)
        number_prefix = format_number_string(current_rule, counters)
        node.title = f"{number_prefix}{clean_title}"

    if node.children:
        child_counter = 1
        for child_node in node.children:
            child_counters = counters + [child_counter]
            apply_numbering_recursive(child_node, level + 1, child_counters, rules)
            child_counter += 1

def fix_title_numbering(input_data, nodes: list):
    if not input_data.h1_numbering_rule:
        return

    if not nodes or len(nodes) == 0:
        return

    rules = [input_data.h1_numbering_rule, input_data.h2_numbering_rule, input_data.h3_numbering_rule]

    if input_data.main_content_start_number:
        current_h1_counter = input_data.main_content_start_number
    elif input_data.introduction_title_has_number:
        current_h1_counter = 2
    else:
        current_h1_counter = 1

    for i, node in enumerate(nodes):
        is_intro = (node.content_type == "绪论" and i == 0)
        is_conclusion = (node.content_type == "结论")
        is_body = not is_intro and not is_conclusion
        if is_intro:
            if input_data.introduction_title_has_number is False:
                node.title = strip_existing_numbering(node.title, rule=input_data.h1_numbering_rule)
                if node.children:
                    child_counter = 1
                    for child in node.children:
                        apply_numbering_recursive(child, 2, [1, child_counter], rules)
                        child_counter += 1
            else:
                apply_numbering_recursive(node, 1, [1], rules)

        elif is_body:
            apply_numbering_recursive(node, 1, [current_h1_counter], rules)
            current_h1_counter += 1

        elif is_conclusion:
            if input_data.conclusion_title_has_number is False:
                node.title = strip_existing_numbering(node.title, rule=input_data.h1_numbering_rule)
                if node.children:
                    child_counter = 1
                    for child in node.children:
                        apply_numbering_recursive(child, 2, [1, child_counter], rules)
                        child_counter += 1
            else:
                apply_numbering_recursive(node, 1, [current_h1_counter], rules)
                current_h1_counter += 1


def check_sections(input_data, output_data):
    if input_data.conclusion_has_subsections is not None:
        if input_data.conclusion_has_subsections != (len(output_data.get_conclusion_writing_plan().children or []) > 0):
            raise ValueError("结论部分小节数量与设置不符")
    if input_data.introduction_has_subsections is not None:
        if input_data.introduction_has_subsections != (len(output_data.get_introduction_writing_plan().children or []) > 0):
            raise ValueError("绪论部分小节数量与设置不符")
