import math
import random
import re
from typing import Optional, List

from pydantic import BaseModel, Field

from thesis_writing.entity.enums import RetrievalReferenceLanguage


def split_strict_nested_brackets(s):
    pattern = r"\[\[([\d\,，、\s\]\[]+)\]\]"

    def replacer(match):
        content = match.group(1)
        content = content.replace(" ", "")
        replace_pattern = r"[\]\[,，、\s]+"
        content = re.sub(replace_pattern, ",", content)
        parts = content.split(",")
        if all(part.isdigit() for part in parts):
            return "".join(f"[[{part}]]" for part in parts)

        return match.group(0)

    result = re.sub(pattern, replacer, s)
    result = re.sub(r"\[\[(?!\d{1,10}\]\])([^\]]*?)\]\]", "", result)
    return result


class ThesisReference(BaseModel):
    id: Optional[int] = Field(default=None, description="参考文献ID")
    sequence: Optional[int] = Field(
        default=None, description="最终论文中的序号, 从1开始"
    )
    title: str = Field(..., description="论文标题")
    summary: Optional[str] = Field(default=None, description="主要观点")
    year: Optional[int] = Field(default=None, description="年份")
    source: Optional[str] = Field(default=None, description="来源")
    authors: Optional[str] = Field(default=None, description="作者")
    citation: Optional[str] = Field(default=None, description="引用")
    lang: Optional[str] = Field(default=None, description="语言")
    is_user_reference: Optional[bool] = Field(
        default=False, description="是否为用户上传文献"
    )

    def to_basic_str(self):
        return f"""\
<reference>
序号：{self.id}
标题：{self.title}
主要观点：{self.summary}
</reference>"""

    def to_detail_str(self):
        text = "<reference>\n"
        if self.is_user_reference:
            text += "【用户上传】\n"
        text += f"""\
序号：{self.id}
标题：{self.title}
作者：{self.authors}
年份：{self.year}
来源：{self.source}
主要观点：{self.summary}
是否为国外文献：{'是' if self.lang == RetrievalReferenceLanguage.En else '否'}
</reference>"""
        return text

    def to_basic_str_without_label(self):
        text = ""
        if self.is_user_reference:
            text += "【用户上传】\n"
        text += f"""\
标题：{self.title}
作者：{self.authors}
年份：{self.year}
来源：{self.source}
主要观点：{self.summary}"""
        return text


def _do_collect_ref(main_content: str, references: List[ThesisReference]) -> tuple[List[ThesisReference], List[int]]:
    if not main_content:
        main_content = ""
    if not references:
        references = []
    main_content_references = re.findall(r"\[\[(\d+)\]\]", main_content)
    ids = list(map(int, main_content_references))

    not_found_ids = []
    used_references = {}

    sequence = 1
    for ref_id in ids:
        if ref_id in used_references.keys():
            continue

        ref = next((r for r in references if r.id == ref_id), None)
        if ref:
            if ref.citation in (used.citation for used in used_references.values()):
                ref.sequence = next(
                    used.sequence
                    for used in used_references.values()
                    if used.citation == ref.citation
                )
            else:
                ref.sequence = sequence
                sequence += 1
            used_references[ref_id] = ref
        else:
            not_found_ids.append(ref_id)

    return list(used_references.values()), not_found_ids


def to_reference_output_str(references: List[ThesisReference]) -> str:
    if not references:
        return ''
    return ('\n'.join(
        [f"[{each.sequence if each.sequence else index + 1}] {each.citation}" for index, each in
         enumerate(sorted(references,
                          key=lambda reference: (reference.sequence if reference.sequence else 999)))]))


class ReferenceManager(BaseModel):
    references: list[ThesisReference] = Field([], description="参考文献列表")
    user_references: list[ThesisReference] = Field([], description="用户上传文献列表")
    replenish_references: list[ThesisReference] = Field(
        [], description="补充参考文献列表"
    )
    unused_references: list[ThesisReference] = Field([], description="还未使用的参考文献列表")

    def find_reference_by_id(self, id: int):
        for reference in self.references or []:
            if reference.id == id:
                return reference
        return None

    def extend_references(self, references: List[ThesisReference]):
        for reference in references or []:
            self.add_reference(reference)

    def extend_user_references(self, user_references: List[ThesisReference]):
        reference_ids = [ref.id for ref in self.references]
        for reference in user_references:
            if reference.id not in reference_ids:
                self.user_references.append(reference)

    def extend_replenish_references(self, references: List[ThesisReference]):
        self.replenish_references.extend(references)

    def remove_used_references(self, pre_job_used_references: List[ThesisReference]):
        if not pre_job_used_references:
            return
        references = []
        exist_ref_ids = set([ref.id for ref in pre_job_used_references])
        exist_citations = set([ref.citation for ref in pre_job_used_references])
        for reference in self.references:
            if reference.id in exist_ref_ids or reference.citation in exist_citations:
                continue
            references.append(reference)
        self.references = references

    def init_unused_references(self, all_references: List[ThesisReference]):
        if not all_references:
            return
        self.unused_references = []
        self.unused_references.extend(all_references)

    def append_not_exist_unused_references(self, unused_references: List[ThesisReference]):
        if not unused_references:
            return
        if not self.unused_references:
            self.unused_references.extend(unused_references)
            return
        exist_ref_ids = set([ref.id for ref in self.unused_references])
        exist_citations = set([ref.citation for ref in self.unused_references])
        for cur_ref in unused_references:
            if cur_ref.id in exist_ref_ids or cur_ref.citation in exist_citations:
                continue
            self.unused_references.append(cur_ref)

    def add_reference(self, reference: ThesisReference):
        self.references.append(reference)

    def get_all_references(self):
        return self.references

    def get_references_count(self):
        return len(self.references)

    def clear_references(self):
        self.references = []

    def to_basic_str(self):
        if len(self.references) == 0:
            return "无"
        return "\n".join([reference.to_basic_str() for reference in self.references])

    def to_detail_str(self):
        if len(self.references) == 0:
            return "无"
        return "\n".join([reference.to_detail_str() for reference in self.references])

    def unused_reference_to_detail_str(self):
        if len(self.unused_references) == 0:
            return "无"
        return "\n".join([reference.to_detail_str() for reference in self.unused_references])

    def to_basic_str_without_label_str(self):
        if len(self.references) == 0:
            return "无"
        return "\n\n\n".join(
            [reference.to_basic_str_without_label() for reference in self.references]
        )

    def update_unused_references(self, main_content: str):
        used_references, _ = _do_collect_ref(main_content, self.unused_references)
        if not used_references:
            return
        used_reference_id_set = set([ref.id for ref in used_references])
        un_used_references = []
        for reference in self.unused_references:
            if reference.id not in used_reference_id_set:
                un_used_references.append(reference)
        self.unused_references = un_used_references

    def update_references_to_used(self, special_unused_ref_ids: list[int] = None):
        cur_unuse_ref_ids = [ref.id for ref in self.unused_references]
        if special_unused_ref_ids:
            cur_unuse_ref_ids.extend(special_unused_ref_ids)
        all_unused_ref_ids = set(cur_unuse_ref_ids)
        used_refs = []
        for reference in self.references:
            if reference.id in all_unused_ref_ids:
                continue
            used_refs.append(reference)
        self.references = used_refs

    def collect_ref(self, main_content: str) -> tuple[List[ThesisReference], List[int]]:
        return _do_collect_ref(main_content, self.references)

    def fixed_ref_count(
            self, reference_count: int, en_min_percent: int, en_max_percent: int, replenish: bool = True
    ):
        diff = math.ceil(reference_count * 0.06)
        reference_count = reference_count + random.randint(-diff, diff)

        all_reference_ids = [item.id for item in self.references]
        all_citations = [item.citation for item in self.references]
        user_references = []
        if self.user_references:
            for item in self.user_references:
                if (
                        item.id not in all_reference_ids
                        and item.citation not in all_citations
                ):
                    user_references.append(item)
                    all_reference_ids.append(item.id)
                    all_citations.append(item.citation)

        replenish_references = []
        if replenish and self.replenish_references:
            for item in self.replenish_references:
                if (
                        item.id not in all_reference_ids
                        and item.citation not in all_citations
                ):
                    replenish_references.append(item)
                    all_reference_ids.append(item.id)
                    all_citations.append(item.citation)

        uniq_refs = {item.id: item for item in self.references}.values()
        uniq_citation_refs_dict = {item.citation: item for item in uniq_refs}
        uniq_refs = list(uniq_citation_refs_dict.values())
        min_en_reference_count = round(reference_count * en_min_percent / 100)
        max_en_reference_count = round(reference_count * en_max_percent / 100)
        en_references = [
            ref for ref in uniq_refs if ref.lang == RetrievalReferenceLanguage.En
        ]
        user_en_references = [
            ref for ref in user_references if ref.lang == RetrievalReferenceLanguage.En
        ]
        replenish_en_references = [
            ref
            for ref in replenish_references
            if ref.lang == RetrievalReferenceLanguage.En
        ]

        if replenish and len(en_references) < min_en_reference_count:
            need_replenish_count = min_en_reference_count - len(en_references)
            if user_en_references:
                user_en_references = random.sample(
                    user_en_references,
                    k=min(need_replenish_count, len(user_en_references)),
                )
                uniq_refs.extend(user_en_references)
                need_replenish_count -= len(user_en_references)
            if need_replenish_count > 0:
                replenish_en_references = random.sample(
                    replenish_en_references,
                    k=min(need_replenish_count, len(replenish_en_references)),
                )
                uniq_refs.extend(replenish_en_references)
        elif len(en_references) > max_en_reference_count:
            wait_remove_refs = []
            retrival_references = [
                ref for ref in en_references if not ref.is_user_reference
            ]
            remove_count = len(en_references) - max_en_reference_count
            wait_remove_refs.extend(
                random.sample(
                    retrival_references, k=min(remove_count, len(retrival_references))
                )
            )
            if remove_count > len(retrival_references):
                user_references = [
                    ref for ref in en_references if ref.is_user_reference
                ]
                wait_remove_refs.extend(
                    random.sample(
                        user_references, k=remove_count - len(retrival_references)
                    )
                )
            uniq_refs = [ref for ref in uniq_refs if ref not in wait_remove_refs]

        zh_references = [
            ref for ref in uniq_refs if ref.lang == RetrievalReferenceLanguage.Zh
        ]
        user_zh_references = [
            ref for ref in user_references if ref.lang == RetrievalReferenceLanguage.Zh
        ]
        replenish_zh_references = [
            ref
            for ref in replenish_references
            if ref.lang == RetrievalReferenceLanguage.Zh
        ]

        if replenish and len(uniq_refs) < reference_count:
            need_replenish_count = reference_count - len(uniq_refs)
            if user_zh_references:
                user_zh_references = random.sample(
                    user_zh_references,
                    k=min(need_replenish_count, len(user_zh_references)),
                )
                uniq_refs.extend(user_zh_references)
                need_replenish_count -= len(user_zh_references)
            if need_replenish_count > 0:
                replenish_zh_references = random.sample(
                    replenish_zh_references,
                    k=min(need_replenish_count, len(replenish_zh_references)),
                )
                uniq_refs.extend(replenish_zh_references)
        elif len(uniq_refs) > reference_count:
            wait_remove_refs = []
            retrival_references = [
                ref for ref in zh_references if not ref.is_user_reference
            ]
            remove_count = len(uniq_refs) - reference_count
            wait_remove_refs.extend(
                random.sample(
                    retrival_references, k=min(remove_count, len(retrival_references))
                )
            )
            if remove_count > len(retrival_references):
                user_references = [
                    ref for ref in zh_references if ref.is_user_reference
                ]
                wait_remove_refs.extend(
                    random.sample(
                        user_references, k=remove_count - len(retrival_references)
                    )
                )
            uniq_refs = [ref for ref in uniq_refs if ref not in wait_remove_refs]

        self.references = uniq_refs

    def fixed_ref_count_with_user_priority(
            self, reference_count: int, en_min_percent: int, en_max_percent: int, replenish: bool = True
    ):
        """
        固定参考文献数量，优先保留用户上传的文献

        Args:
            reference_count: 目标参考文献数量
            en_min_percent: 英文文献最小比例
            en_max_percent: 英文文献最大比例
        """
        diff = math.ceil(reference_count * 0.06)
        reference_count = reference_count + random.randint(-diff, diff)

        all_reference_ids = [item.id for item in self.references]
        all_citations = [item.citation for item in self.references]
        user_references = []
        if self.user_references:
            for item in self.user_references:
                if (
                        item.id not in all_reference_ids
                        and item.citation not in all_citations
                ):
                    user_references.append(item)
                    all_reference_ids.append(item.id)
                    all_citations.append(item.citation)

        replenish_references = []
        if self.replenish_references:
            for item in self.replenish_references:
                if (
                        item.id not in all_reference_ids
                        and item.citation not in all_citations
                ):
                    replenish_references.append(item)
                    all_reference_ids.append(item.id)
                    all_citations.append(item.citation)

        # 去重处理
        uniq_refs = {item.id: item for item in self.references}.values()
        uniq_citation_refs_dict = {item.citation: item for item in uniq_refs}
        uniq_refs = list(uniq_citation_refs_dict.values())

        # 优先加入所有用户上传的文献
        for user_ref in user_references:
            if user_ref.citation not in [ref.citation for ref in uniq_refs]:
                uniq_refs.append(user_ref)

        min_en_reference_count = round(reference_count * en_min_percent / 100)
        max_en_reference_count = round(reference_count * en_max_percent / 100)

        # 分类现有文献
        en_references = [
            ref for ref in uniq_refs if ref.lang == RetrievalReferenceLanguage.En
        ]
        zh_references = [
            ref for ref in uniq_refs if ref.lang == RetrievalReferenceLanguage.Zh
        ]

        # 分别获取用户上传和非用户上传的英文文献
        user_en_refs = [ref for ref in en_references if ref.is_user_reference]
        non_user_en_refs = [ref for ref in en_references if not ref.is_user_reference]
        replenish_en_references = [
            ref
            for ref in replenish_references
            if ref.lang == RetrievalReferenceLanguage.En
        ]

        # 处理英文文献数量
        if replenish and len(en_references) < min_en_reference_count:
            # 英文文献不够，需要补充
            need_replenish_count = min_en_reference_count - len(en_references)
            if need_replenish_count > 0 and replenish_en_references:
                replenish_en_references = random.sample(
                    replenish_en_references,
                    k=min(need_replenish_count, len(replenish_en_references)),
                )
                uniq_refs.extend(replenish_en_references)
        elif len(en_references) > max_en_reference_count:
            # 英文文献过多，需要移除，优先移除非用户上传的文献
            remove_count = len(en_references) - max_en_reference_count
            wait_remove_refs = []

            # 优先移除非用户上传的英文文献
            if remove_count > 0 and non_user_en_refs:
                remove_from_non_user = min(remove_count, len(non_user_en_refs))
                wait_remove_refs.extend(
                    random.sample(non_user_en_refs, k=remove_from_non_user)
                )
                remove_count -= remove_from_non_user

            # 如果还需要移除，才考虑移除用户上传的英文文献
            if remove_count > 0 and user_en_refs:
                wait_remove_refs.extend(
                    random.sample(user_en_refs, k=min(remove_count, len(user_en_refs)))
                )

            uniq_refs = [ref for ref in uniq_refs if ref not in wait_remove_refs]

        # 重新分类文献（因为可能有变化）
        current_refs_count = len(uniq_refs)
        zh_references = [
            ref for ref in uniq_refs if ref.lang == RetrievalReferenceLanguage.Zh
        ]
        user_zh_refs = [ref for ref in zh_references if ref.is_user_reference]
        non_user_zh_refs = [ref for ref in zh_references if not ref.is_user_reference]
        replenish_zh_references = [
            ref
            for ref in replenish_references
            if ref.lang == RetrievalReferenceLanguage.Zh
        ]

        # 处理总文献数量
        if replenish and current_refs_count < reference_count:
            # 总数不够，需要补充中文文献
            need_replenish_count = reference_count - current_refs_count
            if need_replenish_count > 0 and replenish_zh_references:
                replenish_zh_references = random.sample(
                    replenish_zh_references,
                    k=min(need_replenish_count, len(replenish_zh_references)),
                )
                uniq_refs.extend(replenish_zh_references)
        elif current_refs_count > reference_count:
            # 总数过多，需要移除，优先移除非用户上传的中文文献
            remove_count = current_refs_count - reference_count
            wait_remove_refs = []

            # 优先移除非用户上传的中文文献
            if remove_count > 0 and non_user_zh_refs:
                remove_from_non_user = min(remove_count, len(non_user_zh_refs))
                wait_remove_refs.extend(
                    random.sample(non_user_zh_refs, k=remove_from_non_user)
                )
                remove_count -= remove_from_non_user

            # 如果还需要移除，才考虑移除用户上传的中文文献
            if remove_count > 0 and user_zh_refs:
                wait_remove_refs.extend(
                    random.sample(user_zh_refs, k=min(remove_count, len(user_zh_refs)))
                )

            uniq_refs = [ref for ref in uniq_refs if ref not in wait_remove_refs]

        self.references = uniq_refs

    def reset_unuse_references_sequences(self, unuse_ref_ids: List[int]):
        if not self.references:
            return
        used_max_sequence = 0
        for ref in self.references:
            if ref.id in unuse_ref_ids or not ref.sequence:
                continue
            used_max_sequence = max(used_max_sequence, ref.sequence)
        used_max_sequence += 1
        for ref in self.references:
            if ref.id not in unuse_ref_ids:
                continue
            ref.sequence = used_max_sequence
            used_max_sequence += 1
