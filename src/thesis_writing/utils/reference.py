import math
import random
import re
from typing import Optional, List

from pydantic import BaseModel, Field

from thesis_writing.entity.enums import RetrievalReferenceLanguage


def split_strict_nested_brackets(s):
    pattern = r'\[\[([\d\,，、\s\]\[]+)\]\]'

    def replacer(match):
        content = match.group(1)
        content = content.replace(' ', '')
        replace_pattern = r'[\]\[,，、\s]+'
        content = re.sub(replace_pattern, ',', content)
        parts = content.split(',')
        if all(part.isdigit() for part in parts):
            return ''.join(f'[[{part}]]' for part in parts)

        return match.group(0)

    result = re.sub(pattern, replacer, s)
    result = re.sub(r'\[\[(?!\d{1,10}\]\])([^\]]*?)\]\]', '', result)
    return result


class ThesisReference(BaseModel):
    id: Optional[int] = Field(default=None, description="参考文献ID")
    sequence: Optional[int] = Field(default=None, description="最终论文中的序号, 从1开始")
    title: str = Field(..., description="论文标题")
    summary: Optional[str] = Field(default=None, description="主要观点")
    year: Optional[int] = Field(default=None, description="年份")
    source: Optional[str] = Field(default=None, description="来源")
    authors: Optional[str] = Field(default=None, description="作者")
    citation: Optional[str] = Field(default=None, description="引用")
    lang: Optional[str] = Field(default=None, description="语言")
    is_user_reference: Optional[bool] = Field(default=False, description="是否为用户上传文献")

    def to_basic_str(self):
        return f"""\
<reference>
序号：{self.id}
标题：{self.title}
主要观点：{self.summary}
</reference>"""

    def to_detail_str(self):
        text = "<reference>\n"
        if self.is_user_reference:
            text += "【用户上传】\n"
        text += f"""\
序号：{self.id}
标题：{self.title}
作者：{self.authors}
年份：{self.year}
来源：{self.source}
主要观点：{self.summary}
是否为国外文献：{'是' if self.lang == RetrievalReferenceLanguage.En else '否'}
</reference>"""
        return text

    def to_basic_str_without_label(self):
        text = ""
        if self.is_user_reference:
            text += "【用户上传】\n"
        text += f"""\
标题：{self.title}
作者：{self.authors}
年份：{self.year}
来源：{self.source}
主要观点：{self.summary}"""
        return text


class ReferenceManager(BaseModel):
    references: list[ThesisReference] = Field([], description="参考文献列表")
    user_references: list[ThesisReference] = Field([], description="用户上传文献列表")
    replenish_references: list[ThesisReference] = Field([], description="补充参考文献列表")

    def find_reference_by_id(self, id: int):
        for reference in self.references or []:
            if reference.id == id:
                return reference
        return None

    def extend_references(self, references: List[ThesisReference]):
        for reference in references or []:
            self.add_reference(reference)

    def extend_user_references(self, user_references: List[ThesisReference]):
        reference_ids = [ref.id for ref in self.references]
        for reference in user_references:
            if reference.id not in reference_ids:
                self.user_references.append(reference)

    def extend_replenish_references(self, references: List[ThesisReference]):
        self.replenish_references.extend(references)

    def add_reference(self, reference: ThesisReference):
        self.references.append(reference)

    def get_all_references(self):
        return self.references

    def get_references_count(self):
        return len(self.references)

    def clear_references(self):
        self.references = []

    def to_basic_str(self):
        if len(self.references) == 0:
            return "无"
        return "\n".join([reference.to_basic_str() for reference in self.references])

    def to_detail_str(self):
        if len(self.references) == 0:
            return "无"
        return "\n".join([reference.to_detail_str() for reference in self.references])

    def to_basic_str_without_label_str(self):
        if len(self.references) == 0:
            return "无"
        return "\n\n\n".join([reference.to_basic_str_without_label() for reference in self.references])

    def collect_ref(self, main_content: str) -> tuple[List[ThesisReference], List[int]]:
        references = re.findall(r'\[\[(\d+)\]\]', main_content)
        ids = list(map(int, references))

        not_found_ids = []
        used_references = {}

        sequence = 1
        for ref_id in ids:
            if ref_id in used_references.keys():
                continue

            ref = next((r for r in self.references if r.id == ref_id), None)
            if ref:
                if ref.citation in (used.citation for used in used_references.values()):
                    ref.sequence = next(
                        used.sequence for used in used_references.values()
                        if used.citation == ref.citation
                    )
                else:
                    ref.sequence = sequence
                    sequence += 1
                used_references[ref_id] = ref
            else:
                not_found_ids.append(ref_id)

        return list(used_references.values()), not_found_ids

    def fixed_ref_count(self, reference_count: int, en_min_percent: int, en_max_percent: int):
        diff = math.ceil(reference_count * 0.06)
        reference_count = reference_count + random.randint(-diff, diff)

        all_reference_ids = [item.id for item in self.references]
        all_citations = [item.citation for item in self.references]
        user_references  = []
        if self.user_references:
            for item in self.user_references:
                if item.id not in all_reference_ids and item.citation not in all_citations:
                    user_references.append(item)
                    all_reference_ids.append(item.id)
                    all_citations.append(item.citation)

        replenish_references = []
        if self.replenish_references:
            for item in self.replenish_references:
                if item.id not in all_reference_ids and item.citation not in all_citations:
                    replenish_references.append(item)
                    all_reference_ids.append(item.id)
                    all_citations.append(item.citation)

        uniq_refs = {item.id: item for item in self.references}.values()
        uniq_citation_refs_dict = {item.citation: item for item in uniq_refs}
        uniq_refs = list(uniq_citation_refs_dict.values())
        min_en_reference_count = round(reference_count * en_min_percent / 100)
        max_en_reference_count = round(reference_count * en_max_percent / 100)
        en_references = [ref for ref in uniq_refs if ref.lang == RetrievalReferenceLanguage.En]
        user_en_references = [ref for ref in user_references if ref.lang == RetrievalReferenceLanguage.En]
        replenish_en_references = [ref for ref in replenish_references if ref.lang == RetrievalReferenceLanguage.En]

        if len(en_references) < min_en_reference_count:
            need_replenish_count = min_en_reference_count - len(en_references)
            if user_en_references:
                user_en_references = random.sample(user_en_references,
                                                   k=min(need_replenish_count, len(user_en_references)))
                uniq_refs.extend(user_en_references)
                need_replenish_count -= len(user_en_references)
            if need_replenish_count > 0:
                replenish_en_references = random.sample(replenish_en_references,
                                                        k=min(need_replenish_count, len(replenish_en_references)))
                uniq_refs.extend(replenish_en_references)
        elif len(en_references) > max_en_reference_count:
            wait_remove_refs = []
            retrival_references = [ref for ref in en_references if not ref.is_user_reference]
            remove_count = len(en_references) - max_en_reference_count
            wait_remove_refs.extend(random.sample(retrival_references, k=min(remove_count, len(retrival_references))))
            if remove_count > len(retrival_references):
                user_references = [ref for ref in en_references if ref.is_user_reference]
                wait_remove_refs.extend(random.sample(user_references, k=remove_count - len(retrival_references)))
            uniq_refs = [ref for ref in uniq_refs if ref not in wait_remove_refs]

        zh_references = [ref for ref in uniq_refs if ref.lang == RetrievalReferenceLanguage.Zh]
        user_zh_references = [ref for ref in user_references if ref.lang == RetrievalReferenceLanguage.Zh]
        replenish_zh_references = [ref for ref in replenish_references if ref.lang == RetrievalReferenceLanguage.Zh]

        if len(uniq_refs) < reference_count:
            need_replenish_count = reference_count - len(uniq_refs)
            if user_zh_references:
                user_zh_references = random.sample(user_zh_references,
                                                   k=min(need_replenish_count, len(user_zh_references)))
                uniq_refs.extend(user_zh_references)
                need_replenish_count -= len(user_zh_references)
            if need_replenish_count > 0:
                replenish_zh_references = random.sample(replenish_zh_references,
                                                        k=min(need_replenish_count, len(replenish_zh_references)))
                uniq_refs.extend(replenish_zh_references)
        elif len(uniq_refs) > reference_count:
            wait_remove_refs = []
            retrival_references = [ref for ref in zh_references if not ref.is_user_reference]
            remove_count = len(uniq_refs) - reference_count
            wait_remove_refs.extend(random.sample(retrival_references, k=min(remove_count, len(retrival_references))))
            if remove_count > len(retrival_references):
                user_references = [ref for ref in zh_references if ref.is_user_reference]
                wait_remove_refs.extend(random.sample(user_references, k=remove_count - len(retrival_references)))
            uniq_refs = [ref for ref in uniq_refs if ref not in wait_remove_refs]

        self.references = uniq_refs
