from typing import List, <PERSON><PERSON>

from thesis_writing.retriever.index.base import BaseDocument


def fill_material_temp_id(retrieval_materials: List[Tuple[str, List[BaseDocument]]], material_key: str) \
        -> List[Tuple[str, List[BaseDocument]]]:
    temp_idx = 1
    for query, materials in retrieval_materials:
        if len(materials) == 0:
            continue
        for material in materials:
            material.temp_id = f"{material_key}_{temp_idx}"
            temp_idx += 1
    return retrieval_materials
