import os

import tiktoken
from pkg_resources import resource_filename
from transformers import AutoTokenizer

os.environ["TOKENIZERS_PARALLELISM"] = "false"
qwen_tokenizer_path = resource_filename('thesis_writing', 'resource/tokenizer/qwen')
qwen_tokenizer = AutoTokenizer.from_pretrained(qwen_tokenizer_path)
deepseek_tokenizer_path = resource_filename('thesis_writing', 'resource/tokenizer/deepseek')
deepseek_tokenizer = AutoTokenizer.from_pretrained(deepseek_tokenizer_path)


def calculate_token_count(text: str, model_name: str) -> int:
    if model_name.lower().startswith('gpt') or model_name.lower().startswith('o1'):
        return calculate_gpt_token(text, model_name)
    elif model_name.lower().startswith('qw'):
        return calculate_qwen_token(text)
    elif model_name.lower().startswith('deepseek'):
        return calculate_deepseek_token(text)
    else:
        return calculate_qwen_token(text)


def calculate_gpt_token(text: str, model: str) -> int:
    if model.lower().startswith('gpt-4o') or model.lower().startswith('o1'):
        return len(tiktoken.encoding_for_model('gpt-4o').encode(text))
    else:
        raise ValueError(f"Model {model} not supported.")


def calculate_qwen_token(text: str) -> int:
    return len(qwen_tokenizer(text)['input_ids'])


def calculate_deepseek_token(text: str) -> int:
    return len(deepseek_tokenizer(text)['input_ids'])


def template_tokens_per_message(model: str):
    if model.lower().startswith('gpt-4o') or model.lower().startswith('o1'):
        return 4
    elif model.lower().startswith('qw'):
        return 5
    elif model.lower().startswith('deepseek'):
        return 5
    else:
        return 5
