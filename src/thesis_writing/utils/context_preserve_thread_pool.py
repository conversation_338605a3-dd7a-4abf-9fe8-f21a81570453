import contextvars
import functools
from concurrent.futures import ThreadPoolExecutor


class ContextPreserveThreadPoolExecutor(ThreadPoolExecutor):
    def submit(self, fn, *args, **kwargs):
        context_data = contextvars.copy_context().items()

        @functools.wraps(fn)
        def _wrapper(*args, **kwargs):
            for var, value in context_data:
                if var.name == 'observation_stack_context' or var.name == 'observation_params_context':
                    var.set(value)
            return fn(*args, **kwargs)

        return super().submit(_wrapper, *args, **kwargs)

    def map(self, fn, *iterables, timeout=None, chunksize=1):
        context_data = contextvars.copy_context().items()

        @functools.wraps(fn)
        def _wrapper(*args):
            for var, value in context_data:
                if var.name == 'observation_stack_context' or var.name == 'observation_params_context':
                    var.set(value)
            return fn(*args)

        return super().map(_wrapper, *iterables, timeout=timeout, chunksize=chunksize)
