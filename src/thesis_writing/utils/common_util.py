from typing import List, <PERSON><PERSON>


def _find_all_quotes(text: str) -> List[Tuple[int, str]]:
    quotes = []
    i = 0

    while i < len(text):
        char = text[i]
        # 处理普通引号
        if char in ['“', '”', '‘', '’']:
            quotes.append((i, char))

        i += 1

    return quotes


def replace_quotes(original_text: str, output_text: str) -> str:
    if not original_text or not output_text:
        return output_text

    # 找到原文和输出中的所有引号
    original_quotes = _find_all_quotes(original_text)
    output_quotes = _find_all_quotes(output_text)

    # 如果引号数量相同，按顺序替换
    if original_quotes and output_quotes and len(original_quotes) == len(output_quotes):
        chars = list(output_text)
        for i, (_, original_char) in enumerate(original_quotes):
            out_pos, _ = output_quotes[i]
            chars[out_pos] = original_char

        return ''.join(chars)

    return output_text
