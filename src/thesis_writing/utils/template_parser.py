import re

from jinja2 import Environment, FileSystemLoader

from thesis_writing.utils.resource_util import get_resource_path

continuous_bl: re.Pattern = re.compile(r"\n\n+")
template_dir = get_resource_path("")

env = Environment(loader=FileSystemLoader(template_dir))


def get_template(template_name: str, **kwargs) -> str:
    template = env.get_template(template_name)
    render_result = template.render(kwargs)
    render_result = continuous_bl.sub("\n\n", render_result.strip())
    return render_result
