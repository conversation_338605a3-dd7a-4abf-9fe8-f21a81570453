import base64
import json
import os
import shutil
import subprocess
import zlib
from abc import ABC, abstractmethod

import requests
from dotenv import load_dotenv

from thesis_writing.utils.logger import get_logger

load_dotenv()
logger = get_logger(__name__)


def find_playwright_chromium():
    """
    在文件系统中查找 Playwright 安装的 Chromium 可执行文件。
    优先搜索常见路径，然后回退到全盘搜索。
    """
    # 1. 尝试通过 shutil.which
    found_path = shutil.which("chrome")
    if found_path and 'ms-playwright' in found_path and 'chrome-linux' in found_path:
        # logger.info(f"通过 shutil.which 快速找到 Chromium: {found_path}")
        if os.access(found_path, os.X_OK):
            return found_path
        else:
            logger.warning(f"找到路径 {found_path} 但不可执行，继续搜索...")

    # 2. 定义可能的搜索基础路径 (优化性能)
    #    根据 playwright 的默认行为，通常在root目录的 .cache 下
    possible_bases = [
        os.path.expanduser("~/.cache/ms-playwright"),
        "/root/.cache/ms-playwright",
        "/ms-playwright",
        os.path.expanduser("~/.cache/puppeteer"),
    ]
    # logger.info("开始在文件系统中搜索 Playwright Chromium...")
    for base in possible_bases:
        if not os.path.isdir(base):
            # logger.debug(f"基础路径 {base} 不存在，跳过...")
            continue  # 跳过不存在的基础路径
        # logger.debug(f"正在搜索基础路径: {base}")
        try:
            # 使用 os.walk 递归查找
            for root, dirs, files in os.walk(base, topdown=True):
                if "chrome" in files:
                    potential_path = os.path.join(root, "chrome")
                    # 进行严格检查，确保是文件且可执行
                    if os.path.isfile(potential_path) and os.access(potential_path, os.X_OK):
                        # logger.info(f"找到可执行的 Chromium: {potential_path}")
                        return potential_path
        except OSError as e:
            logger.warning(f"搜索路径 {base} 时出错: {e}, 继续...")
            continue  # 继续下一个基础路径

    # 3. 如果 os.walk 没找到，尝试使用 find 命令 (作为备选方案)
    # logger.warning("os.walk 未找到，尝试调用 'find' 命令...")
    try:
        # 注意：这里的 find 命令需要容器内有 find 工具
        # 使用更精确的路径匹配，-quit 找到第一个就退出
        command = 'find / -path "*/ms-playwright/chromium-*/chrome-linux/chrome" -type f -executable -print -quit 2>/dev/null'
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=False, timeout=20)  # 增加超时
        if result.returncode == 0 and result.stdout:
            found_path = result.stdout.strip()
            if os.path.isfile(found_path) and os.access(found_path, os.X_OK):
                # logger.info(f"通过 'find' 命令找到 Chromium: {found_path}")
                return found_path
            else:
                logger.warning(f"'find' 命令返回路径 {found_path}，但验证失败。")
    except FileNotFoundError:
        logger.error("'find' 命令未找到，无法执行搜索。")
    except subprocess.TimeoutExpired:
        logger.error("'find' 命令执行超时。")
    except Exception as e:
        logger.error(f"执行 'find' 命令时发生意外错误: {e}")
    logger.error("未能找到 Playwright 安装的 Chromium 可执行文件。")
    return None


class DiagramValidationStrategy(ABC):
    @abstractmethod
    def validate(self, code: str, **kwargs) -> dict:
        pass


class PlantUMLValidationStrategy(DiagramValidationStrategy):
    def __init__(self, server_url='http://www.plantuml.com/plantuml/png/'):
        self.server_url = server_url

    def validate(self, code: str, **kwargs) -> dict:
        try:
            # 编码流程：UTF-8编码 → Deflate压缩 → PlantUML Base64编码
            code_bytes = code.encode("utf-8")
            compressed = self._deflate_encode(code_bytes)
            encoded = self._plantuml_base64(compressed)
            url = f"{self.server_url}{encoded}"

            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                return {"status": True, "error_msg": None}
            else:
                return {"status": False, "error_msg": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"status": False, "error_msg": str(e)}

    def _deflate_encode(self, data: bytes) -> bytes:
        """实现PlantUML的Deflate编码"""
        compressor = zlib.compressobj(9, zlib.DEFLATED, -zlib.MAX_WBITS)
        return compressor.compress(data) + compressor.flush()

    def _plantuml_base64(self, data: bytes) -> str:
        """将标准Base64转换为PlantUML字符集"""
        std_chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
        plantuml_chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'
        encoded = base64.b64encode(data).decode('utf-8').rstrip('=')
        return encoded.translate(str.maketrans(std_chars, plantuml_chars))


class MermaidValidationStrategy(DiagramValidationStrategy):

    def __init__(self):
        self.tmp_dir = "/mermaid_tmp"

    def verify_mmdc(self):
        """
        验证 mmdc 命令是否可用
        """
        # 1. 检查 mmdc 是否在 PATH 中
        mmdc_executable = shutil.which("mmdc")
        if not mmdc_executable:
            logger.error("错误：在系统的 PATH 中找不到 'mmdc' 命令。请确保已正确安装 @mermaid-js/mermaid-cli。")
            return False
        # logger.info(f"找到 mmdc 可执行文件: {mmdc_executable}")
        # 2. 尝试运行 mmdc --version (基本可用性检查)
        try:
            # logger.info("正在运行 'mmdc --version'...")
            cmd_version = [mmdc_executable, "--version"]
            result_version = subprocess.run(cmd_version, capture_output=True, text=True, check=True, timeout=15)
            logger.info(f"mmdc --version 成功: {result_version.stdout.strip()}")
            return True
        except FileNotFoundError:
            # 理论上 shutil.which 之后不会发生，但作为防御性编程
            logger.error("错误：尝试执行 mmdc 时报告 '文件未找到'。")
            return False
        except subprocess.CalledProcessError as e:
            logger.error(
                f"错误：'mmdc --version' 执行失败，返回码 {e.returncode}。可能是 Node.js 问题或 mmdc 安装不完整。")
            logger.error(f"mmdc Stderr:\n{e.stderr.strip()}")
            return False
        except subprocess.TimeoutExpired:
            logger.error("错误：'mmdc --version' 执行超时。")
            return False
        except Exception as e:
            logger.error(f"执行 'mmdc --version' 时发生意外错误: {e}")
            return False

    def validate(self, code: str, theme="default", scale=1, output_format="png") -> dict:

        # 检查mermaid-cli依赖的chrome路径是否存在
        chromium_path = find_playwright_chromium()
        if not chromium_path:
            error_msg = "未找到 Playwright 安装的 Chromium 可执行文件，无法验证mermaid语法。"
            logger.warning(error_msg)
            return {
                "status": True,
                "error_msg": error_msg
            }

        # 检查mmdc命令是否可用
        if not self.verify_mmdc():
            error_msg = "mmdc命令不可用，请检查安装。"
            logger.warning(error_msg)
            return {
                "status": True,
                "error_msg": error_msg
            }

        if not os.path.exists(self.tmp_dir):
            os.makedirs(self.tmp_dir)

        if chromium_path:
            os.environ['PUPPETEER_EXECUTABLE_PATH'] = chromium_path
            # logger.info(f"环境变量 PUPPETEER_EXECUTABLE_PATH 已设置为: {chromium_path}")

        input_path = os.path.join(self.tmp_dir, "input.mmd")
        with open(input_path, "w", encoding="utf-8") as f:
            f.write(code)

        config_file = os.path.join(self.tmp_dir, "config.json")
        if not os.path.exists(config_file):
            with open(config_file, "w") as f:
                json.dump({"args": ["--no-sandbox"]}, f)

        output_path = os.path.join(self.tmp_dir, "output.png")

        cmd = [
            "mmdc",
            "-i", input_path,
            "-o", output_path,
            "-p", config_file
        ]
        # logger.info(f"准备执行命令: {' '.join(cmd)}")
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=False, timeout=60)
            if result.returncode == 0:
                logger.info("mermaid语法验证成功")
                return {"status": True, "error_msg": None}
            else:
                logger.error(f"mermaid语法验证失败，验证内容为:{code}")
                return {"status": False,
                        "error_msg": f"Exit code {result.returncode}\nSTDOUT: {result.stdout}\nSTDERR: {result.stderr}"}
        except subprocess.CalledProcessError as e:
            return {
                "status": False,
                "error_msg": (
                    f"Exit code {e.returncode}\n"
                    f"STDOUT: {e.stdout}\nSTDERR: {e.stderr}"
                )
            }
        finally:
            self.delete_tmp_file(input_path)
            self.delete_tmp_file(output_path)
            # self.delete_tmp_file(config_file)

    def delete_tmp_file(self, file_path):
        if os.path.exists(file_path):
            os.remove(file_path)


class DiagramCodeCheck:
    def __init__(self):
        self.strategies = {
            "plantuml": PlantUMLValidationStrategy(),
            "mermaid": MermaidValidationStrategy()
        }

    def validate(self, diagram_type: str, code: str, **kwargs) -> dict:
        """统一入口，根据类型选择策略"""
        strategy = self.strategies.get(diagram_type)
        if not strategy:
            return {"status": False, "error_msg": "Unsupported diagram type"}
        return strategy.validate(code, **kwargs)
