import re
import xml.etree.ElementTree as ET
from thesis_writing.utils.logger import get_logger
from langchain_core.output_parsers import BaseOutputParser

logger = get_logger(__name__)

class BadXMLRepair(BaseOutputParser):

    def repair_xml(self, xml_str: str, target_tags=None, special_chars=None) -> str:
        """
        精准转义指定 XML 标签内的文本内容
        保持 XML 结构不变，仅处理目标标签的文本部分
        当thought标签或者diagram标签中的文本内容出现'<>’时，XMLOutputParser会解析失败，需要先对文本内容进行转义
        target_tag是解析后的XML节点标签
        special_symbols：是否需要对节点下的文本内容做进一步优化，例如mermaid流程图代码中不能有()等符号，否则无法渲染
        """
        # 定义匹配目标标签内容的正则模式（支持多行内容）
        if target_tags is None:
            target_tags = ["thought", "diagram"]
        pattern = r"(<({tag})>)(.*?)(</\2>)".format(tag="|".join(target_tags))
        regex = re.compile(pattern, re.DOTALL)

        def _escape_match(match):
            open_tag = match.group(1)  # 例：<thought>
            content = match.group(3)  # 标签内的文本内容
            if special_chars:
                content = self.remove_special_chars(content, special_chars=special_chars)
            close_tag = match.group(4)  # 例：</thought>

            # 分步转义关键字符（优先处理 &）
            escaped = content.replace("&", "&amp;")
            escaped = escaped.replace("<", "&lt;").replace(">", "&gt;")
            return f"{open_tag}{escaped}{close_tag}"

        # 替换所有匹配的目标标签内容
        return regex.sub(_escape_match, xml_str)

    def remove_special_chars(self, text, special_chars):
        # 创建一个翻译表，将特殊字符映射为 None（即删除）
        translation_table = str.maketrans('', '', ''.join(special_chars))
        # 使用 translate 方法删除特殊字符
        cleaned_text = text.translate(translation_table)
        return cleaned_text

    def parse(self, text: str) -> str:
        text = self.repair_xml(text)

        return text


class BadFlowChartXMLRepair(BadXMLRepair):
    def parse(self, text: str) -> str:
        text = self.repair_xml(text, special_chars=['(', ')', '\"'])
        text = self.refine(text)
        return text

    def refine(self, text):
        """
        美化流程图，在最前面添加渲染配置样式。
        :param text: 原始 XML 字符串
        :return: 修改后的 XML 字符串（若解析失败则返回原字符串）
        """
        style = '%%{init: {"flowchart": {"defaultRenderer": "elk"}} }%%'
        try:
            # 解析原始 XML 字符串
            root = ET.fromstring(text)

            # 定位 diagram 元素并检查其文本内容
            diagram_element = root.find('diagram')
            if diagram_element is not None and diagram_element.text:
                # 在原有内容前添加样式配置
                modified_content = f"{style} \n {diagram_element.text}"
                diagram_element.text = modified_content

            # 将修改后的 XML 根节点转为字符串返回
            return ET.tostring(root, encoding='unicode')

        except ET.ParseError as e:
            logger.error(f"XML 解析失败：{e}")
            # 解析失败时返回原始文本（根据需求可修改为其他处理方式）
            return text


class BadSequenceXMLRepair(BadXMLRepair):
    def parse(self, text: str):
        text = self.repair_xml(text)
        # 匹配以任意空格开头，后跟===任意内容===的行
        pattern = r'^\s*===.+===\s*$'

        # 按行处理并过滤
        cleaned_lines = [
            line for line in text.split('\n')
            if not re.match(pattern, line)
        ]

        return '\n'.join(cleaned_lines)


class BadQuantumChartXMLRepair(BadXMLRepair):
    """
    象限图XML修复，否则mermaid渲染失败。
    规则：除title字段外，所有的中文字符都用双引号包裹
    示例：原象限图代码：
    quadrantChart
        title Test项目!123
        x-axis 低风险A1 --> 高风险B2
        y-axis 小影响(1级) --> 大影响(2级)
        quadrant-1 重大!风险
        quadrant-2 较大-风险
        测试123: [0.5, 0.6]
        "已有引号": [0.7, 0.8]
    修复后的象限图代码:
    quadrantChart
        title Test项目!123
        x-axis "低风险A1" --> "高风险B2"
        y-axis "小影响(1级)" --> "大影响(2级)"
        quadrant-1 "重大!风险"
        quadrant-2 "较大-风险"
        "测试123": [0.5, 0.6]
        "已有引号": [0.7, 0.8]
    """

    def parse(self, text: str) -> str:
        text = self.repair_xml(text)
        lines = text.split('\n')
        processed_lines = []

        for line in lines:
            stripped_line = line.strip()
            if not stripped_line:
                processed_lines.append(line)
                continue

            # 保留title行原样
            if stripped_line.startswith('title'):
                processed_lines.append(line)
                continue

            # 处理x-axis和y-axis行（支持带特殊符号的中文）
            if re.match(r'^(x-axis|y-axis)\b', stripped_line):
                match = re.match(r'^(x-axis|y-axis)(\s*)(.*?)(\s*-->\s*)(.*?)$', line)
                if match:
                    keyword, spaces_before, left_part, arrow_part, right_part = match.groups()
                    # 处理左边部分（允许包含非中文）
                    left_processed = self.process_mixed_string(left_part)
                    # 处理右边部分
                    right_processed = self.process_mixed_string(right_part)
                    new_line = f"{keyword}{spaces_before}{left_processed}{arrow_part}{right_processed}"
                    processed_lines.append(new_line)
                else:
                    processed_lines.append(line)
                continue

            # 处理quadrant-行（支持带符号的中文）
            if re.match(r'^quadrant-\d+', stripped_line):
                match = re.match(r'^(quadrant-\d+)(\s*)(.*?)$', line)
                if match:
                    keyword, spaces, content = match.groups()
                    content_processed = self.process_mixed_string(content)
                    new_line = f"{keyword}{spaces}{content_processed}"
                    processed_lines.append(new_line)
                else:
                    processed_lines.append(line)
                continue

            # 处理风险条目行（支持特殊符号）
            if re.search(r':\s*\[.*\]\s*$', stripped_line):
                match = re.match(r'^(\s*)(["\']?)(.*?)(["\']?)(\s*:\s*\[.*\])\s*$', line)
                if match:
                    leading_spaces, open_quote, name, close_quote, rest = match.groups()
                    if open_quote and close_quote and open_quote == close_quote:
                        processed_lines.append(line)
                        continue
                    name_processed = self.process_mixed_string(name)
                    new_line = f"{leading_spaces}{name_processed}{rest}"
                    processed_lines.append(new_line)
                else:
                    processed_lines.append(line)
                continue

            processed_lines.append(line)

        return '\n'.join(processed_lines)

    def process_mixed_string(self, s):
        """处理包含中文的混合字符串"""
        stripped = s.strip()
        if not stripped:
            return s

        # 判断是否已包含引号
        if self.quoted(stripped):
            return stripped

        # 只要包含中文就处理（允许混合其他字符）
        if self.contains_chinese(stripped):
            return f'"{stripped}"'
        else:
            return s

    def contains_chinese(self, s):
        """检查是否包含至少一个中文字符"""
        return re.search(r'[\u4e00-\u9fa5]', s) is not None

    def quoted(self, s):
        """检查是否被完整引号包裹"""
        return (len(s) >= 2 and
                ((s[0] == '"' and s[-1] == '"') or
                 (s[0] == "'" and s[-1] == "'")))

