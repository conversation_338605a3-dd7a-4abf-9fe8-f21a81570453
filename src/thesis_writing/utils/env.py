import os
from enum import Enum


class EnvValue(Enum):
    LOCAL: str = "local"
    DEV: str = "dev"
    PROD: str = "prod"


class Env:

    @property
    def env(self) -> str:
        return os.getenv("TG_ENV", EnvValue.LOCAL.value).lower()

    def is_prod(self) -> bool:
        return self.env == EnvValue.PROD.value

    def is_dev(self) -> bool:
        return self.env == EnvValue.DEV.value

    def is_local(self) -> bool:
        return self.env == EnvValue.LOCAL.value

    def __str__(self):
        return self.env


ENV = Env()
