import json
from typing import Union

from urllib.request import Request, urlopen


def post(url: str, body: dict, timeout: int = 3) -> Union[dict, list]:
    data_json = json.dumps(body, ensure_ascii=False).encode('utf-8')
    request = Request(url, data=data_json, headers={'Content-Type': 'application/json'}, method='POST')
    with urlopen(request, timeout=timeout) as response:
        assert response.status == 200, response.reason
        response_body = response.read().decode('utf-8')
        response_dict = json.loads(response_body)
        return response_dict


def get(url: str, params: dict, timeout: int = 3) -> Union[dict, list]:
    query_string = "&".join([f"{k}={v}" for k, v in params.items()])
    url = f"{url}?{query_string}"
    request = Request(url, headers={'Content-Type': 'application/json'}, method='GET')
    with urlopen(request, timeout=timeout) as response:
        assert response.status == 200, response.reason
        response_body = response.read().decode('utf-8')
        response_dict = json.loads(response_body)
        return response_dict
