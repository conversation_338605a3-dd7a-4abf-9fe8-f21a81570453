from typing import List

import pandas as pd

from thesis_writing.utils.resource_util import get_resource_path


def init_major_alias_dict():
    major_df = pd.read_csv(get_resource_path("major_alias.csv"), encoding="utf-8")
    major_alias_dict = {}
    for index, row in major_df.iterrows():
        if row.aliases and isinstance(row.aliases, str):
            major_alias_dict[row.major] = row.aliases.split(";")
    return major_alias_dict


class MajorAliasUtil:
    major_alias_dict = init_major_alias_dict()

    @classmethod
    def get_major_alias(cls, major: str) -> List[str]:
        return cls.major_alias_dict.get(major, [major])
