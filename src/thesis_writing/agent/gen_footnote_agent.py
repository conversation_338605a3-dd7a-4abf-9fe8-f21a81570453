import json
from typing import List, Dict, Optional, Union

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput
from thesis_writing.entity.enums import SearchEngine
from thesis_writing.retriever.index.book_chunk import BookChunk
from thesis_writing.retriever.index.thesis_chunk import ThesisChunk
from thesis_writing.retriever.index.user_feed_chunk import UserFeedChunk
from thesis_writing.retriever.index.web_chunk import WebChunk


class FootNoteChapter(BaseModel):
    node_id: str = Field(..., description="章节编号")
    title: Optional[str] = Field(None, description="章节标题")
    materials: Optional[Dict[str, List[Union[WebChunk, BookChunk, ThesisChunk, UserFeedChunk]]]] = Field(
        default_factory=dict,
        description="相关材料")
    segment_content: str = Field(..., description="本章内容")


class GenerateFootNoteAgentInput(BaseAgentInput):
    subject: str = Field(..., description="论文标题", min_length=1)
    major: str = Field(..., description="专业")
    chapters: List[FootNoteChapter] = Field(default_factory=list, description="章节内容与章节材料")
    chapter_str: Optional[str] = Field(None, description="去除bing搜索结果，由chapters序列化而来")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_footnote.jinja"
        self.get_chapter_str()

    def get_chapter_str(self):
        for chapter_node in self.chapters:
            web_materials = chapter_node.materials.get("web", [])
            cleaned_web_materials = []
            for web_material in web_materials:
                if web_material.search_engine.capitalize() == SearchEngine.Bing and web_material.url:
                    cleaned_web_materials.append(web_material)

            chapter_node.materials["web"] = cleaned_web_materials

        self.chapter_str = json.dumps(
            [chapter.model_dump(mode='json') for chapter in self.chapters],
            ensure_ascii=False,
            indent=2
        )


class GenerateFootNoteAgentResponse(BaseAgentResponse):
    thought: str = Field(..., description="思考过程")
    chosen_nodes: List[str] = Field(default_factory=list, max_length=3)
    results: List[FootNoteChapter] = Field(default_factory=list, description="结果")


class GenerateFootNoteAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_footnote.jinja"
        self.input_type = GenerateFootNoteAgentInput
        self.output_type = GenerateFootNoteAgentResponse
        super().__init__(options, failover_options_list)

    def validate_output(self, input: GenerateFootNoteAgentInput, output: GenerateFootNoteAgentResponse, remaining_retries=0):
        chosen_nodes = output.chosen_nodes
        results = output.results
        # 1. 校验 chosen_nodes 与 results 长度必须一致
        if len(chosen_nodes) != len(results):
            raise ValueError(
                f"Validation failed: chosen_nodes length ({len(chosen_nodes)}) "
                f"must equal results length ({len(results)})."
            )

        # 2. 若无需脚注，必须双空
        if len(chosen_nodes) == 0:
            if len(results) != 0:
                raise ValueError("Validation failed: chosen_nodes is empty but results is not.")
            # 合法零脚注场景，直接通过
            return

        # 3. chosen_nodes不重复
        if len(chosen_nodes) != len(set(chosen_nodes)):
            duplicated_nodes = [node_id for node_id in chosen_nodes if node_id not in set(chosen_nodes)]
            raise ValueError(f"Validation failed: chosen_nodes contains duplicates."
                             f"duplicated_nodes are: {duplicated_nodes}")

        # 4. 校验 chosen_nodes 与 results.node_id 一一对应、顺序一致
        for i, (node_id, result_chapter) in enumerate(zip(chosen_nodes, results)):
            if result_chapter.node_id != node_id:
                raise ValueError(
                    f"Validation failed at index {i}: "
                    f"chosen_nodes[{i}]='{node_id}' != results[{i}].node_id='{result_chapter.node_id}'."
                )

        # 5. 校验所有 chosen_nodes 中的 node_id 必须存在于原始输入章节列表中
        input_node_ids = {chapter.node_id for chapter in input.chapters}
        for node_id in chosen_nodes:
            if node_id not in input_node_ids:
                raise ValueError(
                    f"Validation failed: Node ID '{node_id}' in chosen_nodes not found in input chapters."
                )

        # 6. 校验输出的正文内容是否与输入一致
        for i, (node_id, result_chapter) in enumerate(zip(chosen_nodes, results)):
            output_segment = result_chapter.segment_content
            origin_chapter = [chapter for chapter in input.chapters if chapter.node_id == node_id][0]
            origin_segment = origin_chapter.segment_content
            # 此处应该是提取output_segment中出去<footnote>标签的内容进行比较，暂时先用length判断
            if len(output_segment) < len(origin_segment):
                raise ValueError(
                    f"Validation failed: Node ID '{node_id}'for output segment content does not match origin segment content"
                    f"output segment content: {output_segment}"
                    f"origin segment content: {origin_segment}"
                )