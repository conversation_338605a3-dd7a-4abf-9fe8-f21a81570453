import json
from typing import List, Dict, Optional, Union

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput
from thesis_writing.entity.enums import SearchEngine
from thesis_writing.retriever.index.book_chunk import BookChunk
from thesis_writing.retriever.index.thesis_chunk import ThesisChunk
from thesis_writing.retriever.index.user_feed_chunk import UserFeedChunk
from thesis_writing.retriever.index.web_chunk import WebChunk


class FootNoteChapter(BaseModel):
    node_id: str = Field(..., description="章节编号")
    title: Optional[str] = Field(None, description="章节标题")
    materials: Optional[Dict[str, List[Union[WebChunk, BookChunk, ThesisChunk, UserFeedChunk]]]] = Field(
        default_factory=dict,
        description="相关材料")
    segment_content: str = Field(..., description="本章内容")


class GenerateFootNoteAgentInput(BaseAgentInput):
    subject: str = Field(..., description="论文标题", min_length=1)
    major: str = Field(..., description="专业")
    chapters: List[FootNoteChapter] = Field(default_factory=list, description="章节内容与章节材料")
    chapter_str: Optional[str] = Field(None, description="去除bing搜索结果，由chapters序列化而来")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_footnote.jinja"
        self.get_chapter_str()

    def get_chapter_str(self):
        for chapter_node in self.chapters:
            web_materials = chapter_node.materials.get("web", [])
            cleaned_web_materials = []
            for web_material in web_materials:
                if web_material.search_engine.capitalize() == SearchEngine.Bing and web_material.url:
                    cleaned_web_materials.append(web_material.to_dict())

            cleaned_book_materials = [book_material.to_dict() for book_material in chapter_node.materials.get("book", [])]

            chapter_node.materials["web"] = cleaned_web_materials
            chapter_node.materials["book"] = cleaned_book_materials

        self.chapter_str = json.dumps(
            [chapter.model_dump(mode='json') for chapter in self.chapters],
            ensure_ascii=False,
            indent=2
        )


class GenerateFootNoteAgentResponse(BaseAgentResponse):
    thought: str = Field(..., description="思考过程")
    chosen_nodes: List[str] = Field(default_factory=list, max_length=3)
    results: List[FootNoteChapter] = Field(default_factory=list, description="结果")


class GenerateFootNoteAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_footnote.jinja"
        self.input_type = GenerateFootNoteAgentInput
        self.output_type = GenerateFootNoteAgentResponse
        super().__init__(options, failover_options_list)

    def validate_output(self, input: GenerateFootNoteAgentInput, output: GenerateFootNoteAgentResponse, remaining_retries=0):
        chosen_nodes = output.chosen_nodes
        results = output.results
        # 1. 校验 chosen_nodes 与 results 长度必须一致
        if len(chosen_nodes) != len(results):
            raise ValueError(
                f"Validation failed: chosen_nodes length ({len(chosen_nodes)}) "
                f"must equal results length ({len(results)})."
            )

        # 2. 若无需脚注，必须双空
        if len(chosen_nodes) == 0:
            if len(results) != 0:
                raise ValueError("Validation failed: chosen_nodes is empty but results is not.")
            # 合法零脚注场景，直接通过
            return

        # 3. chosen_nodes不重复
        if len(chosen_nodes) != len(set(chosen_nodes)):
            duplicated_nodes = [node_id for node_id in chosen_nodes if node_id not in set(chosen_nodes)]
            raise ValueError(f"Validation failed: chosen_nodes contains duplicates."
                             f"duplicated_nodes are: {duplicated_nodes}")

        # 4. 校验 chosen_nodes 与 results.node_id 一一对应、顺序一致
        for i, (node_id, result_chapter) in enumerate(zip(chosen_nodes, results)):
            if result_chapter.node_id != node_id:
                raise ValueError(
                    f"Validation failed at index {i}: "
                    f"chosen_nodes[{i}]='{node_id}' != results[{i}].node_id='{result_chapter.node_id}'."
                )

        # 5. 校验所有 chosen_nodes 中的 node_id 必须存在于原始输入章节列表中
        input_node_ids = {chapter.node_id for chapter in input.chapters}
        for node_id in chosen_nodes:
            if node_id not in input_node_ids:
                raise ValueError(
                    f"Validation failed: Node ID '{node_id}' in chosen_nodes not found in input chapters."
                )

        # 6. 校验输出的正文内容是否与输入一致
        for i, (node_id, result_chapter) in enumerate(zip(chosen_nodes, results)):
            output_segment = result_chapter.segment_content
            origin_chapter = [chapter for chapter in input.chapters if chapter.node_id == node_id][0]
            origin_segment = origin_chapter.segment_content
            # 此处应该是提取output_segment中出去<footnote>标签的内容进行比较，暂时先用length判断
            if len(output_segment) < len(origin_segment):
                raise ValueError(
                    f"Validation failed: Node ID '{node_id}'for output segment content does not match origin segment content"
                    f"output segment content: {output_segment}"
                    f"origin segment content: {origin_segment}"
                )

if __name__ == '__main__':
    book1 = {
          "chunk_id": "f49b4015-9d25-4a4a-a7a4-1ea868b20c47",
          "temp_id": None,
          "content": "(1) 读者与图书的关系类型。读者与图书之间建立了借还关系，需求分析的结果显示，“每名读者最多可以借阅10本图书”，因此一本图书可以被多个读者借阅，同时每位读者也能借阅多本图书，所以读者与图书之间的借还关系为多对多（n：m），这里空（1）和空（2）应分别填写n和m。\\n\\n(2) 书目与图书之间的关系类型。图书馆针对同一书目的图书可以有多个副本，每本书在系统中都有唯一的图书ID，因此书目与图书之间的关系类型为一对多（1：n），因此空（3）和空（4）应分别填写1和m。\\n\\n(3) 书目与读者之间的关系类型。当某书目的可借图书数量为0时，读者能够进行预约登记；鉴于一名读者可以借阅多种图书，所以书目与读者之间的预约关系类型为多对多（n：m），这里空（5）和空（6）应填写n和m。\\n\\n2) 图书馆管理E-R模型\\n依据需求分析的结果，图书馆管理应包括图书证的注册、注销和挂失管理，因此在图12-13中，管理员和读者实体之间缺少的联系为注册、注销及挂失借书证的管理关系。补充这些缺失的联系后，E-R模型如图12-14所示。\\n{{{{{this is a image}}}}}\\n\\n3) 图书管理逻辑结构设计\\n根据得到的E-R图概念设计，转换为图书管理系统的主要关系模式如下，请完善“借还记录”和“预约登记”关系中的缺失部分。注意：时间格式为“年.月.日 时：分：秒”，请标明读者和书目关系模式的主键，以及图书、借还记录与预约登记关系模式的主键和外键。\\n管理员（工号，姓名，权限）\\n读者（姓名，年龄，工作单位，借书证号，电话，E-mail）\\n书目（ISBN号，书名，作者，出版商，出版年月，册数，工号）\\n图书（图书ID，ISBN号，存放位置，状态，工号）\\n借还记录（（a），借出时间，应还时间，归还时间）\\n预约登记（（b），预约时间，预约期限，图书ID）\\n借书证管理（借书证号，使用状态，开始时间，结束时间，工号）\\n\\n1) 问题分析\\n在读者借书时，图书管理员需登记借书证号、所借图书ID、借出时间和应还时间；在归还书籍时，图书管理员会在相应的借书信息中记录归还时间，因此借还记录关系中的空（a）应填写“借书证号，图书ID”。\\n在读者进行预约登记时，需记录借书证号、所需借阅图书的ISBN号和预约时间等。目前的预约登记关系中已经包含了预约时间、预约期限和图书ID信息，但还需记录预约的读者和书的ISBN号，因此预约登记关系模式中的空（b）应填写“借书证号，ISBN号”。",
          "refine_content": None,
          "keywords": "读者；图书；书目；多对多；一对多；E-R模型；借还记录；预约登记",
          "keywords_embedding": None,
          "summary": "段落描述了图书馆管理系统中读者、图书和书目之间的多对多和一对多关系，以及借还记录和预约登记关系模式的完善。",
          "summary_embedding": None,
          "embedding": None,
          "tags": [],
          "book_id": "22d81563-c0ea-4ee7-a2ed-279b1ee33bfb",
          "publish_date": "2018-11-26",
          "genre": "",
          "language": "zh",
          "subject": "",
          "book_name": "软件设计师教程",
          "chunk_hierarchy_path": "软件设计师教程\n第12章软件系统分析与设计\n12.2数据库分析与设计\n12.2.7案例分析"
        }
    book2 = {
          "chunk_id": "d81436fd-118d-4f00-908c-3599bd73fb8a",
          "temp_id": None,
          "content": "从2.4.1节的示例可以看出，在获取用例的过程中，可能会存在三个不同层次的用例，这些用例的范围各不相同。\\n● 概述级用例的范围涵盖整个企业。\\n● 用户目标级用例的范围限定在系统的边界内。\\n● 子功能级用例的范围则聚焦于某个子系统或组件。\\n在2.4.1节中提到的图书馆图书管理系统是由图书管理员使用的，而读者则通过图书管理员进行借书和还书的操作，如图2.19所示。\\n{{{{{this is a image}}}}}\\n图2.19 用例范围\\n在完整的图书馆系统中，系统包含图书管理系统以及图书管理员，读者则作为系统的外部使用者。在这种情况下，系统的范围是整个图书馆。\\n而在图书管理系统中，图书管理员则成为其外部使用者。\\n以上示例表明，用例的范围会影响系统的边界。用例范围的不同意味着系统的边界也有所不同，因此外部执行者可能也会随之改变。例如，如果用例范围被设定为整个图书馆（即企业范围），则外部执行者为读者；相反，如果用例范围局限在图书管理系统（即软件系统），那么相应的外部执行者则是图书管理员。",
          "refine_content": None,
          "keywords": "用例范围；概述级用例；用户目标级用例；子功能级用例；外部执行者",
          "keywords_embedding": None,
          "summary": "用例有不同的层次和范围，包括概述级、用户目标级和子功能级。用例范围影响系统的边界和外部执行者。例如，图书馆系统的范围可以是整个图书馆或仅限于图书管理系统，这决定了外部执行者是读者还是图书管理员。",
          "summary_embedding": None,
          "embedding": None,
          "tags": [],
          "book_id": "712b5ea1-29d7-4ec1-8f2b-e461bf5dac75",
          "publish_date": "2006-06-01",
          "genre": "",
          "language": "zh",
          "subject": "",
          "book_name": "UML基础与应用",
          "chunk_hierarchy_path": "UML基础与应用\n第2章 用例图\n2.4 用例的粒度和范围\n2.4.2 用例的范围"
        }

    books = [BookChunk(**book1), BookChunk(**book2)]
    foot = FootNoteChapter(
        node_id="2.1",
        title="2.1用户需求与角色分析",
        materials={"book": books},
        segment_content=""
    )
    foot_input = GenerateFootNoteAgentInput(
        subject="图书管理系统UML设计",
        major="软件工程",
        chapters=[foot]
    )
    print(foot_input.chapter_str)