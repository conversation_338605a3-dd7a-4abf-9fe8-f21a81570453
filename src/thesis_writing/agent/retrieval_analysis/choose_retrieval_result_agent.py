from datetime import datetime
from typing import Optional, List, Tuple

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgentResponse, BaseAgent
from thesis_writing.retriever.index.base import RerankDocument


class ChooseRetrievalResultAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    summary: Optional[str] = Field(None, description="全文概述")
    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    segment_writing_plan: str = Field(..., description="当前目标小节写作计划", min_length=1)
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    retrieve_results: List[Tuple[str, List[RerankDocument]]] = Field(None, description="检索结果")

    materials_str: str = Field(None, description="召回的资料")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/choose_retrieval_result.jinja"

        self.materials_str = self._format_retrieval_result(self.retrieve_results)

    @staticmethod
    def _format_retrieval_result(retrieval_materials: List[Tuple[str, List[RerankDocument]]]) -> str:
        lines = []
        for query, materials in retrieval_materials:
            if len(materials) == 0:
                continue
            lines.append(f"### {query}")
            for material in materials:
                content = material.refine_content if material.refine_content else material.content
                lines.append(f"<chunk id=\"{material.temp_id}\">\n{content}\n</chunk>")
        return "\n".join(lines)


class ChooseRetrievalResultAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="思路")
    result: List[str] = Field(None, description="提纯后的结果")


class ChooseRetrievalResultAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/choose_retrieval_result.jinja"
        self.input_type = ChooseRetrievalResultAgentInput
        self.output_type = ChooseRetrievalResultAgentResponse
        super().__init__(options, failover_options_list)
