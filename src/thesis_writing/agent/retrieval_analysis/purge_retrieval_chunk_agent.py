from typing import List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgentResponse, BaseAgent


class PurgeRetrievalChunkAgentInput(BaseAgentInput):
    query: str = Field(..., description="查询语句")
    origin_content: str = Field(None, description="对应query的搜索引擎检索结果")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/purge_retrieval_chunk.jinja"


class PurgeRetrievalChunkAgentResponse(BaseAgentResponse):
    thought: str = Field(None, description="思路")
    result: str = Field(None, description="提纯后的结果")


class PurgeRetrievalChunkAgent(BaseAgent):
    def __init__(self, options, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/purge_retrieval_chunk.jinja"
        self.input_type = PurgeRetrievalChunkAgentInput
        self.output_type = PurgeRetrievalChunkAgentResponse
        super().__init__(options, failover_options_list)
