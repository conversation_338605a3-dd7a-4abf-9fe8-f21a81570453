from typing import List, Optional

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgentResponse, BaseAgent


class SummarizeTextAgentInput(BaseAgentInput):
    origin_content: str = Field(None, description="文本")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/summarize_text.jinja"


class SummarizeTextAgentResponse(BaseAgentResponse):
    thought: str = Field(None, description="思路")
    title: str = Field(..., description="标题", min_length=1)
    summary: str = Field(..., description="总结后的结果", min_length=1)


class SummarizeTextAgent(BaseAgent):
    def __init__(self, options, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/summarize_text.jinja"
        self.input_type = SummarizeTextAgentInput
        self.output_type = SummarizeTextAgentResponse
        super().__init__(options, failover_options_list)
