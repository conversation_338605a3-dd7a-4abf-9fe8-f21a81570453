from typing import List, Optional

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgentResponse, BaseAgent


class SummarizeChunkAgentInput(BaseAgentInput):
    chunk_content: str = Field(None, description="文本块内容")
    chunk_source: str = Field(None, description="文本块来源")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/summarize_chunk.jinja"


class SummarizeChunkAgentResponse(BaseAgentResponse):
    keywords: str = Field(None, description="关键词")
    summary: str = Field(..., description="总结后的结果", min_length=1)


class SummarizeChunkAgent(BaseAgent):
    def __init__(self, options, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/summarize_chunk.jinja"
        self.input_type = SummarizeChunkAgentInput
        self.output_type = SummarizeChunkAgentResponse
        super().__init__(options, failover_options_list)
