from typing import Optional, List

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput
from thesis_writing.retriever.retrieve_service import RetrievalSourceType


class RewriteQueryAgentInput(BaseAgentInput):
    queries: list[str] = Field(None, description="查询语句列表")
    to_sources: List[RetrievalSourceType] = Field(None, description="目标查询来源")
    queries_desc: str = Field(None, description="查询语句描述")
    to_sources_desc: str = Field(None, description="目标查询来源描述")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/rewrite_query.jinja"
        self.queries_desc = "\n".join(self.queries)
        self.to_sources_desc = ", ".join(self.to_sources)


class RewrittenQueryItem(BaseModel):
    source: str = Field(..., description="来源")
    original_query: str = Field(None, description="原始查询语句")
    rewritten_query: str = Field(..., description="重写后的查询语句")
    reason: Optional[str] = Field(None, description="思路")


class RewriteQueryAgentResponse(BaseAgentResponse):
    queries: List[RewrittenQueryItem] = Field(None, description="查询语句列表")


class RewriteQueryAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/rewrite_query.jinja"
        self.input_type = RewriteQueryAgentInput
        self.output_type = RewriteQueryAgentResponse
        super().__init__(options, failover_options_list)
