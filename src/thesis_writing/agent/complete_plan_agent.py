from datetime import datetime
from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentInput, BaseAgentResponse
from thesis_writing.agent.gen_plan_agent import GeneratePlanNode
from thesis_writing.entity.enums import WritingPlanContentType
from thesis_writing.utils.plan_util import fix_title_numbering, check_sections

class CompletePlanAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    summary: str = Field(..., description="全文概述", min_length=1)
    writing_plan: str = Field(..., description="写作计划", min_length=1)
    word_count: str = Field("20000", description="全文字数", min_length=1)
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    introduction_title: Optional[str] = Field(None, title="指定绪论标题")
    introduction_has_subsections: Optional[bool] = Field(None, title="绪论是否分小节")
    introduction_title_has_number: Optional[bool] = Field(None, title="绪论标题是否加序号")
    conclusion_title: Optional[str] = Field(None, title="指定结论标题")
    conclusion_has_subsections: Optional[bool] = Field(None, title="结论是否分小节")
    conclusion_title_has_number: Optional[bool] = Field(None, title="结论标题是否加序号")
    h1_numbering_rule: Optional[str] = Field(None, title="一级标题编号规则")
    h2_numbering_rule: Optional[str] = Field(None, title="二级标题编号规则")
    h3_numbering_rule: Optional[str] = Field(None, title="三级标题编号规则")
    main_content_start_number: Optional[int] = Field(None, title="正文部分一级标题起始序号")

    remarks: Optional[str] = Field(None, description="备注信息")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/complete_plan.jinja"
        if self.remarks is None:
            self.remarks = self.gen_remarks()

    def gen_remarks(self):
        remarks = ""
        if self.introduction_has_subsections is not None:
            if self.introduction_has_subsections:
                remarks += "绪论部分必须分小节\n"
            else:
                remarks += "绪论部分禁止包含二级节点\n"

        if self.conclusion_has_subsections is not None:
            if self.conclusion_has_subsections:
                remarks += "结论部分必须生成二级节点，但禁止生成三级节点\n"
            else:
                remarks += "结论部分禁止包含二级节点\n"

        if self.introduction_title:
            remarks += f"绪论部分一级节点标题必须是“{self.introduction_title}”\n"

        if self.conclusion_title:
            remarks += f"结论部分一级节点标题必须是“{self.conclusion_title}”\n"
        return remarks if len(remarks) > 0 else "无"

class CompletePlanNode(BaseAgentResponse):
    title: str = Field(..., description="标题", min_length=1)
    content_type: Optional[str] = Field(None, description="章类型")
    description: Optional[str] = Field(None, description="写作计划")
    writing_length: Optional[str | int] = Field(None, description="篇幅要求")

    children: Optional[List["CompletePlanNode"]] = Field(None, description="子节点")

    def to_gen_plan_output(self) -> GeneratePlanNode:
        plan_node = GeneratePlanNode(
            title=self.title,
            description=self.description,
            content_type=self.content_type,
            writing_length=self.writing_length,
            children=[item.to_gen_plan_output() for item in self.children or []]
        )
        return plan_node


class CompletePlanAgentResponse(BaseAgentResponse):
    complete_plan_nodes: List[CompletePlanNode] = Field(None, description="写作节点")


    def get_introduction_writing_plan(self) -> CompletePlanNode:
        for plan_node in self.complete_plan_nodes or []:
            if plan_node.content_type == WritingPlanContentType.Introduction.value:
                return plan_node

    def get_conclusion_writing_plan(self) -> CompletePlanNode:
        for plan_node in self.complete_plan_nodes or []:
            if plan_node.content_type == WritingPlanContentType.Conclusion.value:
                return plan_node


class CompletePlanAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/complete_plan.jinja"
        self.input_type = CompletePlanAgentInput
        self.output_type = CompletePlanAgentResponse
        super().__init__(options, failover_options_list)

    def validate_output(self, input: CompletePlanAgentInput, output: CompletePlanAgentResponse, remaining_retries=0):
        if not output or not output.complete_plan_nodes:
            raise ValueError("Writing plan nodes are empty or None")
        if input.introduction_title:
            output.get_introduction_writing_plan().title = input.introduction_title
        if input.conclusion_title:
            output.get_conclusion_writing_plan().title = input.conclusion_title
        fix_title_numbering(input, output.complete_plan_nodes)
        check_sections(input, output)

