from datetime import datetime
from typing import Optional, List

from pydantic import Field, RootModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput, ChainRunnableConfig
from thesis_writing.utils.logger import get_logger

logger = get_logger(__name__)


class GenerateAppendixItemAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    introduction: Optional[str] = Field(None, description="绪论")
    main_content: Optional[str] = Field(None, description="正文")
    references: Optional[str] = Field(None, description="参考文献")
    user_feed_summaries: Optional[list[str]] = Field(None, description="资料")
    user_feed_summaries_str: Optional[str] = Field(None, description="资料")
    appendix_info: Optional[str] = Field(None, description="附录信息")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        if self.user_feed_summaries:
            text = ""
            for n, user_feed_summary in enumerate(self.user_feed_summaries, 1):
                text += f"{n}. {user_feed_summary}\n\n"
            self.user_feed_summaries_str = text
        self.user_message_template_path = "user_prompt/gen_appendix_item.jinja"


class GenerateAppendixItemAgentResponse(RootModel[str], BaseAgentResponse):
    def to_llm_response(self):
        return self.root


class GenerateAppendixItemAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_appendix_item.jinja"
        self.input_type = GenerateAppendixItemAgentInput
        self.output_type = GenerateAppendixItemAgentResponse
        self.output_parser = 'str'
        super().__init__(options, failover_options_list)
