from datetime import datetime
from typing import Optional, List

from pydantic import Field, RootModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput, ChainRunnableConfig
from thesis_writing.utils.logger import get_logger

logger = get_logger(__name__)


class GenerateAppendixItemAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    introduction: Optional[str] = Field(None, description="绪论")
    main_content: Optional[str] = Field(None, description="正文")
    references: Optional[str] = Field(None, description="参考文献")
    user_feed_summaries: Optional[list[str]] = Field(None, description="资料")
    user_feed_summaries_str: Optional[str] = Field(None, description="资料")
    appendix_info: Optional[str] = Field(None, description="附录信息")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        if self.user_feed_summaries:
            text = ""
            for n, user_feed_summary in enumerate(self.user_feed_summaries, 1):
                text += f"{n}. {user_feed_summary}\n\n"
            self.user_feed_summaries_str = text
        self.user_message_template_path = "user_prompt/gen_appendix_item.jinja"


class GenerateAppendixItemAgentResponse(RootModel[str], BaseAgentResponse):
    def to_llm_response(self):
        return self.root


class GenerateAppendixItemAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_appendix_item.jinja"
        self.input_type = GenerateAppendixItemAgentInput
        self.output_type = GenerateAppendixItemAgentResponse
        self.output_parser = 'str'
        super().__init__(options, failover_options_list)

    def _filter_non_table_tags(self, text: str) -> str:
        """
        过滤非表格类型的HTML标签，只保留表格相关标签
        允许的标签：table, tr, th, td
        """
        # 定义需要移除的常见非表格HTML标签
        non_table_tags = [
            '<p>', '</p>', '<div>', '</div>', '<span>', '</span>',
            '<strong>', '</strong>', '<em>', '</em>', '<b>', '</b>', '<i>', '</i>',
            '<u>', '</u>', '<s>', '</s>', '<mark>', '</mark>',
            '<ul>', '</ul>', '<ol>', '</ol>', '<li>', '</li>', '<dl>', '</dl>', '<dt>', '</dt>', '<dd>', '</dd>',
            '<br>', '<br/>', '<br />', '<hr>', '<hr/>', '<hr />',
            '<h1>', '</h1>', '<h2>', '</h2>', '<h3>', '</h3>', '<h4>', '</h4>', '<h5>', '</h5>', '<h6>', '</h6>',
            '<code>', '</code>', '<pre>', '</pre>', '<kbd>', '</kbd>', '<samp>', '</samp>',
            '<blockquote>', '</blockquote>', '<q>', '</q>', '<cite>', '</cite>',
            '<a>', '</a>', '<img>'
            '<form>', '</form>', '<input>', '<button>', '</button>', '<select>', '</select>', '<textarea>', '</textarea>',
            '<section>', '</section>', '<article>', '</article>', '<header>', '</header>', '<footer>', '</footer>',
            '<nav>', '</nav>', '<aside>', '</aside>', '<main>', '</main>', '<address>', '</address>',
            '<details>', '</details>', '<summary>', '</summary>', '<dialog>', '</dialog>',
            '<fieldset>', '</fieldset>', '<legend>', '</legend>', '<label>', '</label>',
            '<small>', '</small>', '<sub>', '</sub>', '<sup>', '</sup>', '<del>', '</del>', '<ins>', '</ins>'
        ]
        filtered_text = text
        for tag in non_table_tags:
            filtered_text = filtered_text.replace(tag, '')
        return filtered_text

    async def ainvoke(
        self, agent_input: BaseAgentInput, config: ChainRunnableConfig = None
    ) -> BaseAgentResponse:
        result: GenerateAppendixItemAgentResponse = await super().ainvoke(agent_input, config)
        result.root = self._filter_non_table_tags(result.root)
        return result

    def invoke(self, agent_input: GenerateAppendixItemAgentInput, config: ChainRunnableConfig = None) \
            -> GenerateAppendixItemAgentResponse:
        result: GenerateAppendixItemAgentResponse = super().invoke(agent_input, config)
        result.root = self._filter_non_table_tags(result.root)
        return result
