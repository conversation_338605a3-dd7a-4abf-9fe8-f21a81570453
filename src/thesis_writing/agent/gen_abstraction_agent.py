from datetime import datetime
from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class GenerateAbstractionAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    introduction: Optional[str] = Field(None, description="绪论")
    main_content: Optional[str] = Field(None, description="正文")
    conclusion: Optional[str] = Field(None, description="结论")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_abstraction_keywords.jinja"


class GenerateAbstractionAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="思路")
    abstract: Optional[str] = Field(None, description="摘要")
    keywords: Optional[str] = Field(None, description="关键词")


class GenerateAbstractionAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_abstraction_keywords.jinja"
        self.input_type = GenerateAbstractionAgentInput
        self.output_type = GenerateAbstractionAgentResponse
        super().__init__(options, failover_options_list)
