import json
from typing import Optional, List

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput, ChainRunnableConfig
from thesis_writing.utils.common_util import replace_quotes


class SegmentToRefineWithThesisSegment(BaseModel):
    segment_id: str = Field(..., description="章节ID")
    title: str = Field(..., description="章节标题")
    guide: str = Field(None, description="章节指导")
    length: int = Field(None, description="章节建议书写长度")
    content: str = Field(..., description="章节内容")


class RefinePaperWithThesisSegmentAgentInput(BaseAgentInput):
    subject: str = Field(None, description="论文标题")
    segments: list[SegmentToRefineWithThesisSegment] = Field(..., description="章节内容", min_length=1)
    segments_str: Optional[str] = Field(None, description="章节内容序列化")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/thesis_segment/refine_paper_with_thesis_segment.jinja"
        self.segments_str = json.dumps([segment.model_dump(exclude_none=True) for segment in self.segments],
                                       separators=(',', ':'), indent=2, ensure_ascii=False)


class RefinePaperOperationWithThesisSegment(BaseModel):
    reason: str = Field(None, description="解释原因")
    para_ids: list[str] = Field(None, description="段落编号集合")


class RefineSegmentActionWithThesisSegment(BaseModel):
    segment_id: str = Field(..., description="章节ID")
    title: str = Field(..., description="章节标题", min_length=1)
    action: str = Field(None, description="修改建议")
    result: Optional[str] = Field(None, description="修改后的内容")


class RefinePaperWithThesisSegmentAgentResponse(BaseAgentResponse):
    analysis: Optional[str] = Field(None, description="逐一分析各个小节存在的问题")
    actions: list[RefineSegmentActionWithThesisSegment] = Field(None, description="修改建议")


class RefinePaperWithThesisSegmentAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/thesis_segment/refine_paper_with_thesis_segment.jinja"
        self.input_type = RefinePaperWithThesisSegmentAgentInput
        self.output_type = RefinePaperWithThesisSegmentAgentResponse
        super().__init__(options, failover_options_list)

    def invoke(self, agent_input: RefinePaperWithThesisSegmentAgentInput, config: ChainRunnableConfig = None) \
            -> RefinePaperWithThesisSegmentAgentResponse:
        result: RefinePaperWithThesisSegmentAgentResponse = super().invoke(agent_input, config)
        segments_ = {segment.segment_id: segment.content for segment in agent_input.segments}
        if result.actions:
            for action in result.actions:
                if action.result and action.segment_id in segments_:
                    action.result = replace_quotes(segments_[action.segment_id], action.result)
        return result
