import re
from datetime import datetime
from typing import Optional, List, Dict

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput, ChainRunnableConfig
from thesis_writing.agent.gen_plan_addition_agent import PlanAddition
from thesis_writing.utils.reference import ThesisReference, ReferenceManager
from thesis_writing.utils.template_parser import get_template
from thesis_writing.utils.common_util import replace_quotes


class RefineSegmentsWithThesisSegmentAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")

    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    segment_title: Optional[str] = Field(None, description="当前目标小节标题")
    segment_content: Optional[str] = Field(None, description="当前目标小节内容")
    segment_writing_plan: str = Field(..., description="当前目标小节写作计划", min_length=1)
    suggestions: Optional[str] = Field(None, description="当前小节的专家评估建议")

    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    materials: Optional[Dict[str, List[str]]] = Field(None, description="检索结果")
    materials_str: Optional[str] = Field(None, description="检索结果")
    additions: Optional[List[PlanAddition]] = Field(None, description="补充信息")
    context: Optional[str] = Field(None, description="已完成小节内容")
    references: Optional[List[ThesisReference]] = Field(None, description="参考文献")
    need_check_placeholders: Optional[bool] = Field(True, description="是否需要检查占位符")

    def to_msg(self):
        kwargs = self.model_dump(exclude={"references", "additions"})
        if not self.references or len(self.references) == 0:
            kwargs["references"] = '无'
        else:
            kwargs["references"] = "\n".join([reference.to_detail_str() for reference in self.references])

        segment_additions_str = ''
        for each in self.additions:
            segment_additions_str += f"<addition>\n{each.to_chat_string()}\n</addition>\n"
        if len(segment_additions_str) == 0:
            segment_additions_str = "无"
        kwargs["additions"] = segment_additions_str

        return get_template(self.user_message_template_path, **kwargs)

    def _replace_figures_and_tables(self, text):
        pattern = r'(图|表)[\.\s\d-]+'
        result = re.sub(pattern, lambda match: match.group(1), text)
        return result

    def _remove_markdown(self, text):
        pattern = r'^\s*#+\s.*?$'
        result = re.sub(pattern, '', text, flags=re.MULTILINE)
        result = result.replace('**', '')
        return result.strip()

    def _remove_references(self, text):
        pattern = r'\[\d+\]'
        result = re.sub(pattern, '', text)
        return result

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/thesis_segment/refine_segments_with_thesis_segment.jinja"

        if self.materials:
            self.materials_str = ""
            for material_type, materials in self.materials.items():
                temp_str = f"<{material_type}_materials>\n{"\n\n".join(materials)}\n</{material_type}_materials>\n"
                temp_str = self._replace_figures_and_tables(temp_str)
                temp_str = self._remove_markdown(temp_str)
                temp_str = self._remove_references(temp_str)

                self.materials_str += temp_str + "\n"


class Reference(BaseModel):
    id: str = Field(None, description="引用论文的序号")
    name: str = Field(None, description="引用论文的名称")
    author: str = Field(None, description="引用论文的作者")
    usage: str = Field(None, description="如何使用")


class RefineSegmentsWithThesisSegmentAgentResponse(BaseAgentResponse):
    reference_thought: str = Field(None, description="参考文献思路")
    references: List[Reference] = Field(None, description="参考文献")
    placeholders_thought: str = Field(None, description="其他占位符思路")
    deleted_placeholders: list[str] = Field(None, description="删除的占位符列表")
    latex_thought: str = Field(None, description="LaTeX思路")
    chart_thought: str = Field(None, description="图表润色思路")
    segment_thought: str = Field(None, description="思路")
    segment_content: str = Field(..., description="节内容", min_length=1)


class RefineSegmentsWithThesisSegmentAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.input_type = RefineSegmentsWithThesisSegmentAgentInput
        self.output_type = RefineSegmentsWithThesisSegmentAgentResponse
        self.system_message_template_path = "system_prompt/thesis_segment/refine_segments_with_thesis_segment.jinja"
        super().__init__(options, failover_options_list)

    def invoke(self, agent_input: RefineSegmentsWithThesisSegmentAgentInput, config: ChainRunnableConfig = None) \
            -> RefineSegmentsWithThesisSegmentAgentResponse:
        result: RefineSegmentsWithThesisSegmentAgentResponse = super().invoke(agent_input, config)
        self._repair_segment_content(agent_input, result)
        if agent_input.need_check_placeholders:
            self._check_placeholders(agent_input, result)
            self._check_table(agent_input, result)
        return result

    async def ainvoke(self, agent_input: RefineSegmentsWithThesisSegmentAgentInput, config: ChainRunnableConfig = None) \
            -> RefineSegmentsWithThesisSegmentAgentResponse:
        result: RefineSegmentsWithThesisSegmentAgentResponse = await super().ainvoke(agent_input, config)
        self._repair_segment_content(agent_input, result)
        if agent_input.need_check_placeholders:
            self._check_placeholders(agent_input, result)
            self._check_table(agent_input, result)
        return result

    def _check_placeholders(self, agent_input: RefineSegmentsWithThesisSegmentAgentInput, result: RefineSegmentsWithThesisSegmentAgentResponse):
        if not result or not result.segment_content:
            return
        deleted_placeholders = result.deleted_placeholders or []
        pattern = r'\[\[\[[^\]]*\]\]\]'
        old_placeholders = re.findall(pattern, agent_input.segment_content)
        if not old_placeholders:
            return
        for placeholder in old_placeholders:
            if placeholder in result.deleted_placeholders:
                continue
            if placeholder not in result.segment_content:
                raise ValueError(f"Placeholder '{placeholder}' not found in segment content.")

        new_placeholders = re.findall(pattern, result.segment_content)
        if len(old_placeholders) != len(new_placeholders) + len(deleted_placeholders):
            raise ValueError("The number of placeholders in the segment content does not match the expected count.")

    def _check_table(self, agent_input: RefineSegmentsWithThesisSegmentAgentInput, result: RefineSegmentsWithThesisSegmentAgentResponse):
        if not result or not result.segment_content:
            return
        patterns = [r'<table>', r'</table>']
        for pattern in patterns:
            placeholders = re.findall(pattern, agent_input.segment_content or "")
            if not placeholders:
                continue
            for placeholder in placeholders:
                if placeholder not in (result.segment_content or ""):
                    raise ValueError(f"占位符 '{placeholder}' 未在 segment content 中找到。")

    def validate_output(self, input, output: RefineSegmentsWithThesisSegmentAgentResponse, remaining_retries=0):
        if remaining_retries > 0:
            bad_words = ['##', '**']
            if any(bad_word in output.segment_content for bad_word in bad_words):
                raise ValueError("Bad words detected in segment content")
        if not self._validate_unescaped_unicode(output.segment_content):
            raise ValueError("Too many unescaped unicode detected in segment content")

    def _validate_unescaped_unicode(self, text):
        total_chars = len(text)
        if total_chars == 0:
            return False
        escaped_unicode_pattern = re.compile(r'(\\u[0-9a-fA-F]{4}|\\U[0-9a-fA-F]{8}|\\x[0-9a-fA-F]{2})')
        escaped_count = len(escaped_unicode_pattern.findall(text))
        return escaped_count < 20

    def _get_invalid_chart_or_table(self, segment_content, additions):
        additions_ids = {each.id for each in additions} if additions else set()
        invalid_chart_or_table = [
            match for match in re.findall(r"<(chart|figure|table|addition) id=\"(.*?)\"", segment_content)
            if match[1] not in additions_ids
        ]
        return invalid_chart_or_table

    def _repair_segment_content(self, agent_input: RefineSegmentsWithThesisSegmentAgentInput, result: RefineSegmentsWithThesisSegmentAgentResponse):
        if not result or not result.segment_content:
            return
        self._remove_invalid_tags(result, agent_input.additions)
        self._correct_addition_tags(result, agent_input.additions)
        self._remove_duplicate_tags(result)
        self._fix_reference_tags(result, agent_input.references)
        self.format_other_placeholder(result.segment_content)
        result.segment_content = replace_quotes(agent_input.segment_content, result.segment_content)
        self._replace_placeholders(result)

    def _replace_placeholders(self, result: RefineSegmentsWithThesisSegmentAgentResponse):
        def custom_replace(match):
            start = match.start()
            prefix = result.segment_content[max(0, start-20):start]
            if re.search(r'text=\[\d+((-\d+)|(,\d+))*$', prefix):
                return ']]]]'
            else:
                return ']]]'
        result.segment_content = result.segment_content.replace('[[[[', '[[[')
        # 替换 [[[形状 firstParaId=2E7FDFE3, shapeId=图片 6]]]] 但是不替换 [[[上标 text=[1]]]]、[[[上标 text=[21-22]]]]、[[[上标 text=[5,7]]]] 等
        result.segment_content = re.sub(r'\]\]\]\]', lambda m: custom_replace(m), result.segment_content)
        pattern = r'\[\[\[[^\]]*\]\]\]'
        new_placeholders = re.findall(pattern, result.segment_content)
        if not new_placeholders:
            return
        for placeholder in new_placeholders:

            if f'{placeholder}。' in result.segment_content:
                result.segment_content = result.segment_content.replace(f'{placeholder}。', f'。{placeholder}')

    def _remove_invalid_tags(self, result: RefineSegmentsWithThesisSegmentAgentResponse, additions: List[PlanAddition]):
        invalid_chart_or_table = self._get_invalid_chart_or_table(result.segment_content, additions)
        for tag, id in invalid_chart_or_table:
            result.segment_content = re.sub(rf'<{tag} id="{id}"[^>]*/>', '', result.segment_content)
            result.segment_content = re.sub(rf'<{tag} id="{id}"[^>]*>.*?</{tag}>', '', result.segment_content)
        result.segment_content = result.segment_content.replace("**", "").replace('<addition id="None"/>', "").replace(
            '<addition id="None"></addition>', "").replace('<addition>None</addition>', "")

    def _remove_duplicate_tags(self, result: RefineSegmentsWithThesisSegmentAgentResponse):
        seen_tags = set()

        def replace_tag(match):
            tag = match.group(0)
            if tag in seen_tags:
                return ""
            seen_tags.add(tag)
            return tag

        result.segment_content = re.sub(r'<chart id="[^"]+"/>', replace_tag, result.segment_content)

    def _correct_addition_tags(self, result: RefineSegmentsWithThesisSegmentAgentResponse, additions: List[PlanAddition]):
        tag_types = ["table", "image", "figure", "addition", "chart"]
        for addition in additions:
            if not addition.id:
                continue
            correct_tag = f'<chart id="{addition.id}"/>'
            incorrect_tags = [f'<{tag} id="{addition.id}"/>' for tag in tag_types]
            incorrect_tags += [
                re.compile(rf'<{tag} id="{addition.id}"[^>]*>.*?</{tag}>', re.DOTALL)
                for tag in tag_types
            ]
            for incorrect in incorrect_tags:
                if isinstance(incorrect, str):
                    result.segment_content = result.segment_content.replace(incorrect, correct_tag)
                else:
                    result.segment_content = incorrect.sub(correct_tag, result.segment_content)
            self._adjust_chart_position(result, correct_tag, addition.id)

    def _adjust_chart_position(self, result: RefineSegmentsWithThesisSegmentAgentResponse, correct_tag: str, addition_id: str):
        result.segment_content = result.segment_content.replace(f'{correct_tag}。', f'。{correct_tag}').replace(
            f'{correct_tag}.', f'.{correct_tag}')
        if correct_tag not in result.segment_content:
            if addition_id in result.segment_content:
                chart_index = result.segment_content.find(addition_id)
                if chart_index != -1:
                    period_index = result.segment_content.find("。", chart_index)
                    if period_index != -1:
                        result.segment_content = (
                                result.segment_content[:period_index + 1] + correct_tag + result.segment_content[
                                                                                          period_index + 1:]
                        )
                    else:
                        result.segment_content += correct_tag
                else:
                    result.segment_content += correct_tag
            else:
                result.segment_content += correct_tag

    def _fix_reference_tags(self, result: RefineSegmentsWithThesisSegmentAgentResponse, references: List[ThesisReference]):
        reference_manager = ReferenceManager()
        reference_manager.extend_references(references)
        result.segment_content = self.split_strict_nested_brackets(result.segment_content)
        for ref in references:
            tag, wrong_tag = f'[[{ref.id}]]', f'[{ref.id}]'
            if tag not in result.segment_content and wrong_tag in result.segment_content:
                result.segment_content = result.segment_content.replace(wrong_tag, tag)
        _, not_found_ids = reference_manager.collect_ref(result.segment_content)
        for each in not_found_ids:
            result.segment_content = result.segment_content.replace(f'[[{each}]]', '')

    def split_strict_nested_brackets(self, s):
        pattern = r'\[\[([\d\,，、\s\]\[]+)\]\]'

        def replacer(match):
            content = match.group(1)
            content = content.replace(' ', '')
            replace_pattern = r'[\]\[,，、\s]+'
            content = re.sub(replace_pattern, ',', content)
            parts = content.split(',')
            if all(part.isdigit() for part in parts):
                return ''.join(f'[[{part}]]' for part in parts)

            return match.group(0)

        result = re.sub(pattern, replacer, s)
        return result

    def format_other_placeholder(self, content):
        content = content.replace("[[[text]]]", "")
        return content
