import re
from datetime import datetime
from typing import Optional, List, Tuple

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput, ChainRunnableConfig
from thesis_writing.utils.common_util import replace_quotes
from thesis_writing.utils.reference import ThesisReference, ReferenceManager


class RefineLeadParagraphAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")

    segment_title: Optional[str] = Field(None, description="当期段落标题")
    segment_content: Optional[str] = Field(None, description="当期段落导语内容")
    previous_context: Optional[str] = Field(None, description="当前段落小节内容")

    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/thesis_segment/refine_lead_paragraph.jinja"


class RefineLeadParagraphAgentResponse(BaseAgentResponse):
    thought: str = Field(None, description="思路")
    segment_content: str = Field(..., description="节内容", min_length=1)


class RefineLeadParagraphWithThesisSegmentAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.input_type = RefineLeadParagraphAgentInput
        self.output_type = RefineLeadParagraphAgentResponse
        self.system_message_template_path = "system_prompt/thesis_segment/refine_lead_paragraph.jinja"
        super().__init__(options, failover_options_list)

    def invoke(self, agent_input: RefineLeadParagraphAgentInput, config: ChainRunnableConfig = None) \
            -> RefineLeadParagraphAgentResponse:
        result: RefineLeadParagraphAgentResponse = super().invoke(agent_input, config)
        self._repair_segment_content(agent_input, result)
        return result

    async def ainvoke(self, agent_input: RefineLeadParagraphAgentInput, config: ChainRunnableConfig = None) \
            -> RefineLeadParagraphAgentResponse:
        result: RefineLeadParagraphAgentResponse = await super().ainvoke(agent_input, config)
        self._repair_segment_content(agent_input, result)
        return result

    def validate_output(self, input, output: RefineLeadParagraphAgentResponse, remaining_retries=0):
        if remaining_retries > 0:
            bad_words = ['##', '**']
            if any(bad_word in output.segment_content for bad_word in bad_words):
                raise ValueError("Bad words detected in segment content")
        if not self._validate_unescaped_unicode(output.segment_content):
            raise ValueError("Too many unescaped unicode detected in segment content")

    def _validate_unescaped_unicode(self, text):
        total_chars = len(text)
        if total_chars == 0:
            return False
        escaped_unicode_pattern = re.compile(r'(\\u[0-9a-fA-F]{4}|\\U[0-9a-fA-F]{8}|\\x[0-9a-fA-F]{2})')
        escaped_count = len(escaped_unicode_pattern.findall(text))
        return escaped_count < 20

    def _get_invalid_chart_or_table(self, segment_content, additions):
        additions_ids = {each.id for each in additions} if additions else set()
        invalid_chart_or_table = [
            match for match in re.findall(r"<(chart|figure|table|addition) id=\"(.*?)\"", segment_content)
            if match[1] not in additions_ids
        ]
        return invalid_chart_or_table

    def _repair_segment_content(self, agent_input: RefineLeadParagraphAgentInput,
                                result: RefineLeadParagraphAgentResponse):
        if not result or not result.segment_content:
            return
        self._remove_duplicate_tags(result)
        self.format_other_placeholder(result.segment_content)
        result.segment_content = replace_quotes(agent_input.segment_content, result.segment_content)

    def _remove_duplicate_tags(self, result: RefineLeadParagraphAgentResponse):
        seen_tags = set()

        def replace_tag(match):
            tag = match.group(0)
            if tag in seen_tags:
                return ""
            seen_tags.add(tag)
            return tag

        result.segment_content = re.sub(r'<chart id="[^"]+"/>', replace_tag, result.segment_content)

    def format_other_placeholder(self, content):
        content = content.replace("[[[text]]]", "")
        return content
