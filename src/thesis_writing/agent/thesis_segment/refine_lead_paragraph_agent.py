import re
from datetime import datetime
from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput, ChainRunnableConfig
from thesis_writing.agent.gen_plan_addition_agent import PlanAddition
from thesis_writing.utils.reference import ThesisReference, ReferenceManager


class RefineLeadParagraphAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")

    segment_title: Optional[str] = Field(None, description="当期段落标题")
    segment_content: Optional[str] = Field(None, description="当期段落导语内容")
    previous_context: Optional[str] = Field(None, description="当前段落小节内容")

    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/thesis_segment/refine_lead_paragraph.jinja"


class RefineLeadParagraphAgentResponse(BaseAgentResponse):
    thought: str = Field(None, description="思路")
    segment_content: str = Field(..., description="节内容", min_length=1)


class RefineLeadParagraphWithThesisSegmentAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.input_type = RefineLeadParagraphAgentInput
        self.output_type = RefineLeadParagraphAgentResponse
        self.system_message_template_path = "system_prompt/thesis_segment/refine_lead_paragraph.jinja"
        super().__init__(options, failover_options_list)

    def invoke(self, agent_input: RefineLeadParagraphAgentInput, config: ChainRunnableConfig = None) \
            -> RefineLeadParagraphAgentResponse:
        result: RefineLeadParagraphAgentResponse = super().invoke(agent_input, config)
        self._repair_segment_content(agent_input, result)
        return result

    async def ainvoke(self, agent_input: RefineLeadParagraphAgentInput, config: ChainRunnableConfig = None) \
            -> RefineLeadParagraphAgentResponse:
        result: RefineLeadParagraphAgentResponse = await super().ainvoke(agent_input, config)
        self._repair_segment_content(agent_input, result)
        return result

    def validate_output(self, input, output: RefineLeadParagraphAgentResponse, remaining_retries=0):
        if remaining_retries > 0:
            bad_words = ['##', '**']
            if any(bad_word in output.segment_content for bad_word in bad_words):
                raise ValueError("Bad words detected in segment content")
        if not self._validate_unescaped_unicode(output.segment_content):
            raise ValueError("Too many unescaped unicode detected in segment content")

    def _validate_unescaped_unicode(self, text):
        total_chars = len(text)
        if total_chars == 0:
            return False
        escaped_unicode_pattern = re.compile(r'(\\u[0-9a-fA-F]{4}|\\U[0-9a-fA-F]{8}|\\x[0-9a-fA-F]{2})')
        escaped_count = len(escaped_unicode_pattern.findall(text))
        return escaped_count < 20

    def _get_invalid_chart_or_table(self, segment_content, additions):
        additions_ids = {each.id for each in additions} if additions else set()
        invalid_chart_or_table = [
            match for match in re.findall(r"<(chart|figure|table|addition) id=\"(.*?)\"", segment_content)
            if match[1] not in additions_ids
        ]
        return invalid_chart_or_table

    def _repair_segment_content(self, agent_input: RefineLeadParagraphAgentInput,
                                result: RefineLeadParagraphAgentResponse):
        if not result or not result.segment_content:
            return
        self._remove_duplicate_tags(result)
        self.format_other_placeholder(result.segment_content)
        self._replace_quotes(result)


    def _remove_duplicate_tags(self, result: RefineLeadParagraphAgentResponse):
        seen_tags = set()

        def replace_tag(match):
            tag = match.group(0)
            if tag in seen_tags:
                return ""
            seen_tags.add(tag)
            return tag

        result.segment_content = re.sub(r'<chart id="[^"]+"/>', replace_tag, result.segment_content)

    def _adjust_chart_position(self, result: RefineLeadParagraphAgentResponse, correct_tag: str, addition_id: str):
        result.segment_content = result.segment_content.replace(f'{correct_tag}。', f'。{correct_tag}').replace(
            f'{correct_tag}.', f'.{correct_tag}')
        if correct_tag not in result.segment_content:
            if addition_id in result.segment_content:
                chart_index = result.segment_content.find(addition_id)
                if chart_index != -1:
                    period_index = result.segment_content.find("。", chart_index)
                    if period_index != -1:
                        result.segment_content = (
                                result.segment_content[:period_index + 1] + correct_tag + result.segment_content[
                                                                                          period_index + 1:]
                        )
                    else:
                        result.segment_content += correct_tag
                else:
                    result.segment_content += correct_tag
            else:
                result.segment_content += correct_tag

    def _fix_reference_tags(self, result: RefineLeadParagraphAgentResponse, references: List[ThesisReference]):
        reference_manager = ReferenceManager()
        reference_manager.extend_references(references)
        result.segment_content = self.split_strict_nested_brackets(result.segment_content)
        for ref in references:
            tag, wrong_tag = f'[[{ref.id}]]', f'[{ref.id}]'
            if tag not in result.segment_content and wrong_tag in result.segment_content:
                result.segment_content = result.segment_content.replace(wrong_tag, tag)
        _, not_found_ids = reference_manager.collect_ref(result.segment_content)
        for each in not_found_ids:
            result.segment_content = result.segment_content.replace(f'[[{each}]]', '')

    def split_strict_nested_brackets(self, s):
        pattern = r'\[\[([\d\,，、\s\]\[]+)\]\]'

        def replacer(match):
            content = match.group(1)
            content = content.replace(' ', '')
            replace_pattern = r'[\]\[,，、\s]+'
            content = re.sub(replace_pattern, ',', content)
            parts = content.split(',')
            if all(part.isdigit() for part in parts):
                return ''.join(f'[[{part}]]' for part in parts)

            return match.group(0)

        result = re.sub(pattern, replacer, s)
        return result

    def format_other_placeholder(self, content):
        content = content.replace("[[[text]]]", "")
        return content

    def _replace_quotes(self, result: RefineLeadParagraphAgentResponse):
        text = result.segment_content
        chars = []
        inside_double_quotes = False
        single_quote_indices = []

        for i, char in enumerate(text):
            if char == '“':
                inside_double_quotes = True
                chars.append(char)
            elif char == '”':
                inside_double_quotes = False
                chars.append(char)
            elif char == '‘' and not inside_double_quotes:
                single_quote_indices.append(i)
                chars.append(char)
            elif char == '’' and not inside_double_quotes:
                if single_quote_indices:
                    left_index = single_quote_indices.pop()
                    if len(single_quote_indices) == 0:
                        chars[left_index] = '“'
                        chars.append('”')
                    else:
                        chars.append(char)
                else:
                    chars.append(char)
            else:
                chars.append(char)
        result.segment_content = "".join(chars)
