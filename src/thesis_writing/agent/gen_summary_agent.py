from datetime import datetime
from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput
from thesis_writing.retriever.retrieve_service import RetrieveService
from thesis_writing.utils.logger import get_logger

logger = get_logger(__name__)


class GenerateSummaryAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    user_feed_summaries: Optional[List[str]] = Field(None, description="用户投喂内容的摘要")
    user_feed_summaries_str: Optional[str] = Field(None, description="用户投喂内容的摘要字符串")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        if self.user_feed_summaries:
            text = ""
            for n, user_feed_summary in enumerate(self.user_feed_summaries, 1):
                text += f"{n}. {user_feed_summary}\n"
            self.user_feed_summaries_str = text
            self.user_message_template_path = "user_prompt/gen_summary_with_feed.jinja"
        else:
            self.user_message_template_path = "user_prompt/gen_summary.jinja"

    @staticmethod
    async def construct_input_with_retrieval(major, subject, keywords: str = "", user_feed_summaries: list[str] = None)\
            -> "GenerateSummaryAgentInput":
        gen_summary_input = GenerateSummaryAgentInput(
            subject=subject,
            major=major,
            keywords=keywords,
            user_feed_summaries=user_feed_summaries
        )
        max_retries = 3
        for _ in range(max_retries):
            try:
                retrieval_summaries = await RetrieveService().search_thesis_summary(major, subject, keywords, 3, 0.3)
                for retrieval_summary in retrieval_summaries or []:
                    exp_input = GenerateSummaryAgentInput(
                        subject=retrieval_summary[1].title,
                        major=retrieval_summary[1].major,
                        keywords=retrieval_summary[1].keywords
                    )
                    exp_output = GenerateSummaryAgentResponse(
                        thought="xxxxx",
                        summary=retrieval_summary[1].summary
                    )

                    gen_summary_input.append_example(exp_input.to_msg()
                                                     + "\n\n# 输出\n" + exp_output.model_dump_json(exclude_none=True))

                return gen_summary_input
            except Exception as e:
                logger.exception(e)
        raise RuntimeError("Failed to search summary after retries")


class GenerateSummaryAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="思路")
    summary: str = Field(None, description="全文概述")


class GenerateSummaryAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_summary.jinja"
        self.input_type = GenerateSummaryAgentInput
        self.output_type = GenerateSummaryAgentResponse
        super().__init__(options, failover_options_list)
