from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class GenerateEnglishTitleAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_en_title.jinja"


class GenerateEnglishTitleAgentResponse(BaseAgentResponse):
    title: str = Field(..., description="英文标题")


class GenerateEnglishTitleAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_en_title.jinja"
        self.input_type = GenerateEnglishTitleAgentInput
        self.output_type = GenerateEnglishTitleAgentResponse
        super().__init__(options, failover_options_list)
