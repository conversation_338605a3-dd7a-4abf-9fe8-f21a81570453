from typing import List, Optional

from pydantic import Field
from thesis_writing.agent.gen_conclusion_agent import GenerateConclusionAgentInput, GenerateConclusionAgentResponse

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent
from thesis_writing.agent.gen_abstraction_agent import GenerateAbstractionAgentInput, GenerateAbstractionAgentResponse
from thesis_writing.agent.gen_acknowledge_agent import GenerateAcknowledgeAgent, GenerateAcknowledgeAgentInput, \
    GenerateAcknowledgeAgentResponse
from thesis_writing.agent.gen_plan_addition_agent import GeneratePlanAdditionAgentInput, \
    GeneratePlanAdditionAgentResponse, PlanAddition, GenerateCommonPlanAdditionAgent
from thesis_writing.agent.gen_plan_agent import GeneratePlanAgentResponse, GeneratePlanAgentInput, \
    filter_plan_node_title
from thesis_writing.agent.gen_segment_agent import GenerateSegmentAgent, GenerateSegmentAgentInput, \
    GenerateSegmentAgentResponse
from thesis_writing.agent.gen_summary_agent import GenerateSummaryAgent, GenerateSummaryAgentInput, \
    GenerateSummaryAgentResponse
from thesis_writing.utils.plan_util import fix_title_numbering, check_sections, check_writing_plan_content_type


class GenShortSummaryAgent(GenerateSummaryAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        super().__init__(options, failover_options_list)
        self.input_type = GenerateSummaryAgentInput
        self.output_type = GenerateSummaryAgentResponse
        self.system_message_template_path = "system_prompt/short_paper/gen_short_summary.jinja"


class GenShortPlanAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        super().__init__(options, failover_options_list)
        self.system_message_template_path = "system_prompt/short_paper/gen_short_plan.jinja"
        self.input_type = GeneratePlanAgentInput
        self.output_type = GeneratePlanAgentResponse

    def validate_output(self, input: GeneratePlanAgentInput, output: GeneratePlanAgentResponse, remaining_retries=0):
        if not output or not output.writing_plan_nodes:
            raise ValueError("Writing plan nodes are empty or None")
        output.writing_plan_nodes = filter_plan_node_title(output.writing_plan_nodes)
        if input.introduction_title:
            output.get_introduction_writing_plan().title = input.introduction_title
        if input.conclusion_title:
            output.get_conclusion_writing_plan().title = input.conclusion_title
        fix_title_numbering(input, output.writing_plan_nodes)
        if not input.conclusion_has_subsections:
            output.clear_node_children(content_type="结论")
        if not input.introduction_has_subsections:
            output.clear_node_children(content_type="绪论")
        check_sections(input, output)

        if not check_writing_plan_content_type(output):
            raise ValueError("Writing Plan Content Type Error")
        output.generate_node_id()
        output.adjust_plan_writing_length(input.word_count)

class GenShortShortPlanAgent(GenShortPlanAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        super().__init__(options, failover_options_list)
        self.system_message_template_path = "system_prompt/short_paper/gen_short_short_plan.jinja"
        self.input_type = GeneratePlanAgentInput
        self.output_type = GeneratePlanAgentResponse

class GenShortPlanAdditionAgentResponse(GeneratePlanAdditionAgentResponse):
    step1: Optional[str] = Field(None, description="思考步骤1")
    step2: Optional[str] = Field(None, description="思考步骤2")
    additions: List[PlanAddition] = Field(default_factory=list, max_length=3, description="图表计划信息")


class GenShortPlanAdditionAgent(GenerateCommonPlanAdditionAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/short_paper/gen_short_plan_addition.jinja"
        self.input_type = GeneratePlanAdditionAgentInput
        self.output_type = GenShortPlanAdditionAgentResponse
        super().__init__(options, failover_options_list)


class GenShortSegmentAgent(GenerateSegmentAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        super().__init__(options, failover_options_list)
        self.input_type = GenerateSegmentAgentInput
        self.output_type = GenerateSegmentAgentResponse
        self.template_paths = {
            'with_empirical': "system_prompt/short_paper/gen_short_segment_with_empirical.jinja",
            'without_empirical': "system_prompt/short_paper/gen_short_segment.jinja"
        }

    def _get_template_path(self, has_empirical_data: bool) -> str:
        """根据是否有实证数据选择模板路径"""
        return self.template_paths['with_empirical' if has_empirical_data else 'without_empirical']


class GenShortAcknowledgeAgent(GenerateAcknowledgeAgent):

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        super().__init__(options, failover_options_list)
        self.system_message_template_path = "system_prompt/short_paper/gen_short_acknowledge.jinja"
        self.input_type = GenerateAcknowledgeAgentInput
        self.output_type = GenerateAcknowledgeAgentResponse


class GenShortAbstractAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        super().__init__(options, failover_options_list)
        self.input_type = GenerateAbstractionAgentInput
        self.output_type = GenerateAbstractionAgentResponse
        self.system_message_template_path = "system_prompt/short_paper/gen_short_abstraction_keywords.jinja"

class GenShortConclusionAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/short_paper/gen_short_conclusion.jinja"
        self.input_type = GenerateConclusionAgentInput
        self.output_type = GenerateConclusionAgentResponse
        super().__init__(options, failover_options_list)