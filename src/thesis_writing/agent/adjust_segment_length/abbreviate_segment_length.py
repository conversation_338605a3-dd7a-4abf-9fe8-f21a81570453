from typing import List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentInput, BaseAgentResponse


class AbbreviateSegmentLengthAgentInput(BaseAgentInput):
    current_content: str = Field(..., description="当前小节内容", min_length=1)
    current_length: int = Field(..., description="当前小节字数")
    target_length: int = Field(..., description="目标字数")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/adjust_segment_length/abbreviate_segment_length.jinja"


class AbbreviateSegmentLengthAgentResponse(BaseAgentResponse):
    thought: str = Field(..., description="缩写思路")
    abbreviated_content: str = Field(..., description="缩写后内容", min_length=1)


class AbbreviateSegmentLengthAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/adjust_segment_length/abbreviate_segment_length.jinja"
        self.input_type = AbbreviateSegmentLengthAgentInput
        self.output_type = AbbreviateSegmentLengthAgentResponse
        super().__init__(options, failover_options_list)
