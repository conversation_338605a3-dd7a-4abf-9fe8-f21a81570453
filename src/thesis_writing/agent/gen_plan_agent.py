import json
import re
from datetime import datetime
from typing import Optional, List, OrderedDict

import markdown_to_json
from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput
from thesis_writing.entity.enums import WritingPlanContentType, EducationLevel
from thesis_writing.retriever.retrieve_service import RetrieveService
from thesis_writing.utils.logger import get_logger
from thesis_writing.utils.plan_util import check_writing_plan_content_type, fix_title_numbering, check_sections

logger = get_logger(__name__)


class GeneratePlanAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    summary: str = Field(..., description="全文概述", min_length=1)
    word_count: Optional[str] = Field("20000", description="全文字数")
    generate_third_level: Optional[str] = Field('否', description="是否生成三级目录")
    remarks: Optional[str] = Field("无", description="备注信息")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)
    introduction_title: Optional[str] = Field(None, title="指定绪论标题")
    introduction_has_subsections: Optional[bool] = Field(None, title="绪论是否分小节")
    introduction_title_has_number: Optional[bool] = Field(None, title="绪论标题是否加序号")
    conclusion_title: Optional[str] = Field(None, title="指定结论标题")
    conclusion_has_subsections: Optional[bool] = Field(None, title="结论是否分小节")
    conclusion_title_has_number: Optional[bool] = Field(None, title="结论标题是否加序号")
    h1_numbering_rule: Optional[str] = Field(None, title="一级标题编号规则")
    h2_numbering_rule: Optional[str] = Field(None, title="二级标题编号规则")
    h3_numbering_rule: Optional[str] = Field(None, title="三级标题编号规则")
    main_content_start_number: Optional[int] = Field(None, title="正文部分一级标题起始序号")

    remarks: Optional[str] = Field(None, description="备注信息")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_plan.jinja"
        if self.remarks is None:
            self.remarks = self.gen_remarks()

    def gen_remarks(self):
        remarks = ""
        if self.introduction_has_subsections is not None:
            if self.introduction_has_subsections:
                remarks += "绪论部分必须分小节\n"
            else:
                remarks += "绪论部分禁止包含二级节点\n"

        if self.conclusion_has_subsections is not None:
            if self.conclusion_has_subsections:
                remarks += "结论部分必须生成二级节点，但禁止生成三级节点\n"
            else:
                remarks += "结论部分禁止包含二级节点\n"

        if self.introduction_title:
            remarks += f"绪论部分一级节点标题必须是“{self.introduction_title}”\n"

        if self.conclusion_title:
            remarks += f"结论部分一级节点标题必须是“{self.conclusion_title}”\n"
        return remarks if len(remarks) > 0 else "无"

    @staticmethod
    async def construct_input_with_retrieval(major, subject, keywords, summary: str,
                                             word_count: int | None, outline_level: int,
                                             introduction_title: Optional[str] = None,
                                             introduction_has_subsections: Optional[bool] = None,
                                             introduction_title_has_number: Optional[bool] = None,
                                             conclusion_title: Optional[str] = None,
                                             conclusion_has_subsections: Optional[bool] = None,
                                             conclusion_title_has_number: Optional[bool] = None,
                                             h1_numbering_rule: Optional[str] = None,
                                             h2_numbering_rule: Optional[str] = None,
                                             h3_numbering_rule: Optional[str] = None,
                                             main_content_start_number: Optional[int] = None
                                             ) -> "GeneratePlanAgentInput":
        gen_plan_input = GeneratePlanAgentInput(
            subject=subject,
            major=major,
            keywords=keywords,
            summary=summary,
            word_count=str(word_count) if word_count else "20000",
            generate_third_level='是' if outline_level == 3 else '否',
            introduction_title=introduction_title,
            introduction_has_subsections=introduction_has_subsections,
            introduction_title_has_number=introduction_title_has_number,
            conclusion_title=conclusion_title,
            conclusion_has_subsections=conclusion_has_subsections,
            conclusion_title_has_number=conclusion_title_has_number,
            h1_numbering_rule=h1_numbering_rule,
            h2_numbering_rule=h2_numbering_rule,
            h3_numbering_rule=h3_numbering_rule,
            main_content_start_number=main_content_start_number
        )
        max_retries = 3
        for _ in range(max_retries):
            try:
                example_plans = await RetrieveService().search_thesis_writing_plan(major, subject, keywords, summary, 2,
                                                                                   0)
                for example_plan in example_plans or []:
                    exp_input = GeneratePlanAgentInput(
                        subject=example_plan[1].title,
                        major=example_plan[1].major,
                        keywords=example_plan[1].keywords,
                        summary=example_plan[1].summary
                    )

                    temp_dict = {
                        "analysis": "....",
                        "writing_plan_nodes": json.dumps(
                            convert_to_writing_plan_nodes(markdown_to_json.dictify(example_plan[1].writing_plan)),
                            ensure_ascii=False)
                    }

                    gen_plan_input.append_example(exp_input.to_msg()
                                                  + "\n\n## 结果\n" + json.dumps(temp_dict, ensure_ascii=False))
                return gen_plan_input
            except Exception as e:
                logger.exception(e)
        raise RuntimeError("Failed to search plan after retries")


class GeneratePlanNode(BaseAgentResponse):
    node_analysis: Optional[str] = Field(None, description="思路分析")
    id: Optional[str] = Field(None, description="节点ID")
    title: str = Field(..., description="标题", min_length=1)
    content_type: Optional[str] = Field(None, description="章类型")
    description: Optional[str] = Field(None, description="写作计划")
    writing_length: Optional[str | int] = Field(None, description="篇幅要求")
    deliverable_ids: Optional[List[str]] = Field(None, description="关联交付物ID")

    children: Optional[List["GeneratePlanNode"]] = Field(None, description="子节点")

    def get_all_leaf_nodes(self):
        leaf_nodes = []
        if self.children:
            for node in self.children:
                leaf_nodes.extend(node.get_all_leaf_nodes())
        else:
            leaf_nodes.append(self)

        return leaf_nodes

    def get_writing_plan_str(self):
        return self.model_dump_json(exclude_none=True)

    def generate_node_id(self, index: int, parent_id: int = None):
        self.id = f"{parent_id}.{index + 1}" if parent_id is not None else str(index + 1)

        if self.children:
            for i, node in enumerate(self.children):
                node.generate_node_id(i, self.id)

    def get_all_node_ids(self):
        node_ids = [self.id]
        if self.children:
            for node in self.children:
                node_ids.extend(node.get_all_node_ids())
        return node_ids


class GeneratePlanAgentResponse(BaseAgentResponse):
    analysis: Optional[str] = Field(None, description="思路")
    writing_plan_nodes: List[GeneratePlanNode] = Field(None, description="写作计划")

    def generate_node_id(self):
        for i, node in enumerate(self.writing_plan_nodes):
            node.generate_node_id(i)

    def get_writing_plan_str(self):
        result_str = ''
        for node in self.writing_plan_nodes or []:
            result_str += node.get_writing_plan_str() + '\n'
        return result_str

    def get_chapter_writing_plans(self) -> List[GeneratePlanNode]:
        return [
            node for node in self.writing_plan_nodes or []
            if node.content_type == WritingPlanContentType.Chapter.value
        ]

    def get_introduction_writing_plan(self) -> GeneratePlanNode:
        for plan_node in self.writing_plan_nodes or []:
            if plan_node.content_type == WritingPlanContentType.Introduction.value:
                return plan_node

    def get_conclusion_writing_plan(self) -> GeneratePlanNode:
        for plan_node in self.writing_plan_nodes or []:
            if plan_node.content_type == WritingPlanContentType.Conclusion.value:
                return plan_node

    def _round_by_level(self, n: float, level: int) -> int:
        """"
        辅助函数：根据节点深度进行取整。
        - level 1: 取整到最近的 100
        - level 2: 取整到最近的 50
        - level 3 及以上: 取整到最近的 10
        """
        if level == 3:
            return int(round(n / 10.0)) * 10
        elif level == 2:
            return int(round(n // 50.0)) * 50
        else:
            return int(round(n / 100.0)) * 100

    def _adjust_hierarchically_and_round(self, nodes: List[GeneratePlanNode], target_level_length: int, level: int):
        """
        递归函数：自上而下，分层按比例调整，并根据深度取整。
        """
        if not nodes:
            return

        current_level_original_sum = sum(int(node.writing_length or 0) for node in nodes)

        if current_level_original_sum == 0:
            logger.warning(f"Level {level} sum is zero, cannot distribute {target_level_length}. Skipping.")
            return

        local_ratio = target_level_length / current_level_original_sum

        for node in nodes:
            try:
                original_node_length = int(node.writing_length)
                adjusted_length = original_node_length * local_ratio

                rounded_length = self._round_by_level(adjusted_length, level)
                node.writing_length = str(rounded_length)

                if node.children:
                    # 递归调用时，深度+1
                    self._adjust_hierarchically_and_round(node.children, rounded_length, level + 1)

            except (ValueError, TypeError):
                logger.warning(f"Node '{node.title}' (Level {level}) has invalid length. Skipping.")

    def adjust_plan_writing_length(self, writing_length: str):
        """
        主方法：启动自上而下的分层调整，初始深度为1。
        """
        try:
            writing_length = int(writing_length)
        except ValueError:
            logger.warning(f"Invalid writing length: {writing_length}. Skipping.")
            return
        self._adjust_hierarchically_and_round(self.writing_plan_nodes, writing_length, level=1)

    def clear_node_children(self, content_type:str):
        for node in self.writing_plan_nodes or []:
            if node.content_type == content_type:
                node.children = []
                break


class GeneratePlanAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions], education_level):
        if education_level == EducationLevel.Degree:
            self.system_message_template_path = "system_prompt/gen_plan_degree.jinja"
        elif education_level == EducationLevel.Bachelor:
            self.system_message_template_path = "system_prompt/gen_plan_bachelor.jinja"
        elif education_level == EducationLevel.Graduate:
            self.system_message_template_path = "system_prompt/gen_plan_graduate.jinja"
        self.input_type = GeneratePlanAgentInput
        self.output_type = GeneratePlanAgentResponse
        super().__init__(options, failover_options_list)

    def validate_output(self, input: GeneratePlanAgentInput, output: GeneratePlanAgentResponse, remaining_retries=0):
        if not output or not output.writing_plan_nodes:
            raise ValueError("Writing plan nodes are empty or None")
        output.writing_plan_nodes = filter_plan_node_title(output.writing_plan_nodes)
        if input.introduction_title:
            output.get_introduction_writing_plan().title = input.introduction_title
        if input.conclusion_title:
            output.get_conclusion_writing_plan().title = input.conclusion_title
        fix_title_numbering(input, output.writing_plan_nodes)
        check_sections(input, output)
        if not check_writing_plan_content_type(output):
            raise ValueError("Writing Plan Content Type Error")
        clean_duplicate_deliverable_ids(output.writing_plan_nodes)
        output.generate_node_id()

def filter_plan_node_title(writing_plan_nodes):
    new_writing_plan_nodes = []
    for node in writing_plan_nodes:
        current_title = remove_prefix_number(node.title)
        if current_title in ['参考文献', '致谢', '附录']:
            continue
        new_writing_plan_nodes.append(node)
    return new_writing_plan_nodes

def clean_duplicate_deliverable_ids(writing_plan_nodes):
    if not writing_plan_nodes:
        return

    seen_ids = set()

    def traverse(node: GeneratePlanNode):
        if node.deliverable_ids:
            unique_for_node = []
            for deliverable_id in node.deliverable_ids:
                if deliverable_id in seen_ids:
                    continue
                if deliverable_id not in unique_for_node:
                    unique_for_node.append(deliverable_id)
                    seen_ids.add(deliverable_id)
            node.deliverable_ids = unique_for_node or None
        for child in (node.children or []):
            traverse(child)

    for root in writing_plan_nodes:
        traverse(root)

def remove_prefix_number(title):
    title = title.replace(' ', '')
    pattern = r'^(第[一二三四五六七八九十]+章|[' \
                r'一二三四五六七八九十]+\.|\d+[\.\s]|' \
                r'[0-9]+)\s*'
    match = re.match(pattern, title)
    if match:
        return title[match.end():].strip()
    return title.strip()


def convert_to_writing_plan_nodes(ordered_dict):
    def merge_subsection_content(content):
        if isinstance(content, str):
            return content
        if isinstance(content, (dict, OrderedDict)):
            merged = []
            for _, sub_content in content.items():
                merged.append(merge_subsection_content(sub_content))
            return " ".join(filter(None, merged))
        return ""

    result = []

    for chapter_key, chapter_content in ordered_dict.items():
        chapter_node = {
            "title": str(chapter_key),
            "description": ""
        }

        # 处理子节点（小节）
        if isinstance(chapter_content, (dict, OrderedDict)):
            for section_key, section_content in chapter_content.items():
                merged_description = merge_subsection_content(section_content)
                section_node = {
                    "title": str(section_key),
                    "description": str(merged_description)
                }

                if chapter_node.get("children") is None:
                    chapter_node["children"] = []
                chapter_node["children"].append(section_node)
        elif isinstance(chapter_content, str):
            # 如果章节内容直接是字符串，创建一个子节点
            section_node = {
                "title": "概述",
                "description": str(chapter_content)
            }

            if chapter_node.get("children") is None:
                chapter_node["children"] = []
            chapter_node["children"].append(section_node)
        result.append(chapter_node)
    return result
