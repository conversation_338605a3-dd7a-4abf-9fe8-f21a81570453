from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class GenerateThesisProposalAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    toc: Optional[str] = Field(..., description="开题报告目录", min_length=1)
    summary: Optional[str] = Field(..., description="全文概述")
    reference: Optional[str] = Field(..., description="参考文献")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_thesis_proposal.jinja"


class GenerateThesisProposalChapter(BaseAgentResponse):
    chapter_analysis: Optional[str] = Field(None, description="分析")
    chapter_title: Optional[str] = Field(None, description="标题")
    chapter_content: Optional[str] = Field(None, description="具体内容")
    chart: Optional[str] = Field(None, description="图表")


class GenerateThesisProposalAgentResponse(BaseAgentResponse):
    thesis_proposal: Optional[list[GenerateThesisProposalChapter]] = Field(None, description="章节")

    def main_content(self):
        return '\n'.join([f"{chapter.chapter_title}\n{chapter.chapter_content}" for chapter in self.thesis_proposal])


class GenerateThesisProposalAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_thesis_proposal.jinja"
        self.input_type = GenerateThesisProposalAgentInput
        self.output_type = GenerateThesisProposalAgentResponse
        super().__init__(options, failover_options_list)
