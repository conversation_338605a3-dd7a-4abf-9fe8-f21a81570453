import json
from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class GenerateThesisProposalQueryAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    summary: str = Field(..., description="全文概述", min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_thesis_proposal_query.jinja"

    @staticmethod
    def outline_to_toc(outline: str):
        return '\n'.join(item['title'] for item in json.loads(outline))


class GenerateThesisProposalQueryAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="分析")
    queries: Optional[list[str]] = Field(None, description="查询语句")


class GenerateThesisProposalQueryAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_thesis_proposal_query.jinja"
        self.input_type = GenerateThesisProposalQueryAgentInput
        self.output_type = GenerateThesisProposalQueryAgentResponse
        super().__init__(options, failover_options_list)
