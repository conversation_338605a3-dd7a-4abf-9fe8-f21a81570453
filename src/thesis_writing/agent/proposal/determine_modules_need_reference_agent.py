from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class DetermineModulesNeedReferenceAgentInput(BaseAgentInput):
    toc: Optional[str] = Field(..., description="开题报告目录", min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/determine_modules_need_reference.jinja"


class DetermineModulesNeedReferenceAgentResponse(BaseAgentResponse):
    need_reference_modules: Optional[List[str]] = Field(None, description="需要文献的模块列表")


class DetermineModulesNeedReferenceAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/determine_modules_need_reference.jinja"
        self.input_type = DetermineModulesNeedReferenceAgentInput
        self.output_type = DetermineModulesNeedReferenceAgentResponse
        super().__init__(options, failover_options_list)
