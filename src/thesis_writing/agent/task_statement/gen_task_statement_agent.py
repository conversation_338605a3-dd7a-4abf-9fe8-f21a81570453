import json
from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class GenerateTaskStatementAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    toc: str = Field(..., description="任务书目录", min_length=1)
    keywords: Optional[str] = Field(..., description="关键词")
    summary: Optional[str] = Field(..., description="全文概述")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_task_statement.jinja"

    @staticmethod
    def outline_to_toc(outline: str):
        outline_items = [{'title': item['title']} for item in json.loads(outline)]
        group_size = 5
        num_items = len(outline_items)
        grouped_items = [outline_items[i:i + group_size] for i in range(0, num_items, group_size)]
        result = [json.dumps(group, ensure_ascii=False, indent=2) for group in grouped_items]
        return result


class GenerateTaskStatementContent(BaseAgentResponse):
    background: Optional[str] = Field(None, description="论文研究背景")
    meaning: Optional[str] = Field(None, description="论文研究意义")
    target: Optional[str] = Field(None, description="论文研究对象")
    key_research: Optional[str] = Field(None, description="论文研究问题")
    assumption: Optional[str] = Field(None, description="论文研究假设")
    method: Optional[str] = Field(None, description="论文研究方法")
    conclusion: Optional[str] = Field(None, description="论文预期结论")
    writing_framework: Optional[str] = Field(None, description="论文写作框架")


class GenerateTaskStatementChapter(BaseAgentResponse):
    title: Optional[str] = Field(None, description="标题")
    thought: Optional[str] = Field(None, description="思路")
    chapter_content: Optional[str] = Field(None, description="具体内容")


class GenerateTaskStatementAgentResponse(BaseAgentResponse):
    content: Optional[GenerateTaskStatementContent] = Field(None, description="内容")
    task_statement: Optional[list[GenerateTaskStatementChapter]] = Field(None, description="章节")


class GenerateTaskStatementAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_task_statement.jinja"
        self.input_type = GenerateTaskStatementAgentInput
        self.output_type = GenerateTaskStatementAgentResponse
        super().__init__(options, failover_options_list)
