from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentInput, BaseAgentResponse


class RefineReferenceAgentInput(BaseAgentInput):
    reference: str = Field(..., description="标题", min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/refine_reference.jinja"


class RefineReferenceAgentResponse(BaseAgentResponse):
    type: Optional[str] = Field(None, description="文献类型")
    authors: Optional[str] = Field(None, description="作者")
    title: Optional[str] = Field(None, description="题目")
    year: Optional[str] = Field(None, description="出版年份|报告年份")
    address: Optional[str] = Field(None, description="出版地|保存地|报告地")
    unit: Optional[str] = Field(None, description="出版单位|保存单位|主办单位")
    journal_name: Optional[str] = Field(None, description="期刊名")
    reel_number: Optional[str] = Field(None, description="卷号")
    period_num: Optional[str] = Field(None, description="期数")
    page_num: Optional[str] = Field(None, description="起止页码")
    newspapers_name: Optional[str] = Field(None, description="报纸名")
    date: Optional[str] = Field(None, description="出版日期")
    edition: Optional[str] = Field(None, description="版次")
    editor_name: Optional[str] = Field(None, description="主编")
    collection_title: Optional[str] = Field(None, description="论文集名")
    reference_date: Optional[str] = Field(None, description="引用日期")
    access_path: Optional[str] = Field(None, description="访问路径")


class RefineReferenceAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/refine_reference.jinja"
        self.input_type = RefineReferenceAgentInput
        self.output_type = RefineReferenceAgentResponse
        super().__init__(options, failover_options_list)
