import re
from datetime import datetime
from typing import Optional, List, Dict

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput, ChainRunnableConfig
from thesis_writing.agent.gen_plan_addition_agent import PlanAddition, BasePlanAddition
from thesis_writing.utils.reference import ThesisReference, ReferenceManager, split_strict_nested_brackets
from thesis_writing.utils.template_parser import get_template


class GenerateSegmentAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    segment_title: Optional[str] = Field(None, description="当前目标小节标题")
    segment_writing_plan: str = Field(..., description="当前目标小节写作计划", min_length=1)
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)
    materials: Optional[Dict[str, List[str]]] = Field(None, description="检索结果")
    materials_str: Optional[str] = Field(None, description="检索结果")
    additions: Optional[List[PlanAddition]] = Field(None, description="补充信息")
    context: Optional[str] = Field(None, description="本章已完成小节内容")
    references: Optional[List[ThesisReference]] = Field(None, description="参考文献")

    global_entities: Optional[List[str]] = Field(None, description="论文研究对象实体")
    global_entities_str: Optional[str] = Field(None, description="全局实体信息")

    common_global_data_str: Optional[str] = Field(None, description="全局数据信息")
    empirical_analysis_data_str: Optional[str] = Field(None, description="实证分析数据")

    def to_msg(self):
        kwargs = self.model_dump(exclude={"references", "additions"})
        if not self.references or len(self.references) == 0:
            kwargs["references"] = '无'
        else:
            kwargs["references"] = "\n".join([reference.to_detail_str() for reference in self.references])

        segment_additions_str = ''
        for each in self.additions or []:
            segment_additions_str += f"<addition>\n{each.to_chat_string()}\n</addition>\n"
        if len(segment_additions_str) == 0:
            segment_additions_str = "无"
        kwargs["additions"] = segment_additions_str

        return get_template(self.user_message_template_path, **kwargs)

    def _replace_figures_and_tables(self, text):
        pattern = r'(图|表)[\.\s\d-]+'
        result = re.sub(pattern, lambda match: match.group(1), text)
        return result

    def _remove_markdown(self, text):
        pattern = r'^\s*#+\s.*?$'
        result = re.sub(pattern, '', text, flags=re.MULTILINE)
        result = result.replace('**', '')
        return result.strip()

    def _remove_references(self, text):
        pattern = r'\[\d+\]'
        result = re.sub(pattern, '', text)
        return result

    def __init__(self, **data):
        super().__init__(**data)
        if self.empirical_analysis_data_str:
            self.user_message_template_path = "user_prompt/gen_segment_with_empirical.jinja"
        else:
            self.user_message_template_path = "user_prompt/gen_segment.jinja"

        if self.materials:
            self.materials_str = ""
            for material_type, materials in self.materials.items():
                temp_str = f"<{material_type}_materials>\n{"\n\n".join(materials)}\n</{material_type}_materials>\n"
                temp_str = self._replace_figures_and_tables(temp_str)
                temp_str = self._remove_markdown(temp_str)
                temp_str = self._remove_references(temp_str)

                self.materials_str += temp_str + "\n"

        if self.global_entities:
            self.global_entities_str = f"<entities>\n{'\n\n'.join(self.global_entities)}\n</entities>"


class Reference(BaseModel):
    id: str = Field(None, description="引用论文的序号")
    name: str = Field(None, description="引用论文的名称")
    author: str = Field(None, description="引用论文的作者")
    usage: str = Field(None, description="如何使用")


class GenerateSegmentAgentResponse(BaseAgentResponse):
    thought: str = Field(None, description="思路")
    material_thought: Optional[str] = Field(None, description="资料思路")
    reference_thought: Optional[str] = Field(None, description="参考文献思路")
    references: Optional[List[Reference]] = Field(None, description="参考文献")
    empirical_analysis_thought: Optional[str] = Field(None, description="实证分析报告使用思路")
    segment_content: str = Field(..., description="节内容", min_length=1)


class GenerateSegmentAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.input_type = GenerateSegmentAgentInput
        self.output_type = GenerateSegmentAgentResponse
        super().__init__(options, failover_options_list)
        # 设置默认模板路径
        self.default_template_paths = {
            'with_empirical': "system_prompt/gen_segment_with_empirical.jinja",
            'without_empirical': "system_prompt/gen_segment.jinja"
        }

    def _get_template_path(self, has_empirical_data: bool) -> str:
        """子类可以重写此方法来自定义模板路径"""
        return self.default_template_paths['with_empirical' if has_empirical_data else 'without_empirical']

    def _prepare_template_path(self, agent_input: GenerateSegmentAgentInput):
        """
        核心逻辑：准备模板路径。
        通过检查 self.system_message_template_path 是否为字符串来判断其是否已被有效设置。
        """
        # 检查路径是否已经是一个有效的字符串。如果不是，就根据输入进行设置。
        path_is_set = hasattr(self, 'system_message_template_path') and isinstance(self.system_message_template_path,
                                                                                   str)

        if not path_is_set:
            self.system_message_template_path = self._get_template_path(
                bool(agent_input.empirical_analysis_data_str)
            )


    def invoke(self, agent_input: GenerateSegmentAgentInput, config: ChainRunnableConfig = None) \
            -> GenerateSegmentAgentResponse:
        # 调用准备方法来设置模板路径
        self._prepare_template_path(agent_input)

        result: GenerateSegmentAgentResponse = super().invoke(agent_input, config)
        self._repair_segment_content(agent_input, result)
        return result

    async def ainvoke(self, agent_input: GenerateSegmentAgentInput, config: ChainRunnableConfig = None) \
            -> GenerateSegmentAgentResponse:
        # 调用准备方法来设置模板路径
        self._prepare_template_path(agent_input)

        result: GenerateSegmentAgentResponse = await super().ainvoke(agent_input, config)
        self._repair_segment_content(agent_input, result)
        return result

    def validate_output(self, input, output: GenerateSegmentAgentResponse, remaining_retries=0):
        if remaining_retries > 0:
            bad_words = ['##', '**']
            if any(bad_word in output.segment_content for bad_word in bad_words):
                raise ValueError("Bad words detected in segment content")
        if not self._validate_unescaped_unicode(output.segment_content):
            raise ValueError("Too many unescaped unicode detected in segment content")

    def _validate_unescaped_unicode(self, text):
        total_chars = len(text)
        if total_chars == 0:
            return False
        escaped_unicode_pattern = re.compile(r'(\\u[0-9a-fA-F]{4}|\\U[0-9a-fA-F]{8}|\\x[0-9a-fA-F]{2})')
        escaped_count = len(escaped_unicode_pattern.findall(text))
        return escaped_count < 20

    def _get_invalid_chart_or_table(self, segment_content, additions):
        additions_ids = {each.id for each in additions} if additions else set()
        invalid_chart_or_table = [
            match for match in re.findall(r"<(chart|figure|table|addition) id=\"(.*?)\"", segment_content)
            if match[1] not in additions_ids
        ]
        return invalid_chart_or_table

    def _repair_segment_content(self, agent_input: GenerateSegmentAgentInput, result: GenerateSegmentAgentResponse):
        if not result or not result.segment_content:
            return
        result.segment_content = remove_invalid_tags(result.segment_content, agent_input.additions)
        result.segment_content = correct_addition_tags(result.segment_content, agent_input.additions)
        result.segment_content = remove_duplicate_tags(result.segment_content)
        result.segment_content = fix_reference_tags(result.segment_content, agent_input.references)
        result.segment_content = replace_quotes(result.segment_content)


def correct_addition_tags(text: str, additions: List[BasePlanAddition]):
    segment_content = text
    tag_types = ["table", "image", "figure", "addition", "chart"]
    for addition in additions or []:
        if not addition.id:
            continue
        correct_tag = f'<chart id="{addition.id}"/>'
        incorrect_tags = [f'<{tag} id="{addition.id}"/>' for tag in tag_types]
        incorrect_tags += [
            re.compile(rf'<{tag} id="{addition.id}"[^>]*>.*?</{tag}>', re.DOTALL)
            for tag in tag_types
        ]
        for incorrect in incorrect_tags:
            if isinstance(incorrect, str):
                segment_content = segment_content.replace(incorrect, correct_tag)
            else:
                segment_content = incorrect.sub(correct_tag, segment_content)
        segment_content = adjust_chart_position(segment_content, correct_tag, addition.id)

    return segment_content


def remove_invalid_tags(
        text: str,
        additions: List[BasePlanAddition],
):
    segment_content = text
    invalid_chart_or_table = get_invalid_chart_or_table(
        segment_content, additions
    )
    for tag, id in invalid_chart_or_table:
        segment_content = re.sub(
            rf'<{tag} id="{id}"[^>]*/>', "", segment_content
        )
        segment_content = re.sub(
            rf'<{tag} id="{id}"[^>]*>.*?</{tag}>', "", segment_content
        )

    return (
        segment_content.replace("**", "")
        .replace('<addition id="None"/>', "")
        .replace('<addition id="None"></addition>', "")
        .replace("<addition>None</addition>", "")
    )


def get_invalid_chart_or_table(segment_content, additions: List[BasePlanAddition]):
    additions_ids = {each.id for each in additions} if additions else set()
    invalid_chart_or_table = [
        match
        for match in re.findall(
            r"<(chart|figure|table|addition) id=\"(.*?)\"", segment_content
        )
        if match[1] not in additions_ids
    ]
    return invalid_chart_or_table


def remove_duplicate_tags(segment_content: str):
    seen_tags = set()

    def replace_tag(match):
        tag = match.group(0)
        if tag in seen_tags:
            return ""
        seen_tags.add(tag)
        return tag

    return re.sub(r'<chart id="[^"]+"/>', replace_tag, segment_content)


def adjust_chart_position(text: str, correct_tag: str, addition_id: str):
    segment_content = text
    segment_content = segment_content.replace(f'{correct_tag}。', f'。{correct_tag}').replace(
        f'{correct_tag}.', f'.{correct_tag}')
    if correct_tag not in segment_content:
        if addition_id in segment_content:
            chart_index = segment_content.find(addition_id)
            if chart_index != -1:
                period_index = segment_content.find("。", chart_index)
                if period_index != -1:
                    segment_content = (
                        segment_content[:period_index + 1] + correct_tag + segment_content[period_index + 1:]
                    )
                else:
                    segment_content += correct_tag
            else:
                segment_content += correct_tag
        else:
            segment_content += correct_tag

    return segment_content


def fix_reference_tags(text: str, references: List[ThesisReference]):
    segment_content = text
    reference_manager = ReferenceManager()
    reference_manager.extend_references(references)
    segment_content = split_strict_nested_brackets(segment_content)

    for ref in references or []:
        tag, wrong_tag = f'[[{ref.id}]]', f'[{ref.id}]'
        if tag not in segment_content and wrong_tag in segment_content:
            segment_content = segment_content.replace(wrong_tag, tag)
    _, not_found_ids = reference_manager.collect_ref(segment_content)

    for each in not_found_ids or []:
        segment_content = segment_content.replace(f'[[{each}]]', '')

    return segment_content


def replace_quotes(text: str):
    chars = []
    inside_double_quotes = False
    single_quote_indices = []

    for i, char in enumerate(text):
        if char == '“':
            inside_double_quotes = True
            chars.append(char)
        elif char == '”':
            inside_double_quotes = False
            chars.append(char)
        elif char == '‘' and not inside_double_quotes:
            single_quote_indices.append(i)
            chars.append(char)
        elif char == '’' and not inside_double_quotes:
            if single_quote_indices:
                left_index = single_quote_indices.pop()
                if len(single_quote_indices) == 0:
                    chars[left_index] = '“'
                    chars.append('”')
                else:
                    chars.append(char)
            else:
                chars.append(char)
        else:
            chars.append(char)

    return "".join(chars)
