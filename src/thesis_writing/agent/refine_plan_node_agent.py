from datetime import datetime
from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentInput, BaseAgentResponse


class RefinePlanNodeAgentInput(BaseAgentInput):
    subject: Optional[str] = Field(None, description="标题")
    major: Optional[str] = Field(None, description="专业")
    keywords: Optional[str] = Field(None, description="关键词")
    summary: str = Field(..., description="全文概述", min_length=1)
    writing_plan: str = Field(..., description="写作计划", min_length=1)
    refine_title: str = Field(..., description="润色标题", min_length=1)
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/refine_plan_node.jinja"


class RefinePlanNodeAgentResponse(BaseAgentResponse):
    description: Optional[str] = Field(None, description="润色之后的描述")


class RefinePlanNodeAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/refine_plan_node.jinja"
        self.input_type = RefinePlanNodeAgentInput
        self.output_type = RefinePlanNodeAgentResponse
        super().__init__(options, failover_options_list)
