from datetime import datetime
from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput, ChainRunnableConfig
from thesis_writing.utils.logger import get_logger

logger = get_logger(__name__)


class GenerateAppendixAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    introduction: Optional[str] = Field(None, description="绪论")
    main_content: Optional[str] = Field(None, description="正文")
    user_feed_summaries: Optional[list[str]] = Field(None, description="资料")
    user_feed_summaries_str: Optional[str] = Field(None, description="资料")
    references: Optional[str] = Field(None, description="参考文献")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        if self.user_feed_summaries:
            text = ""
            for n, user_feed_summary in enumerate(self.user_feed_summaries, 1):
                text += f"{n}. {user_feed_summary}\n"
            self.user_feed_summaries_str = text
        self.user_message_template_path = "user_prompt/gen_appendix.jinja"


class GenerateAppendixNode(BaseAgentResponse):
    think: str = Field(description="思考过程")
    title: str = Field(description="标题")
    description: Optional[str] = Field(None, description="附录描述")
    content: Optional[str] = Field(None, description="附录内容")


class GenerateAppendixAgentResponse(BaseAgentResponse):
    appendix: List[GenerateAppendixNode] = Field(description="附录内容")


class GenerateAppendixAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_appendix.jinja"
        self.input_type = GenerateAppendixAgentInput
        self.output_type = GenerateAppendixAgentResponse
        super().__init__(options, failover_options_list)
