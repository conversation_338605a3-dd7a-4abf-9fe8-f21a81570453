from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class GenerateAcknowledgeAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_acknowledge.jinja"


class GenerateAcknowledgeAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="思路")
    acknowledgment: Optional[str] = Field(None, description="致谢内容")


class GenerateAcknowledgeAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_acknowledge.jinja"
        self.input_type = GenerateAcknowledgeAgentInput
        self.output_type = GenerateAcknowledgeAgentResponse
        super().__init__(options, failover_options_list)
