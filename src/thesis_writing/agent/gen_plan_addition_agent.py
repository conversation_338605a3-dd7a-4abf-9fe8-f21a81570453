from datetime import datetime
from typing import Optional, List

from pydantic import Field, BaseModel, ConfigDict

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput
from thesis_writing.utils.logger import get_logger

logger = get_logger(__name__)

chart_type = {
    "表格": ["表格"],
    "数据图": ["折线图", "柱状图", "饼图", "桑基图", "象限图", "散点图", "雷达图"],
    "UML图": ["实体关系图", "用例图", "类图", "时序图", "状态图", "架构图"],
    "流程图": ["流程图"],
    "其他类型的图": ["甘特图", "思维导图", "时间线图", "数据包图", "看板图"]
}


class GeneratePlanAdditionAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    research_entities_str: Optional[str] = Field(None, description="具体研究对象实体")
    writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    global_data_analysis: Optional[str] = Field(None, description="全局数据分析")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_plan_addition.jinja"


class ChartRetrieveInfo(BaseModel):
    retrieve_url: str = Field(..., description="检索链接")
    retrieve_title: str = Field(..., description="检索标题")
    retrieve_content: str = Field(..., description="检索内容")


class BasePlanAddition(BaseModel):
    node_id: Optional[str] = Field(None, description="节点ID")
    id: Optional[str] = Field(None, alias="题注的序号", description="题注的序号")


class PlanAddition(BasePlanAddition):
    model_config = ConfigDict(populate_by_name=True)
    first_level_type: Optional[str] = Field(None, alias="图表一级类型", description="图表一级类型")
    type: str = Field(..., alias="类型", description="类型")
    title: str = Field(..., alias="名称", description="名称")
    description: str = Field(..., alias="描述", description="描述")
    purpose: str = Field(..., alias="目的", description="目的")
    retrieve_url: Optional[str] = Field(None, description="检索链接")
    retrieve_title: Optional[str] = Field(None, description="检索标题")
    retrieve_content: Optional[str] = Field(None, description="检索内容")

    def to_chat_string(self, user_feed_materials: List[tuple[str, List]] = None) -> str:
        retrieve_info = "无"
        if self.retrieve_url and self.retrieve_title and self.retrieve_content:
            retrieve_info = f'【网络资料】\n根据{self.retrieve_title}查询到与{self.title}相关的内容：{self.retrieve_content}'

        if user_feed_materials:
            retrieve_info += "\n【用户投喂资料】\n"
            for query, chunks in user_feed_materials:
                retrieve_info += "\n".join(
                    [f"- {query}：{chunk.refine_content or chunk.content}" for chunk in chunks or []])

        return f"""\
ID：{self.id}
图表类型:{self.type}
标题：{self.title}
描述：{self.description}
目的：{self.purpose}
相关资料：{retrieve_info or "无"}
"""


def parse_node_id(node_id: str) -> Optional[List[int]]:
    try:
        return [int(part) for part in node_id.split(".")]
    except ValueError:
        return None


def sort_plans_by_node_id(plans: List[PlanAddition]) -> List[PlanAddition]:
    """
    按 node_id 排序 PlanAddition 实例列表，丢弃解析失败的记录。
    """
    valid_plans = [
        plan for plan in plans if parse_node_id(plan.node_id) is not None
    ]
    return sorted(valid_plans, key=lambda x: parse_node_id(x.node_id))


class GeneratePlanAdditionAgentResponse(BaseAgentResponse):
    step1: Optional[str] = Field(None, description="思考步骤1")
    step2: Optional[str] = Field(None, description="思考步骤2")
    additions: List[PlanAddition] = Field([], description="图表计划信息")

    def find_additions_by_node_id(self, node_id: str) -> List[PlanAddition]:
        result = []
        for addition in self.additions:
            if addition.node_id == node_id:
                result.append(addition)
        return result


class GenerateCommonPlanAdditionAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        super().__init__(options, failover_options_list)

    def deserialize_parser_output(self, parser_output: str) -> GeneratePlanAdditionAgentResponse:
        output = GeneratePlanAdditionAgentResponse.model_validate(parser_output)
        output.additions = [plan for plan in output.additions if plan.first_level_type in chart_type.keys()]

        output.additions = sort_plans_by_node_id(output.additions)
        current_chapter_num = 0
        current_table_sequence = 1
        current_image_sequence = 1
        for each in output.additions:
            chapter_num = int(each.node_id.split(".")[0])
            if chapter_num != current_chapter_num:
                current_chapter_num = chapter_num
                current_table_sequence = 1
                current_image_sequence = 1
            if '表' in each.type:
                each.id = f"表{current_chapter_num}-{current_table_sequence}"
                current_table_sequence += 1
            else:
                each.id = f"图{current_chapter_num}-{current_image_sequence}"
                current_image_sequence += 1
        return output


class GeneratePlanAdditionAgent(GenerateCommonPlanAdditionAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_plan_addition.jinja"
        self.input_type = GeneratePlanAdditionAgentInput
        self.output_type = GeneratePlanAdditionAgentResponse
        super().__init__(options, failover_options_list)
