from typing import Optional, List, Dict

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput
from thesis_writing.agent.gen_plan_addition_agent import PlanAddition


class MaterialInput(BaseModel):
    id: str = Field(..., description="资料ID")
    source: str = Field(..., description="资料来源")
    summary: str = Field(..., description="资料简介")


class GenerateUserFeedQueryAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: str = Field(None, description="关键词")
    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    global_entities: Optional[List[str]] = Field(None, description="论文研究对象实体")
    global_entities_str: Optional[str] = Field(None, description="全局实体信息")

    current_section_plan: str = Field(..., description="当前目标小节写作计划", min_length=1)
    current_section_plan_additions: Optional[List[PlanAddition]] = Field(None, description="当前目标小节图表计划")
    segment_additions_str: str = Field("无", description="当前目标小节图表计划")

    materials: List[MaterialInput] = Field(..., description="资料概要清单", min_length=1)
    materials_str: str = Field(None, description="资料概要清单")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_query_of_user_feed.jinja"

        if self.current_section_plan_additions:
            self.segment_additions_str = ""
            for each in self.current_section_plan_additions:
                self.segment_additions_str += f"<addition>\n{each.to_chat_string()}\n</addition>\n"

        if self.global_entities:
            self.global_entities_str = f"<entities>\n{'\n\n'.join(self.global_entities)}\n</entities>"

        self.materials_str = "\n\n".join([f"- 资料 \nID: {summary.id} \n来源: {summary.source} \n概要: {summary.summary}"
                                          for summary in self.materials if summary.summary])


class MaterialQuery(BaseModel):
    material_thought: str = Field(..., description="思考分析资料是否有助于当前小节或小节图表的写作")
    material_id: str = Field(..., description="资料ID")
    queries_for_content: List[str] = Field(default_factory=list, description="用于内容写作的查询语句列表")
    queries_for_chart: List[str] = Field(default_factory=list, description="用于图表生成的查询语句列表")


class GenerateUserFeedQueryAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="思路")
    materials: List[MaterialQuery] = Field(..., description="资料查询列表")


class GenerateUserFeedQueryAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_query_of_user_feed.jinja"
        self.input_type = GenerateUserFeedQueryAgentInput
        self.output_type = GenerateUserFeedQueryAgentResponse
        super().__init__(options, failover_options_list)
