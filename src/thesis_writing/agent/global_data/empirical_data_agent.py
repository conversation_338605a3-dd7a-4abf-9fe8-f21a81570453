from datetime import datetime
from typing import Optional, List

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class EmpiricalAnalysisPlan(BaseModel):
    research_question: str = Field(..., description="研究问题")
    hypotheses: Optional[List[str]] = Field(None, description="研究假设列表（可选）")
    data_requirements: List[str] = Field(..., description="数据需求")
    analysis_methods: List[str] = Field(...,
                                        description="要使用的分析方法列表，例如：'描述性统计', '回归分析', 't检验', '方差分析', '聚类分析', '时间序列分析'")
    expected_outcomes: str = Field(None, description="预期结果的描述")
    visualization_methods: List[str] = Field(..., description="可视化方法")


class GenEmpiricalAnalysisPlanAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/global_data/analysis_empirical_data_plan.jinja"


class GenEmpiricalAnalysisPlanAgentResponse(BaseAgentResponse):
    thought: str = Field(None, description="思路")
    need_empirical_analysis: bool = Field(..., description="是否需要进行实证分析")
    empirical_analysis_plan: Optional[EmpiricalAnalysisPlan] = Field(..., description="实证分析计划")


class GenEmpiricalAnalysisPlanAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/global_data/analysis_empirical_data_plan.jinja"
        self.input_type = GenEmpiricalAnalysisPlanAgentInput
        self.output_type = GenEmpiricalAnalysisPlanAgentResponse
        super().__init__(options, failover_options_list)


class GenEmpiricalAnalysisQueryAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    research_entities_str: Optional[str] = Field(None, description="研究实体")
    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    user_feeds_overview: Optional[str] = Field(None, description="用户反馈的概要")
    empirical_analysis_plan: EmpiricalAnalysisPlan = Field(..., description="实证分析计划")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/global_data/analysis_empirical_data_query.jinja"


class GenEmpiricalAnalysisQueryAgentResponse(BaseAgentResponse):
    thought: str = Field(None, description="思路")
    queries: List[str] = Field(None, description="查询")


class GenEmpiricalAnalysisQueryAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/global_data/analysis_empirical_data_query.jinja"
        self.input_type = GenEmpiricalAnalysisQueryAgentInput
        self.output_type = GenEmpiricalAnalysisQueryAgentResponse
        super().__init__(options, failover_options_list)


class ExtractEmpiricalAnalysisQueryResultAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    empirical_analysis_plan: EmpiricalAnalysisPlan = Field(..., description="实证分析计划")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)
    query_result: str = Field(..., description="实证分析查询结果", min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/global_data/extract_empirical_data_query_result.jinja"


class ExtractEmpiricalAnalysisQueryResultAgentResponse(BaseAgentResponse):
    thought: str = Field(None, description="思路")
    content: str = Field(None, description="实证分析查询结果")


class ExtractEmpiricalAnalysisQueryResultAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/global_data/extract_empirical_data_query_result.jinja"
        self.input_type = ExtractEmpiricalAnalysisQueryResultAgentInput
        self.output_type = ExtractEmpiricalAnalysisQueryResultAgentResponse
        super().__init__(options, failover_options_list)


class FakeEmpiricalAnalysisDataAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    research_entities_str: Optional[str] = Field(None, description="研究实体")
    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    empirical_analysis_plan: EmpiricalAnalysisPlan = Field(..., description="实证分析计划")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)
    query_result: str = Field(..., description="实证分析查询结果", min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/global_data/fake_empirical_data.jinja"


class FakeEmpiricalAnalysisDataAgentResponse(BaseAgentResponse):
    thought: str = Field(None, description="思路")
    content: str = Field(..., description="数据", min_length=1)


class FakeEmpiricalAnalysisDataAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/global_data/fake_empirical_data.jinja"
        self.input_type = FakeEmpiricalAnalysisDataAgentInput
        self.output_type = FakeEmpiricalAnalysisDataAgentResponse
        super().__init__(options, failover_options_list)

