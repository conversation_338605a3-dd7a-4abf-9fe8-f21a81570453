from datetime import datetime
from typing import List, Optional

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentInput, BaseAgentResponse


class AnalysisCommonGlobalDataQueryAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    summary: str = Field(None, description="全文概述")
    research_entities_str: Optional[str] = Field(None, description="研究实体")
    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    user_feeds_overview: Optional[str] = Field(None, description="用户投喂的概要信息")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/global_data/analysis_common_global_data_query.jinja"


class AnalysisCommonGlobalDataQueryResponse(BaseAgentResponse):
    thought: str = Field(None, description="对问题的分析思考过程")
    global_datas: Optional[List[str]] = Field(None, description="需要的核心数据列表")
    web_queries: Optional[List[str]] = Field(None, description="查询语句列表")
    user_feed_queries: Optional[List[str]] = Field(None, description="用户投喂的查询语句列表")

    def get_global_data_desc(self):
        return "\n".join(self.global_datas or [])


class AnalysisCommonGlobalDataQuery(BaseAgent):
    def __init__(self, options, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/global_data/analysis_common_global_data_query.jinja"
        self.input_type = AnalysisCommonGlobalDataQueryAgentInput
        self.output_type = AnalysisCommonGlobalDataQueryResponse
        super().__init__(options, failover_options_list)


class ExtractCommonGlobalDataQueryResultAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="major", min_length=1)
    complete_writing_plan: str = Field(..., description="论文大纲", min_length=1)
    research_entities_str: Optional[str] = Field(None, description="研究对象实体")
    global_data_str: str = Field(..., description="需要的数据信息清单", min_length=1)
    retrieval_materials: list[str] = Field(..., description="召回资料", min_length=1)

    materials_str: str = Field(None, description="资料列表")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/global_data/extract_common_data_query_result.jinja"

        if self.retrieval_materials:
            self.materials_str = f"<material_chunks>\n{"\n\n".join(self.retrieval_materials)}\n</material_chunks>"


class ExtractCommonGlobalDataQueryResultAgentResponse(BaseAgentResponse):
    thought: str = Field(None, description="对问题的分析思考过程")
    useful_content: str = Field(None, description="有用的内容")


class ExtractCommonGlobalDataQueryResultAgent(BaseAgent):
    def __init__(self, options, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/global_data/extract_common_data_query_result.jinja"
        self.input_type = ExtractCommonGlobalDataQueryResultAgentInput
        self.output_type = ExtractCommonGlobalDataQueryResultAgentResponse
        super().__init__(options, failover_options_list)


class SummarizeCommonGlobalDataAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="major", min_length=1)
    complete_writing_plan: str = Field(..., description="论文大纲", min_length=1)
    research_entities_str: Optional[str] = Field(None, description="研究对象实体")
    global_data_str: str = Field(..., description="需要的核心数据列表", min_length=1)
    extracted_content: Optional[str] = Field(None, description="召回资料")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/global_data/summarize_common_global_data.jinja"


class GlobalCoreData(BaseModel):
    title: str = Field(None, description="核心数据标题")
    thought: str = Field(None, description="对详细内容的分析思考过程")
    content: str = Field(None, description="核心数据的详细内容")

    def to_msg(self):
        return f"- {self.title}：{self.content}"


class ResearchDetail(BaseModel):
    method: str = Field(None, description="研究方法")
    thought: str = Field(None, description="对详细内容的分析思考过程")
    detail: str = Field(None, description="研究方法详细内容")

    def to_msg(self):
        return f"- {self.method}\n{self.detail}"


class SummarizeCommonGlobalDataAgentResponse(BaseAgentResponse):
    thought: str = Field(None, description="对问题的分析思考过程")
    global_datas: List[GlobalCoreData] = Field(None, description="需要的核心数据列表")
    researches: List[ResearchDetail] = Field(None, description="需要的核心数据列表")


class SummarizeCommonGlobalDataAgent(BaseAgent):
    def __init__(self, options, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/global_data/summarize_common_global_data.jinja"
        self.input_type = SummarizeCommonGlobalDataAgentInput
        self.output_type = SummarizeCommonGlobalDataAgentResponse
        super().__init__(options, failover_options_list)

