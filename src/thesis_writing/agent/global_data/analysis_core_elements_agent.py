from typing import List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgentResponse, BaseAgent


class AnalysisCoreElementsAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: str = Field(None, description="关键词")
    summary: str = Field(..., description="论文概要", min_length=1)
    plan_str: str = Field(..., description="写作计划", min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/global_data/analysis_core_elements.jinja"


class AnalysisCoreElementsAgentResponse(BaseAgentResponse):
    analysis: str = Field(None, description="任务分析")
    entity_analysis: str = Field(None, description="实体分析")
    entity_count: str | int = Field(None, description="实体分析")
    candidate_analysis: str = Field(None, description="候选分析")
    research_entities: list[str] = Field(None, description="研究实体")


class AnalysisCoreElementsAgent(BaseAgent):
    def __init__(self, options, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/global_data/analysis_core_elements.jinja"
        self.input_type = AnalysisCoreElementsAgentInput
        self.output_type = AnalysisCoreElementsAgentResponse
        super().__init__(options, failover_options_list)
