from datetime import datetime
from typing import Optional, List

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class Material(BaseModel):
    title: str = Field(..., description="资料简述")
    description: str = Field(..., description="描述")


class GenerateFeedGuideAgentInput(BaseAgentInput):
    subject: str = Field(..., description="论文标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    level: str = Field("本科", description="学历层级：专科，本科，研究生")
    keywords: Optional[List[str]] = Field(None, description="关键词数组")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期", min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_feed_guide.jinja"


class GenerateFeedGuideAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="思路")
    material_overview: str = Field(None, description="资料概述")
    material_detail: str = Field(None, description="资料详细")


class GenerateFeedGuideAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_feed_guide.jinja"
        self.input_type = GenerateFeedGuideAgentInput
        self.output_type = GenerateFeedGuideAgentResponse
        super().__init__(options, failover_options_list)