from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class GenerateLiteratureReviewAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    toc: str = Field(..., description="文献综述目录", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    type: str = Field(..., description="文献综述类型")
    description: str = Field(..., description="文献综述写作思路")
    keywords: Optional[str] = Field(..., description="关键词")
    summary: Optional[str] = Field(..., description="全文概述")
    reference: Optional[str] = Field(..., description="参考文献")
    main_content: Optional[str] = Field(..., description="已完成的章节内容")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_literature_review.jinja"


class GenerateLiteratureReviewChapter(BaseAgentResponse):
    analysis: Optional[str] = Field(None, description="分析")
    length: Optional[str | int] = Field(None, description="字数")
    title: Optional[str] = Field(None, description="标题")
    content: Optional[str] = Field(None, description="具体内容")
    children: Optional[List["GenerateLiteratureReviewChapter"]] = Field(None, description="子节点")


class GenerateLiteratureReviewAgentResponse(BaseAgentResponse):
    literature_review: Optional[list[GenerateLiteratureReviewChapter]] = Field(None, description="章节")

    def main_content(self):
        return self._main_content_recursion(self.literature_review)

    def _main_content_recursion(self, item_list):
        result = ''
        for item in item_list:
            result += f"{item.title}\n"
            if item.content:
                result += f"{item.content}\n"
            if item.children:
                result += self._main_content_recursion(item.children)
        return result


class GenerateLiteratureReviewAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_literature_review.jinja"
        self.input_type = GenerateLiteratureReviewAgentInput
        self.output_type = GenerateLiteratureReviewAgentResponse
        super().__init__(options, failover_options_list)
