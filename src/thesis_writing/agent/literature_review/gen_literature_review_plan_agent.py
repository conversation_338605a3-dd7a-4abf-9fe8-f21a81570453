import json
from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class GenerateLiteratureReviewPlanAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(..., description="关键词")
    summary: Optional[str] = Field(..., description="全文概述")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_literature_review_plan.jinja"


class GenerateLiteratureReviewPlanAgentResponse(BaseAgentResponse):
    analysis: Optional[str] = Field(None, description="分析")
    literature_review_type: Optional[str] = Field(None, description="文献综述类型")
    literature_review_plan: Optional[List["GenerateLiteratureReviewPlanChapter"]] = Field(None, description="子节点")

    @property
    def toc(self):
        plan_json_list = [plan.model_dump(exclude_none=True) for plan in self.literature_review_plan]
        return [json.dumps(plan_json_list[i:i + 2], ensure_ascii=False, indent=2) for i in range(0, len(plan_json_list), 2)]


class GenerateLiteratureReviewPlanChapter(BaseAgentResponse):
    analysis: Optional[str] = Field(None, description="分析")
    title: Optional[str] = Field(None, description="标题")
    length: Optional[str | int] = Field(None, description="字数")
    children: Optional[List["GenerateLiteratureReviewPlanChapter"]] = Field(None, description="子节点")


class GenerateLiteratureReviewPlanAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_literature_review_plan.jinja"
        self.input_type = GenerateLiteratureReviewPlanAgentInput
        self.output_type = GenerateLiteratureReviewPlanAgentResponse
        super().__init__(options, failover_options_list)
