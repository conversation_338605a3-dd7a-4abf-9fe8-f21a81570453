from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentInput
from thesis_writing.agent.literature_review.gen_literature_review_plan_agent import \
    GenerateLiteratureReviewPlanAgentResponse


class ReGenerateLiteratureReviewPlanAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    toc: str = Field(..., description="文献综述目录", min_length=1)
    keywords: Optional[str] = Field(..., description="关键词")
    summary: Optional[str] = Field(..., description="全文概述")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/re_gen_literature_review_plan.jinja"


class ReGenerateLiteratureReviewPlanAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/re_gen_literature_review_plan.jinja"
        self.input_type = ReGenerateLiteratureReviewPlanAgentInput
        self.output_type = GenerateLiteratureReviewPlanAgentResponse
        super().__init__(options, failover_options_list)
