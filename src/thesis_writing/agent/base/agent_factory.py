from typing import List

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent
from thesis_writing.agent.chart.gen_chart_agent import (
    GenerateBarChartAgent,
    GenerateLineChartAgent,
    GeneratePieChartAgent,
    GenerateTableAgent,
    GenerateScatterChartAgent,
    GenerateRadarChartAgent,
)
from thesis_writing.agent.chart.gen_diagram_agent import (
    GenerateERDiagramAgent,
    GenerateSequenceDiagramAgent,
    GenerateClassDiagramAgent,
    GenerateStateDiagramAgent,
    GenerateArchitectureDiagramAgent,
    GenerateQuadrantChartAgent,
    GenerateSanKeyDiagramAgent,
    GenerateGanttDiagramAgent,
    GenerateMindMapDiagramAgent,
    GenerateTimeLineChartAgent,
    GeneratePacketDiagram,
    GenerateKanBanChartAgent,
    GenerateUseCaseDiagramAgent,
    GenerateFlowChartAgent, GenerateTechniqueRoadAgent,
)
from thesis_writing.agent.chart.refine_chart_agent import RefineChartAgent
from thesis_writing.agent.complete_plan_agent import CompletePlanAgent
from thesis_writing.agent.defense.gen_thesis_defense_ppt_agent import (
    GenerateThesisDefensePPTAgent,
)
from thesis_writing.agent.defense.gen_thesis_defense_qa_agent import (
    GenerateThesisDefenseQAAgent,
)
from thesis_writing.agent.engineering.gen_architechture_segment_agent import (
    GenerateArchSegmentAgent,
)
from thesis_writing.agent.engineering.gen_engineering_segment_agent import (
    GenerateEngineeringSegmentAgent,
)
from thesis_writing.agent.engineering.gen_engineering_task_statement_agent import (
    GenerateEngineeringTaskStatementAgent,
)
from thesis_writing.agent.engineering.gen_engineering_summary_agent import (
    GenerateEngineeringSummaryAgent,
)
from thesis_writing.agent.engineering.gen_engineering_conclusion_agent import (
    GenerateEngineeringConclusionAgent,
)
from thesis_writing.agent.engineering.gen_engineering_abstract_keywords_agent import (
    GenerateEngineeringAbstractKeywordsAgent,
)
from thesis_writing.agent.engineering.gen_proposal_report_agent import (
    GenerateEngineeringProposalAgent,
)
from thesis_writing.agent.gen_abstraction_agent import GenerateAbstractionAgent
from thesis_writing.agent.gen_acknowledge_agent import GenerateAcknowledgeAgent, GenerateAcknowledgeToBAgent
from thesis_writing.agent.gen_appendix_agent import GenerateAppendixAgent
from thesis_writing.agent.gen_appendix_item_agent import GenerateAppendixItemAgent
from thesis_writing.agent.gen_chapter_query_agent import GenerateChapterQueryAgent
from thesis_writing.agent.gen_conclusion_agent import GenerateConclusionAgent
from thesis_writing.agent.engineering.gen_engineering_plan_agent import (
    GenerateEngineeringPlanAgent,
)
from thesis_writing.agent.gen_en_abstraction_agent import GenerateEnAbstractAgent
from thesis_writing.agent.gen_en_keywords_agent import GenerateEnKeywordsAgent
from thesis_writing.agent.gen_en_title import GenerateEnglishTitleAgent
from thesis_writing.agent.gen_footnote_agent import GenerateFootNoteAgent
from thesis_writing.agent.gen_keywords_agent import GenerateKeywordsAgent
from thesis_writing.agent.gen_plan_addition_agent import GeneratePlanAdditionAgent
from thesis_writing.agent.gen_plan_agent import GeneratePlanAgent
from thesis_writing.agent.gen_reference_query_agent import GenerateReferenceQueryAgent
from thesis_writing.agent.gen_segment_agent import GenerateSegmentAgent
from thesis_writing.agent.gen_summary_agent import GenerateSummaryAgent
from thesis_writing.agent.gen_topic_agent import GenerateTopicAgent
from thesis_writing.agent.gen_user_feed_query_agent import GenerateUserFeedQueryAgent
from thesis_writing.agent.global_data.analysis_core_elements_agent import (
    AnalysisCoreElementsAgent,
)
from thesis_writing.agent.global_data.empirical_data_agent import (
    GenEmpiricalAnalysisPlanAgent,
    GenEmpiricalAnalysisQueryAgent,
    ExtractEmpiricalAnalysisQueryResultAgent,
    FakeEmpiricalAnalysisDataAgent,
)
from thesis_writing.agent.global_data.gen_feed_guide_agent import GenerateFeedGuideAgent
from thesis_writing.agent.global_data.global_data_agent import (
    AnalysisCommonGlobalDataQuery,
    SummarizeCommonGlobalDataAgent,
    ExtractCommonGlobalDataQueryResultAgent,
)
from thesis_writing.agent.literature_review.gen_literature_review_agent import (
    GenerateLiteratureReviewAgent,
)
from thesis_writing.agent.literature_review.gen_literature_review_plan_agent import (
    GenerateLiteratureReviewPlanAgent,
)
from thesis_writing.agent.literature_review.re_gen_literature_review_plan_agent import (
    ReGenerateLiteratureReviewPlanAgent,
)
from thesis_writing.agent.midterm_report.gen_thesis_midterm_report import (
    GenerateThesisMidtermReportAgent,
)
from thesis_writing.agent.proposal.determine_modules_need_reference_agent import (
    DetermineModulesNeedReferenceAgent,
)
from thesis_writing.agent.proposal.gen_thesis_proposal_agent import (
    GenerateThesisProposalAgent,
)
from thesis_writing.agent.proposal.gen_thesis_proposal_query_agent import (
    GenerateThesisProposalQueryAgent,
)
from thesis_writing.agent.questionnaire.gen_question_distributions_agent import (
    GenQuestionDistributionsAgent,
)
from thesis_writing.agent.questionnaire.gen_questionnaire_agent import (
    GenerateDefaultQuestionnaireAgent,
    GenerateCustomQuestionnaireAgent,
)
from thesis_writing.agent.questionnaire.gen_questionnaire_expected_result_agent import (
    GenQuestionnaireExpectedResultAgent,
)
from thesis_writing.agent.questionnaire.gen_questionnaire_report_agent import (
    GenQuestionnaireReportAgent,
    DeduplicateQuestionnaireReportAgent,
)
from thesis_writing.agent.refine_plan_node_agent import RefinePlanNodeAgent
from thesis_writing.agent.refine_reference_agent import RefineReferenceAgent
from thesis_writing.agent.refine_segments_agent import RefineSegmentsAgent
from thesis_writing.agent.refine_title_agent import RefineTitleAgent
from thesis_writing.agent.retrieval_analysis.choose_retrieval_result_agent import (
    ChooseRetrievalResultAgent,
)
from thesis_writing.agent.retrieval_analysis.rewrite_query_agent import (
    RewriteQueryAgent,
)
from thesis_writing.agent.retrieval_analysis.summarize_chunk_agent import (
    SummarizeChunkAgent,
)
from thesis_writing.agent.retrieval_analysis.summarize_text_agent import (
    SummarizeTextAgent,
)
from thesis_writing.agent.review_segments_agent import ReviewSegmentsAgent
from thesis_writing.agent.rewrite.gen_rewrite_instruction import (
    GenRewriteInstructionAgent,
)
from thesis_writing.agent.rewrite.rewrite_agent import RewriteAgent, CheckRewriteAgent
from thesis_writing.agent.short_paper.gen_short_paper_agent import GenShortPlanAgent, GenShortPlanAdditionAgent, \
    GenShortSegmentAgent, GenShortAcknowledgeAgent, GenShortAbstractAgent, GenShortSummaryAgent, GenShortShortPlanAgent, \
    GenShortConclusionAgent
from thesis_writing.agent.task_statement.gen_task_statement_agent import (
    GenerateTaskStatementAgent,
)
from thesis_writing.agent.thesis_segment.gen_chapter_query_agent import (
    GenerateChapterQueryWithThesisSegmentAgent,
)
from thesis_writing.agent.thesis_segment.refine_lead_paragraph_agent import (
    RefineLeadParagraphWithThesisSegmentAgent,
)
from thesis_writing.agent.thesis_segment.refine_paper_agent import (
    RefinePaperWithThesisSegmentAgent,
)
from thesis_writing.agent.thesis_segment.refine_segment_agent import (
    RefineSegmentsWithThesisSegmentAgent,
)
from thesis_writing.agent.thesis_segment.review_paper_agent import (
    ReviewPaperWithThesisSegmentAgent,
)
from thesis_writing.agent.engineering.revise_engineering_plan_agent import ReviseEngineeringPlanAgent
from thesis_writing.agent.engineering.revise_engineering_segment_agent import ReviseEngineeringSegmentAgent
from thesis_writing.entity.enums import AgentType
from thesis_writing.entity.enums import EducationLevel
from thesis_writing.agent.internship_report.gen_internship_report import GenerateInternshipReportAgent, \
    GenerateInternshipReportQueryAgent, GenerateFakeInternshipInfoAgent, GenerateInternshipMainContentAgent


class AgentFactory:

    @staticmethod
    def create_agent(
        agent_type: AgentType,
        agent_options: AgentOptions,
        failover_options_list: List[AgentOptions] = None,
        education_level: int = EducationLevel.Bachelor,
    ) -> BaseAgent:

        # Create the agent instance based on the agent type
        if agent_type == AgentType.GEN_SUMMARY:
            agent = GenerateSummaryAgent(agent_options, failover_options_list)

        elif agent_type == AgentType.ANALYZE_CORE_ELEMENTS:
            return AnalysisCoreElementsAgent(agent_options, failover_options_list)

        elif agent_type == AgentType.GEN_GLOBAL_DATA_QUERY:
            return AnalysisCommonGlobalDataQuery(agent_options, failover_options_list)
        elif agent_type == AgentType.EXTRACT_GLOBAL_DATA_QUERY_RESULT:
            return ExtractCommonGlobalDataQueryResultAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.SUMMARIZE_GLOBAL_DATA:
            return SummarizeCommonGlobalDataAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.ANALYSIS_EMPIRICAL_DATA:
            agent = GenEmpiricalAnalysisPlanAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_EMPIRICAL_QUERY:
            agent = GenEmpiricalAnalysisQueryAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.EXTRACT_EMPIRICAL_QUERY_RESULT:
            agent = ExtractEmpiricalAnalysisQueryResultAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.FAKE_EMPIRICAL_DATA:
            agent = FakeEmpiricalAnalysisDataAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_FEED_GUIDE:
            agent = GenerateFeedGuideAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_USER_FEED_QUERY:
            agent = GenerateUserFeedQueryAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.SUMMARIZE_TEXT:
            agent = SummarizeTextAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.SUMMARIZE_CHUNK:
            agent = SummarizeChunkAgent(agent_options, failover_options_list)

        elif agent_type == AgentType.GEN_PLAN:
            agent = GeneratePlanAgent(
                agent_options, failover_options_list, education_level
            )
        elif agent_type == AgentType.GEN_APPENDIX:
            agent = GenerateAppendixAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_APPENDIX_ITEM:
            agent = GenerateAppendixItemAgent(agent_options, failover_options_list)

        elif agent_type == AgentType.COMPLETE_PLAN:
            agent = CompletePlanAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_PLAN_ADDITION:
            agent = GeneratePlanAdditionAgent(agent_options, failover_options_list)

        elif agent_type == AgentType.GEN_RETRIEVAL_REFERENCE_QUERY:
            agent = GenerateReferenceQueryAgent(agent_options, failover_options_list)

        elif agent_type == AgentType.GEN_CHAPTER_QUERY:
            agent = GenerateChapterQueryAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.REWRITE_CHAPTER_QUERY:
            agent = RewriteQueryAgent(agent_options, failover_options_list)

        elif agent_type == AgentType.REVIEW_SEGMENTS:
            agent = ReviewSegmentsAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.REFINE_SEGMENTS:
            agent = RefineSegmentsAgent(agent_options, failover_options_list)

        elif agent_type == AgentType.CHOOSE_RETRIEVAL_RESULT:
            agent = ChooseRetrievalResultAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_SEGMENT:
            agent = GenerateSegmentAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.REFINE_SEGMENTS_WITH_THESIS_SEGMENT:
            agent = RefineSegmentsWithThesisSegmentAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_CHAPTER_QUERY_WITH_THESIS_SEGMENT:
            agent = GenerateChapterQueryWithThesisSegmentAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.REVIEW_PAPER_WITH_THESIS_SEGMENT:
            agent = ReviewPaperWithThesisSegmentAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.REFINE_PAPER_WITH_THESIS_SEGMENT:
            agent = RefinePaperWithThesisSegmentAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.REFINE_LEAD_PARAGRAPH_WITH_THESIS_SEGMENT:
            agent = RefineLeadParagraphWithThesisSegmentAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_PIE_CHART:
            agent = GeneratePieChartAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_LINE_CHART:
            agent = GenerateLineChartAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_BAR_CHART:
            agent = GenerateBarChartAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_TABLE:
            agent = GenerateTableAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_FLOW_CHART:
            agent = GenerateFlowChartAgent(agent_options, failover_options_list)

        elif agent_type == AgentType.GEN_CONCLUSION:
            agent = GenerateConclusionAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_ABSTRACT_KEYWORDS:
            agent = GenerateAbstractionAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_EN_ABSTRACT:
            agent = GenerateEnAbstractAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_EN_KEYWORDS:
            agent = GenerateEnKeywordsAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_EN_TITLE:
            agent = GenerateEnglishTitleAgent(agent_options, failover_options_list)

        elif agent_type == AgentType.GEN_ACKNOWLEDGEMENT:
            agent = GenerateAcknowledgeAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.REFINE_TITLE:
            agent = RefineTitleAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_KEYWORDS:
            agent = GenerateKeywordsAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_TASK_STATEMENT:
            agent = GenerateTaskStatementAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_THESIS_PROPOSAL_QUERY:
            agent = GenerateThesisProposalQueryAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_THESIS_PROPOSAL:
            agent = GenerateThesisProposalAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.DETERMINE_MODULES_NEED_REFERENCE:
            agent = DetermineModulesNeedReferenceAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.REWRITE:
            agent = RewriteAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.CHECK_REWRITE:
            agent = CheckRewriteAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_REWRITE_INSTRUCTION:
            agent = GenRewriteInstructionAgent(agent_options, failover_options_list)

        elif agent_type == AgentType.GEN_LITERATURE_REVIEW:
            agent = GenerateLiteratureReviewAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_LITERATURE_REVIEW_PLAN:
            agent = GenerateLiteratureReviewPlanAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.RE_GEN_LITERATURE_REVIEW_PLAN:
            agent = ReGenerateLiteratureReviewPlanAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_THESIS_DEFENSE_PPT:
            agent = GenerateThesisDefensePPTAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_THESIS_DEFENSE_QA:
            agent = GenerateThesisDefenseQAAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.REFINE_REFERENCE:
            agent = RefineReferenceAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_TOPIC:
            agent = GenerateTopicAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.REFINE_PLAN_NODE:
            agent = RefinePlanNodeAgent(agent_options, failover_options_list)

        elif agent_type == AgentType.GEN_ER_DIAGRAM:
            agent = GenerateERDiagramAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_CLASS_DIAGRAM:
            agent = GenerateClassDiagramAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_SEQUENCE_DIAGRAM:
            agent = GenerateSequenceDiagramAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_STATE_DIAGRAM:
            agent = GenerateStateDiagramAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_ARCHITECTURE_DIAGRAM:
            agent = GenerateArchitectureDiagramAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_QUADRANT_CHART:
            agent = GenerateQuadrantChartAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_SANKEY_DIAGRAM:
            agent = GenerateSanKeyDiagramAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_GANTT_DIAGRAM:
            agent = GenerateGanttDiagramAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_MINDMAP_DIAGRAM:
            agent = GenerateMindMapDiagramAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_TIMELINE_DIAGRAM:
            agent = GenerateTimeLineChartAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_PACKET_DIAGRAM:
            agent = GeneratePacketDiagram(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_KANBAN_DIAGRAM:
            agent = GenerateKanBanChartAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_SCATTER_CHART:
            agent = GenerateScatterChartAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_USECASE_DIAGRAM:
            agent = GenerateUseCaseDiagramAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_RADAR_CHART:
            agent = GenerateRadarChartAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.REFINE_CHART:
            agent = RefineChartAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_DEFAULT_QUESTIONNAIRE:
            agent = GenerateDefaultQuestionnaireAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_CUSTOM_QUESTIONNAIRE:
            agent = GenerateCustomQuestionnaireAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_QUESTIONNAIRE_EXPECTED_RESULT:
            agent = GenQuestionnaireExpectedResultAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_QUESTION_DISTRIBUTIONS:
            agent = GenQuestionDistributionsAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_QUESTIONNAIRE_REPORT:
            agent = GenQuestionnaireReportAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.DEDUPLICATE_QUESTIONNAIRE_REPORT:
            agent = DeduplicateQuestionnaireReportAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_ENGINEERING_PLAN:
            agent = GenerateEngineeringPlanAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_ENGINEERING_SEGMENT:
            agent = GenerateEngineeringSegmentAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_ARCH_SEGMENT:
            agent = GenerateArchSegmentAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_THESIS_MIDTERM_REPORT:
            agent = GenerateThesisMidtermReportAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_ENGINEERING_PROPOSAL:
            agent = GenerateEngineeringProposalAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_ENGINEERING_TASK_STATEMENT:
            agent = GenerateEngineeringTaskStatementAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_ENGINEERING_SUMMARY:
            agent = GenerateEngineeringSummaryAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_ENGINEERING_CONCLUSION:
            agent = GenerateEngineeringConclusionAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.GEN_ENGINEERING_ABSTRACT_KEYWORDS:
            agent = GenerateEngineeringAbstractKeywordsAgent(
                agent_options, failover_options_list
            )
        elif agent_type == AgentType.REVISE_ENGINEERING_PLAN:
            agent = ReviseEngineeringPlanAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.REVISE_ENGINEERING_SEGMENT:
            agent = ReviseEngineeringSegmentAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_SHORT_PAPER_PLAN:
            agent = GenShortPlanAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_SHORT_PLAN_ADDITION:
            agent = GenShortPlanAdditionAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_SHORT_SEGMENT:
            agent = GenShortSegmentAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_SHORT_ACKNOWLEDGEMENT:
            agent = GenShortAcknowledgeAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_SHORT_ABSTRACT_KEYWORDS:
            agent = GenShortAbstractAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_SHORT_SUMMARY:
            agent = GenShortSummaryAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_SHORT_SHORT_PLAN:
            agent = GenShortShortPlanAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_SHORT_CONCLUSION:
            agent = GenShortConclusionAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_ACKNOWLEDGEMENT_TOB:
            agent = GenerateAcknowledgeToBAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_TECHNIQUE_ROADMAP_DIAGRAM:
            agent = GenerateTechniqueRoadAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_INTERNSHIP_REPORT:
            agent = GenerateInternshipReportAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_INTERNSHIP_REPORT_QUERY:
            agent = GenerateInternshipReportQueryAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_FAKE_INTERNSHIP_INFO:
            agent = GenerateFakeInternshipInfoAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_INTERNSHIP_CONTENT:
            agent = GenerateInternshipMainContentAgent(agent_options, failover_options_list)
        elif agent_type == AgentType.GEN_FOOTNOTE:
            agent = GenerateFootNoteAgent(agent_options, failover_options_list)
        else:
            raise ValueError(f"Unknown agent type: {agent_type}")

        return agent
