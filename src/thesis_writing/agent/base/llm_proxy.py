from typing import Optional

from langchain_openai import ChatOpenAI, AzureChatOpenAI
from langchain_openai.chat_models.base import BaseChatOpenAI


def create_llm_proxy(
        api_key: str,
        model: str,
        base_url: str,
        max_tokens: int = 4096,
        temperature: Optional[float] = None,
        repetition_penalty: float = 1.05,
        timeout: int = None,
        enable_thinking: bool = False,
        response_format = None
) -> BaseChatOpenAI:
    if 'o1' in model:
        return AzureChatOpenAI(
            api_version="2024-10-01-preview",
            azure_endpoint=base_url,
            api_key=api_key,
            azure_deployment=model,
            temperature=1.0,
            max_retries=0,  # 这个是langchain内部的重试机制，与base_agent中的重试冲突，所以禁用
            timeout=timeout
        )
    elif 'azure' in base_url:
        return AzureChatOpenAI(
            api_version="2024-02-01",
            azure_endpoint=base_url,
            api_key=api_key,
            azure_deployment=model,
            max_tokens=max_tokens,
            temperature=temperature,
            max_retries=0,  # 这个是langchain内部的重试机制，与base_agent中的重试冲突，所以禁用
            timeout=timeout
        )
    elif 'qwen3' in model.lower():
        if 'aliyun' in base_url:
            extra_body = {"enable_thinking": enable_thinking}
        else:
            extra_body = {'chat_template_kwargs': {"enable_thinking": enable_thinking}}
        if response_format:
            extra_body['response_format'] = response_format
        return ChatOpenAI(
            base_url=base_url,
            api_key=api_key,
            model_name=model,
            max_tokens=max_tokens,
            temperature=temperature,
            extra_body=extra_body,
            top_p=0.8,
            max_retries=0,  # 这个是langchain内部的重试机制，与base_agent中的重试冲突，所以禁用
            request_timeout=timeout
        )
    return ChatOpenAI(
        base_url=base_url,
        api_key=api_key,
        model_name=model,
        max_tokens=max_tokens,
        temperature=temperature,
        extra_body={'repetition_penalty': repetition_penalty},
        top_p=0.8,
        max_retries=0,  # 这个是langchain内部的重试机制，与base_agent中的重试冲突，所以禁用
        request_timeout=timeout
    )
