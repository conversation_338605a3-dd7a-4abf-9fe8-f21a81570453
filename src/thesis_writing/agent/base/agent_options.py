from pydantic import BaseModel, Field


class AgentOptions(BaseModel):
    base_url: str = Field(default="")
    api_key: str = Field(default="")
    model: str = Field(default="")
    temperature: float = Field(default=0.2)
    retries: int = Field(default=3)
    repetition_penalty: float = Field(default=1.05)  # Only used for Qwen
    timeout: int = Field(default=300)  # 默认超时5分钟，具体场景可以单独设置
    stream: bool = Field(default=False)  # 是否启用流式输出

    max_output_tokens: int = Field(default=4096)
    max_prompt_tokens: int = Field(default=28 * 1024)
    max_histories_turns: int = Field(default=5)

    enable_thinking: bool = Field(default=False)  # qwen3是否启用思考模式

    def is_o1_model(self):
        return 'o1' in self.model
