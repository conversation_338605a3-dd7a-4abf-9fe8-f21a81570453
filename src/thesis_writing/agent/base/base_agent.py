from contextlib import suppress
from typing import Type, Optional, List, Tuple, Dict, Callable, Any

import dotenv
from langchain.globals import set_debug
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.messages.base import BaseMessage
from langchain_core.output_parsers import <PERSON>sonOutputParser, BaseOutputParser, StrOutputParser
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.runnables import RunnableConfig
from langfuse.decorators import langfuse_context
from pydantic import BaseModel, Field, ConfigDict

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.llm_proxy import create_llm_proxy
from thesis_writing.utils.json_repair.json_repair import repair_json
from thesis_writing.utils.logger import get_logger
from thesis_writing.utils.serializer import dict_to_markdown, dict_to_markdown_json
from thesis_writing.utils.template_parser import get_template
from thesis_writing.utils.token_helper import *

dotenv.load_dotenv()

set_debug(False)
logger = get_logger(__name__)


class ChainRunnableConfig(RunnableConfig):
    exception_callback: Optional[Callable]


class BaseAgentInput(BaseModel):
    model_config = ConfigDict(populate_by_name=True)
    user_message_template_path: str = Field(default=None)
    examples: Optional[list] = Field(None, description="样例")

    histories_in_asc: List[Tuple[str, str]] = Field(default=None, description="样例, Tuple[用户消息, AI消息]")

    inner_msg: str = Field(default=None, description="用户消息")

    def get_examples_txt(self):
        return "\n\n".join([f"# 范例{i + 1}\n{example_txt}\n" for i, example_txt in enumerate(self.examples or [])])

    def append_example(self, example_txt: str):
        self.examples = [] if not self.examples else self.examples
        self.examples.append(example_txt)

    def append_histories(self, pre_user_input: "BaseAgentInput", pre_agent_response: "BaseAgentResponse"):
        human_msg = pre_user_input.to_msg()
        ai_msg = pre_agent_response.to_markdown_json()

        self.histories_in_asc = [] if not self.histories_in_asc else self.histories_in_asc
        self.histories_in_asc.append((human_msg, ai_msg))

    def get_histories_in_desc(self):
        return self.histories_in_asc[::-1] if self.histories_in_asc else []

    def to_msg(self):
        if self.inner_msg:
            return self.inner_msg

        kwargs = self.model_dump()
        return get_template(self.user_message_template_path, **kwargs)

    def set_msg(self, message: str):
        self.inner_msg = message


class BaseAgentResponse(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    def to_markdown_text(self, level: int = 3):
        return dict_to_markdown(self.model_dump(exclude_none=True), level)

    def to_markdown_json(self):
        return dict_to_markdown_json(self.model_dump(exclude_none=True, by_alias=True))


class BaseAgent:
    options: AgentOptions = Field(default=None)
    failover_options_list: List[AgentOptions] = Field(default=None)

    system_message_template_path: str = Field(default=None)
    input_type: Type[BaseAgentInput] = Field(default=BaseAgentInput)
    output_type: Type[BaseAgentResponse] = Field(default=BaseAgentResponse)
    output_parser: str = 'json'

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.options = options
        self.failover_options_list = failover_options_list

    def custom_parser(self):
        parser = None
        if self.output_parser == "json":
            parser = JsonWithRepairParser()
        elif self.output_parser == "str":
            parser = StrOutputParser()
        return parser

    def deserialize_parser_output(self, parser_output: str) -> BaseAgentResponse:
        return self.output_type.model_validate(parser_output)

    def validate_output(self, input: BaseAgentInput, output: BaseAgentResponse, remaining_retries=0):
        pass

    def _build_chains(self, options: AgentOptions, include_parser=True):
        llm = create_llm_proxy(
            base_url=options.base_url,
            api_key=options.api_key,
            model=options.model,
            max_tokens=options.max_output_tokens,
            temperature=options.temperature,
            repetition_penalty=options.repetition_penalty,
            timeout=options.timeout,
            enable_thinking=options.enable_thinking
        )
        if options.is_o1_model():
            self._prompt = ChatPromptTemplate.from_messages(
                [
                    ("human", "{system}\n\n{question}")
                ]
            )
        else:
            self._prompt = ChatPromptTemplate.from_messages(
                [
                    ("system", "{system}"),
                    MessagesPlaceholder("histories"),
                    ("human", "{question}")
                ]
            )

        if include_parser is True:
            return self._prompt | llm | self.custom_parser()
        else:
            return self._prompt | llm

    def invoke(self, agent_input: BaseAgentInput, config: ChainRunnableConfig = None) -> BaseAgentResponse:
        if self.options.stream:
            invoke_result = self.do_invoke_stream(agent_input, self.options, config)
        else:
            invoke_result = self.do_invoke(agent_input, self.options, config)

        if invoke_result:
            return invoke_result

        for failover_options in self.failover_options_list or []:
            if failover_options.stream:
                invoke_result = self.do_invoke_stream(agent_input, failover_options, config)
            else:
                invoke_result = self.do_invoke(agent_input, failover_options, config)

            if invoke_result:
                return invoke_result

        raise RuntimeError("Failed to invoke agent after retries")

    def do_invoke(self, agent_input: BaseAgentInput, agent_options: AgentOptions,
                  config: ChainRunnableConfig = None) -> BaseAgentResponse:
        chain = self._build_chains(agent_options)
        data = self._format_input(agent_input, agent_options)
        for i in range(agent_options.retries):
            try:
                result = chain.invoke(data, config=self._set_invoke_config(config))
                output = self.deserialize_parser_output(result)
                self.validate_output(agent_input, output, remaining_retries=agent_options.retries - i - 1)
                return output
            except Exception as e:
                logger.error({
                    "TraceId": langfuse_context.get_current_trace_id(),
                    "exception_class": e.__class__.__name__,
                    "exception_message": str(e),
                })

                if config and config["exception_callback"]:
                    config["exception_callback"](config["metadata"])

    def do_invoke_stream(self, agent_input: BaseAgentInput, agent_options: AgentOptions,
                  config: ChainRunnableConfig = None) -> BaseAgentResponse:
        chain = self._build_chains(agent_options, include_parser=False)
        data = self._format_input(agent_input, agent_options)
        for i in range(agent_options.retries):
            try:
                output_stream = chain.stream(data, config=self._set_invoke_config(config))
                output_chunks = []
                for chunk in output_stream:
                    output_chunks.append(chunk.content)

                result = self.custom_parser().parse(''.join(output_chunks))
                output = self.deserialize_parser_output(result)
                self.validate_output(agent_input, output, remaining_retries=agent_options.retries - i - 1)
                return output
            except Exception as e:
                logger.error({
                    "TraceId": langfuse_context.get_current_trace_id(),
                    "exception_class": e.__class__.__name__,
                    "exception_message": str(e),
                })

                if config and config["exception_callback"]:
                    config["exception_callback"](config["metadata"])

    def stream(self, agent_input: BaseAgentInput, config: ChainRunnableConfig = None):
        chain = self._build_chains(self.options, include_parser=False)
        data = self._format_input(agent_input, self.options)

        return chain.stream(data, config=self._set_invoke_config(config))

    async def ainvoke(self, agent_input: BaseAgentInput, config: ChainRunnableConfig = None) -> BaseAgentResponse:
        if self.options.stream:
            invoke_result = await self.do_ainvoke_stream(agent_input, self.options, config)
        else:
            invoke_result = await self.do_ainvoke(agent_input, self.options, config)

        if invoke_result:
            return invoke_result

        for failover_options in self.failover_options_list or []:
            if failover_options.stream:
                invoke_result = await self.do_ainvoke_stream(agent_input, failover_options, config)
            else:
                invoke_result = await self.do_ainvoke(agent_input, failover_options, config)

            if invoke_result:
                return invoke_result

        raise RuntimeError("Failed to invoke agent after retries")

    async def do_ainvoke(self, agent_input: BaseAgentInput, agent_options: AgentOptions,
                         config: ChainRunnableConfig = None) -> BaseAgentResponse:
        chain = self._build_chains(agent_options)
        data = self._format_input(agent_input, agent_options)
        for i in range(agent_options.retries):
            try:
                result = await chain.ainvoke(data, config=self._set_invoke_config(config))
                output = self.deserialize_parser_output(result)
                self.validate_output(agent_input, output, remaining_retries=agent_options.retries - i - 1)
                return output
            except Exception as e:
                logger.error({
                    "TraceId": langfuse_context.get_current_trace_id(),
                    "exception_class": e.__class__.__name__,
                    "exception_message": str(e),
                })

                if config and config["exception_callback"]:
                    config["exception_callback"](config["metadata"])

    async def do_ainvoke_stream(self, agent_input: BaseAgentInput, agent_options: AgentOptions,
                         config: ChainRunnableConfig = None) -> BaseAgentResponse:
        chain = self._build_chains(agent_options, include_parser=False)
        data = self._format_input(agent_input, agent_options)
        for i in range(agent_options.retries):
            try:
                output_stream = chain.astream(data, config=self._set_invoke_config(config))
                output_chunks = []
                async for chunk in output_stream:
                    output_chunks.append(chunk.content)

                result = self.custom_parser().parse(''.join(output_chunks))
                output = self.deserialize_parser_output(result)
                self.validate_output(agent_input, output, remaining_retries=agent_options.retries - i - 1)
                return output
            except Exception as e:
                logger.error({
                    "TraceId": langfuse_context.get_current_trace_id(),
                    "exception_class": e.__class__.__name__,
                    "exception_message": str(e),
                })

                if config and config["exception_callback"]:
                    config["exception_callback"](config["metadata"])

    async def astream(self, agent_input: BaseAgentInput, config: ChainRunnableConfig = None):
        chain = self._build_chains(self.options, include_parser=False)
        data = self._format_input(agent_input, self.options)

        return chain.astream(data, config=self._set_invoke_config(config))

    def batch(self, inputs: List[BaseAgentInput]) -> List[BaseAgentResponse]:
        return [self.invoke(agent_input) for agent_input in inputs]

    def render_template_with_input(self, agent_input: BaseAgentInput):
        return get_template(self.system_message_template_path)

    def _format_input(self, agent_input: BaseAgentInput, agent_options: AgentOptions) \
            -> Dict[str, str | List[BaseMessage]]:
        _system_message_template = self.render_template_with_input(agent_input)
        messages = {"system": _system_message_template}
        if agent_input.examples:
            messages["system"] = _system_message_template + "\n\n" + agent_input.get_examples_txt()

        user_message = agent_input.to_msg()
        max_prompt_tokens = agent_options.max_prompt_tokens
        total_tokens = (self._get_message_token_count(_system_message_template, agent_options.model)
                        + self._get_message_token_count(user_message, agent_options.model) + 3)

        if total_tokens > max_prompt_tokens:
            overflow_tokens = total_tokens - max_prompt_tokens
            logger.error(f"The message is too large to send to the model, cut {overflow_tokens} chars. input_class: {agent_input.__class__.__name__}")
            user_message = user_message[:-overflow_tokens]

        messages["question"] = user_message
        messages["histories"] = []

        # because of message token limit, we need add latter messages first
        for histories_message_tuple in agent_input.get_histories_in_desc():
            user_message = HumanMessage(content=histories_message_tuple[0])
            ai_message = AIMessage(content=histories_message_tuple[1])

            tokens = (self._get_message_token_count(user_message.content, agent_options.model)
                      + self._get_message_token_count(ai_message.content, agent_options.model))
            if total_tokens + tokens > max_prompt_tokens:
                break

            # because the loop is in desc order, so we need to insert the new message to the front
            new_histories = [user_message, ai_message]
            new_histories.extend(messages['histories'])
            messages['histories'] = new_histories

            total_tokens += tokens

            if len(messages['histories']) >= agent_options.max_histories_turns * 2:
                break

        return messages

    def _get_message_token_count(self, content: str, llm_model: str) -> int:
        return calculate_token_count(content, llm_model) + template_tokens_per_message(llm_model)

    def _set_invoke_config(self, config: RunnableConfig) -> Optional[RunnableConfig]:
        if not config:
            config = RunnableConfig()

        config["run_name"] = self.__class__.__name__
        handler = langfuse_context.get_current_langchain_handler()
        if handler:
            config["callbacks"] = [handler]

        return config

    def to_chat_string(self, agent_input: BaseAgentInput) -> str:
        data = self._format_input(agent_input, self.options)

        chat_string = ""
        for each in self._prompt.invoke(data).to_messages():
            chat_string += each.type + ": \n" + each.content + "\n"
            chat_string += "\n\n"

        return chat_string


class JsonWithRepairParser(BaseOutputParser):
    def parse(self, text: str) -> Any:
        json_result = None
        with suppress(Exception):
            json_result = JsonOutputParser().parse(text)

        repaired_json_result = repair_json(text, ensure_ascii=False, return_objects=True)

        if json_result and isinstance(json_result, dict)    \
            and (not isinstance(repaired_json_result, dict) or len(json_result) == len(repaired_json_result)):
            return json_result

        return repaired_json_result
