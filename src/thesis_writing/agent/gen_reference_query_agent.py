from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class GenerateReferenceQueryAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    major: str = Field(..., description="专业", min_length=1)
    summary: Optional[str] = Field(..., description="全文概述")
    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    segment_writing_plan: str = Field(..., description="当前目标小节写作计划", min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_query_of_reference.jinja"


class GenerateReferenceQueryAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="思路")
    need_reference: str = Field(..., description="是否需要参考文献")
    queries: List[str] = Field(None, description="查询语句")


class GenerateReferenceQueryAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_query_of_reference.jinja"
        self.input_type = GenerateReferenceQueryAgentInput
        self.output_type = GenerateReferenceQueryAgentResponse
        super().__init__(options, failover_options_list)
