from typing import Optional, List, Dict

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class GenerateChapterQueryAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    current_chapter_title: str = Field(..., description="当前章的标题", min_length=1)
    chapter_additions: Optional[str] = Field(default='无', description="本章节包含的图表和案例")
    global_entities: Optional[List[str]] = Field(None, description="全局实体信息")
    global_entities_str: Optional[str] = Field(None, description="全局实体信息")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_query_of_chapter.jinja"
        if self.global_entities:
            self.global_entities_str = f"<entities>\n{'\n\n'.join(self.global_entities)}\n</entities>"


class QueryItem(BaseModel):
    source: str = Field(..., description="查询来源")
    query: str = Field(..., description="查询语句")


class GenerateChapterQueryAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="思路")
    query_of_node: Dict[str, List[QueryItem]] = Field(None, description="各节的查询语句")


class GenerateChapterQueryAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_query_of_chapter.jinja"
        self.input_type = GenerateChapterQueryAgentInput
        self.output_type = GenerateChapterQueryAgentResponse
        super().__init__(options, failover_options_list)
