from typing import Optional, List

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput
from thesis_writing.utils.template_parser import get_template


class GenerateThesisMidtermReportAgentInput(BaseAgentInput):
    subject: str = Field(..., description="论文标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    midterm_checkpoints: str = Field(default=..., description="中期检查表需要完成的任务列表")
    summary: Optional[str] = Field(default=None, description="论文全文概述")
    keywords: Optional[str] = Field(default=None, description="论文关键词")
    proposal: Optional[str] = Field(default=None, description="已经完成的开题报告")  # 暂时不用

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_thesis_midterm_report.jinja"


class MidTermReportChapter(BaseModel):
    checkpoint_title: Optional[str] = Field(None, description="标题")
    checkpoint_content: Optional[str] = Field(None, description="具体内容")


class GenerateThesisMidtermReportAgentResponse(BaseAgentResponse):
    midterm_report: list[MidTermReportChapter] = Field(default_factory=list, description="中期检查报告章节",
                                                       min_length=1)


class GenerateThesisMidtermReportAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_thesis_midterm_report.jinja"
        self.input_type = GenerateThesisMidtermReportAgentInput
        self.output_type = GenerateThesisMidtermReportAgentResponse
        super().__init__(options, failover_options_list)

