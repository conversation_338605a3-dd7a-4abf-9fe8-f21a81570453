from typing import Optional, List

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput, ChainRunnableConfig
from thesis_writing.retriever.retrieve_service import RetrieveService
from thesis_writing.utils.logger import get_logger

logger = get_logger(__name__)


class GenerateTopicAgentInput(BaseAgentInput):
    major: str = Field(..., description="专业", min_length=1)
    comment: Optional[str] = Field(None, description="备注")
    count: int = Field(..., description="数量", ge=1, le=20)
    reference_topics: Optional[str] = Field(None, description="参考选题")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_topic.jinja"


class ThesisTopic(BaseModel):
    thought: str = Field(..., description="选题思路")
    title: str = Field(..., description="标题")
    keywords: str = Field(..., description="关键词")
    reason: str = Field(..., description="选题理由")


class GenerateTopicAgentResponse(BaseAgentResponse):
    topics: list[ThesisTopic] = Field(..., description="选题")


class GenerateTopicAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_topic.jinja"
        self.input_type = GenerateTopicAgentInput
        self.output_type = GenerateTopicAgentResponse
        super().__init__(options, failover_options_list)

    async def ainvoke(self, agent_input: GenerateTopicAgentInput,
                      config: ChainRunnableConfig = None) -> GenerateTopicAgentResponse:
        if not agent_input.reference_topics or agent_input.reference_topics == "":
            reference_topics = ""
            for each in await RetrieveService().search_thesis_topic(agent_input.major, 5):
                reference_topics += f"题目：{each.title}\n关键词：{each.keywords}\n全文概要：{each.summary.replace('\n', '')}\n\n"
            agent_input.reference_topics = reference_topics
        return await super().ainvoke(agent_input, config)
