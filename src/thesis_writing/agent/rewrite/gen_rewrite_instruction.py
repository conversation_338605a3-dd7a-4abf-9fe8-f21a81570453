from datetime import datetime
from typing import Optional, List, Union

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentInput, BaseAgentResponse
from thesis_writing.utils.template_parser import get_template


class RewriteParagraph(BaseModel):
    id: Optional[str] = Field(None, title="传给模型的ID")
    para_id: str = Field(..., title="word中的段落ID")
    text: str = Field(..., title="段落的文本内容")
    chapter: Optional[str] = Field(..., title="所属的章节标题")

    def to_agent_input(self):
        if self.chapter:
            return f"<segment id=\"{self.id}\" chapter=\"{self.chapter}\" length=\"{len(self.text)}\">{self.text}</segment>"
        return f"<segment id=\"{self.id}\" length=\"{len(self.text)}\">{self.text}</segment>"


class RewriteComment(BaseModel):
    ids: Optional[List[str]] = Field(None, title="传给模型的段落ID")
    para_ids: Optional[List[str]] = Field(None, title="word中的段落ID")
    comment: str = Field(..., title="段落的文本内容")

    def to_agent_input(self):
        if self.ids:
            ids_text = ','.join(self.ids)
            return f"<comment ids=\"{ids_text}\">{self.comment}</comment>"
        else:
            return f"<comment>{self.comment}</comment>"


class GenRewriteInstructionAgentInput(BaseAgentInput):
    paragraphs: List[RewriteParagraph] = Field(..., title="待改写的段落")
    comments: List[RewriteComment] = Field(None, description="改写意见")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_rewrite_instruction.jinja"

    def to_msg(self):
        kwargs = self.model_dump(exclude={"paragraphs", "comments"})
        mapping = {}
        for index, each in enumerate(self.paragraphs):
            each.id = f"p{index + 1}"
            if each.para_id:
                mapping[each.para_id] = each.id
        for each in self.comments:
            each.ids = [mapping[para_id] for para_id in each.para_ids if para_id in mapping] if each.para_ids else None
        kwargs['content'] = '\n'.join([each.to_agent_input() for each in self.paragraphs])

        kwargs['comments'] = '\n'.join([each.to_agent_input() for each in self.comments])
        return get_template(self.user_message_template_path, **kwargs)


class RewriteInstruction(BaseModel):
    ids: List[str] = Field(..., description="修改意见对应的para_ids")
    instruction: str = Field(..., description="具体可执行的修改意见")
    title: Optional[str] = Field(None, description="修改意见的标题")


class GenRewriteInstructionAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="思考过程")
    success: Union[str, bool] = Field(None, description="是否成功")
    error_message: Optional[str] = Field(None, description="错误信息")
    instructions: List[RewriteInstruction] = Field(..., description="改稿指令")

    def check_success(self):
        return self.success not in ["False", False, "false", "0", 0, "否", -1, '-1']


class GenRewriteInstructionAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_rewrite_instruction.jinja"
        self.input_type = GenRewriteInstructionAgentInput
        self.output_type = GenRewriteInstructionAgentResponse
        super().__init__(options, failover_options_list)

    async def ainvoke(self, agent_input: GenRewriteInstructionAgentInput) -> GenRewriteInstructionAgentResponse:
        result: GenRewriteInstructionAgentResponse = await super().ainvoke(agent_input)
        self.update_ids_in_instructions(agent_input, result.instructions)
        return result

    @classmethod
    def update_ids_in_instructions(cls, agent_input, instructions: List[RewriteInstruction]):
        ids_mapping = {each.id: each.para_id for each in agent_input.paragraphs}
        for each in instructions:
            each.ids = [ids_mapping.get(each_id, each_id) for each_id in each.ids]
