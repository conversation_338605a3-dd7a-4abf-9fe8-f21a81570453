from datetime import datetime
from enum import IntEnum
from typing import Optional, List, Union

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentInput, BaseAgentResponse


class RewriteDirectiveEnum(IntEnum):
    REWRITE = 1
    EXPAND = 2
    SHORTEN = 3
    POLISH = 4
    CONTINUE = 5
    SUMMARIZE = 6
    CUSTOM = 7


def get_default_instruction(directive: RewriteDirectiveEnum) -> str:
    """
    根据 RewriteDirectiveEnum 枚举值，返回对应的默认指令。
    """
    if directive == RewriteDirectiveEnum.REWRITE:
        return "对当前选中内容在不改变原文含义的前提下通过变换语法、表达方式等进行改写。"
    elif directive == RewriteDirectiveEnum.EXPAND:
        return "对当前选中内容在原有文本的基础上，通过增加细节、丰富内容、扩展情节等方式，使文本更加详细、生动和完整。"
    elif directive == RewriteDirectiveEnum.SHORTEN:
        return "对当前选中内容在原有文本的基础上，通过简化内容、省略细节、提炼要点等方式，使文本更加简洁、紧凑和精炼。"
    elif directive == RewriteDirectiveEnum.POLISH:
        return "对当前选中内容在不改变原文含义的前提下对语言表达方式和逻辑进行润色优化。"
    elif directive == RewriteDirectiveEnum.CONTINUE:
        return "根据当前选中内容继续创作新的内容，使故事、描述等得以延伸和发展。"
    elif directive == RewriteDirectiveEnum.SUMMARIZE:
        return "对当前选中内容的含义进行总结。"
    else:
        raise ValueError(f"未知的重写指令：{directive}")


class RewriteAgentInput(BaseAgentInput):
    preceding_text: Optional[str] = Field(default="无", description="前文")
    selected_text: str = Field(..., description="待改写的文本")
    following_text: Optional[str] = Field(default="无", description="后文")
    user_instruction: Optional[str] = Field(None, description="用户指令")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)
    type: RewriteDirectiveEnum = Field(RewriteDirectiveEnum.REWRITE, description="重写指令")
    materials: Optional[str] = Field(None, description="材料")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/rewrite.jinja"
        if self.user_instruction is None and self.type:
            self.user_instruction = get_default_instruction(self.type)


class CheckRewriteAgentResponse(BaseAgentResponse):
    success: Union[str, bool] = Field(None, description="是否成功")
    error_message: Optional[str] = Field(None, description="错误信息")
    queries: List[str] = Field([], description="查询列表")

    def check_success(self):
        return self.success not in ["False", False, "false", "0", 0, "否", -1, '-1']


class RewriteAgentResponse(BaseAgentResponse):
    modified_text: Optional[str] = Field(None, description="重写后文本")


class CheckRewriteAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/check_rewrite.jinja"
        self.input_type = RewriteAgentInput
        self.output_type = CheckRewriteAgentResponse
        super().__init__(options, failover_options_list)


class RewriteAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/rewrite.jinja"
        self.input_type = RewriteAgentInput
        self.output_type = RewriteAgentResponse
        super().__init__(options, failover_options_list)
