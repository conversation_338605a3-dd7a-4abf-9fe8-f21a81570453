from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgentResponse, BaseAgent
from thesis_writing.entity.thesis_data import ProcessDocumentation


class GenerateInternshipReportAgentInput(BaseAgentInput):
    major: Optional[str] = Field(None, description="专业", min_length=1)
    toc: str = Field(..., description="实习报告目录", min_length=1)
    internship_unit: Optional[str] = Field(None, description="实习单位")
    career: str = Field(..., description="实习岗位")
    internship_time: Optional[str] = Field(None, description="实习时间")
    materials: Optional[str] = Field(None, description="相关材料")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/internship_report/gen_internship_report.jinja"


class GenerateInternshipReportAgentResponse(BaseAgentResponse):
    internship_report: List[ProcessDocumentation] = Field(..., description="实习报告")

    def get_completed_report(self):
        completed_content = [process_documentation.get_content() for process_documentation in self.internship_report]
        return "\n".join(completed_content) if completed_content else ""


class GenerateInternshipReportAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/internship_report/gen_internship_report.jinja"
        self.input_type = GenerateInternshipReportAgentInput
        self.output_type = GenerateInternshipReportAgentResponse
        super().__init__(options, failover_options_list)


class GenerateInternshipReportQueryAgentInput(BaseAgentInput):
    toc: str = Field(..., description="实习报告目录", min_length=1)
    career: str = Field(..., description="实习岗位")
    internship_unit: Optional[str] = Field(None, description="实习单位")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/internship_report/gen_internship_report_query.jinja"


class GenerateInternshipReportQueryAgentResponse(BaseAgentResponse):
    queries: List[str] = Field(default_factory=List, description="查询语句")


class GenerateInternshipReportQueryAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/internship_report/gen_internship_report_query.jinja"
        self.input_type = GenerateInternshipReportQueryAgentInput
        self.output_type = GenerateInternshipReportQueryAgentResponse
        super().__init__(options, failover_options_list)


class GenerateFakeInternshipInfoAgentInput(BaseAgentInput):
    major: str = Field(..., description="专业")
    internship_unit_hint: Optional[str] = Field(None, description="实习单位名称")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/internship_report/gen_fake_internship_info.jinja"


class GenerateFakeInternshipInfoAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="思考过程")
    internship_unit: str = Field(..., description="实习单位名称")
    internship_career: str = Field(..., description="实习岗位")


class GenerateFakeInternshipInfoAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/internship_report/gen_fake_internship_info.jinja"
        self.input_type = GenerateFakeInternshipInfoAgentInput
        self.output_type = GenerateFakeInternshipInfoAgentResponse
        super().__init__(options, failover_options_list)


class GenerateInternshipMainContentAgentInput(BaseAgentInput):
    major: Optional[str] = Field(None, description="专业", min_length=1)
    toc: str = Field(..., description="实习内容章节结构", min_length=1)
    internship_unit: Optional[str] = Field(None, description="实习单位")
    career: str = Field(..., description="实习岗位")
    internship_time: Optional[str] = Field(None, description="实习时间")
    materials: Optional[str] = Field(None, description="相关材料")
    completed_content: Optional[str] = Field(None, description="实习报告已完成内容")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/internship_report/gen_internship_content.jinja"


class GenerateInternshipMainContentAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="思考过程")
    content: str = Field(..., description="实习内容")


class GenerateInternshipMainContentAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/internship_report/gen_internship_content.jinja"
        self.input_type = GenerateInternshipMainContentAgentInput
        self.output_type = GenerateInternshipMainContentAgentResponse
        super().__init__(options, failover_options_list)

