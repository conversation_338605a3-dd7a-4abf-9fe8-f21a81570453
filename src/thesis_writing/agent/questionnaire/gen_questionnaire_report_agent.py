from typing import Optional, List

from pydantic import Field, conlist

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgentResponse, BaseAgent
from thesis_writing.agent.questionnaire.questionnaire import Analysis, Suggestion
from thesis_writing.utils.template_parser import get_template


class GenQuestionnaireReportAgentInput(BaseAgentInput):
    title: str = Field(..., description="问卷标题")
    description: str = Field(..., description="问卷描述")
    target_audience: Optional[str] = Field("", description="目标群体")
    expected_results: str = Field(..., description="期望结果")
    samples: int = Field(100, ge=0, description="样本数量")
    questions: str = Field(..., description="问题列表")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/questionnaire/gen_questionnaire_report.jinja"


class GenQuestionnaireReportAgentResponse(BaseAgentResponse):
    report_title: str = Field(..., description="分析报告标题")
    report_description: str = Field(..., description="分析报告描述")
    data_analysis: conlist(Analysis, min_length=1) = Field(..., description="数据分析")
    suggestions: Optional[conlist(Suggestion, min_length=1)] = Field([], description="建议")
    summary: Optional[str] = Field("", description="总结")


class GenQuestionnaireReportAgent(BaseAgent):

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/questionnaire/gen_questionnaire_report_expression.jinja"
        self.input_type = GenQuestionnaireReportAgentInput
        self.output_type = GenQuestionnaireReportAgentResponse
        super().__init__(options, failover_options_list)

    def render_template_with_input(self, agent_input: BaseAgentInput):
        return get_template(self.system_message_template_path, **agent_input.model_dump())


class DeduplicateQuestionnaireReportAgent(GenQuestionnaireReportAgent):
    """
    处理报告重复内容
    """
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        super().__init__(options, failover_options_list)
        self.system_message_template_path = "system_prompt/questionnaire/deduplicate_questionnaire_report.jinja"
        self.input_type = DeduplicateQuestionnaireReportAgentInput
        self.output_type = GenQuestionnaireReportAgentResponse


class DeduplicateQuestionnaireReportAgentInput(BaseAgentInput):
    report_title: str = Field(..., description="调查报告标题")
    report_description: str = Field(..., description="调查报告描述")
    data_analysis: conlist(Analysis, min_length=1) = Field(..., description="数据分析")
    suggestions: Optional[conlist(Suggestion, min_length=1)] = Field([], description="建议")
    summary: Optional[str] = Field("", description="总结")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/questionnaire/deduplicate_questionnaire_report.jinja"
