from typing import Optional, List

import numpy as np
from pydantic import Field, conlist
from thesis_writing.utils.template_parser import get_template

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgentResponse, BaseAgent
from thesis_writing.agent.questionnaire.questionnaire import Question, OpenEndQuestion, SingleChoiceQuestion, \
    MultipleChoiceQuestion


class GenQuestionDistributionsInput(BaseAgentInput):
    title: str = Field(..., description="问卷标题")
    description: str = Field(..., description="问卷描述")
    target_audience: Optional[str] = Field("", description="目标群体")
    expected_results: str = Field(..., description="调查结果预期")
    questions: str = Field(..., description="问题列表")
    samples: int = Field(100, ge=0, description="样本数量")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/questionnaire/gen_question_distributions.jinja"


class GenQuestionDistributionsResponse(BaseAgentResponse):
    questions: conlist(Question, min_length=2) = Field(..., description="问题列表")


class GenQuestionDistributionsAgent(BaseAgent):

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/questionnaire/gen_question_distributions.jinja"
        self.input_type = GenQuestionDistributionsInput
        self.output_type = GenQuestionDistributionsResponse
        super().__init__(options, failover_options_list)

    def render_template_with_input(self, agent_input: BaseAgentInput):
        return get_template(self.system_message_template_path, **agent_input.model_dump())

    def deserialize_parser_output(self, output) -> GenQuestionDistributionsResponse:
        """
        解析模型输出
        """
        result = []
        for question in output['questions']:
            question["section_type"] = ""
            question["stem"] = "default stem"
            if "placeholder" in question:
                question['type'] = "open_end"
                question["section_type"] = "feedback"
                result.append(OpenEndQuestion(**question))
            else:
                question["section_type"] = "core"
                question["type"] = "single_choice"
                if sum(question['distributions']) > 1:  # 说明是多选题
                    question['distributions'] = self.add_random_perturbation(np.array(question['distributions']),
                                                                             scale=0.15, type=0)
                    question['type'] = "multiple_choice"
                    result.append(MultipleChoiceQuestion(**question))
                else:
                    question['distributions'] = self.add_random_perturbation(np.array(question['distributions']),
                                                                             scale=0.15, type=1)
                    result.append(SingleChoiceQuestion(**question))
        return GenQuestionDistributionsResponse(questions=result)

    def add_random_perturbation(self, probs, scale=0.1, type=1):
        """
        probs: 初始概率分布
        scale: 扰动强度(0-1之间)
        """
        perturbations = np.random.uniform(-scale, scale, size=len(probs))
        perturbed = probs * (1 + perturbations)
        perturbed = np.maximum(perturbed, 0)
        if not type:
            # 如果是多选题，加了扰动就返回，不需要归一化
            return list(np.round(perturbed, 2))
        perturbed = perturbed / np.sum(perturbed)

        # 先对前 n-1 个元素四舍五入
        rounded = np.round(perturbed[:-1], 2)
        # 最后一个元素 = 1 - 前 n-1 个元素的和
        last_elem = np.round(1 - np.sum(rounded), 2)
        return list(np.append(rounded, last_elem))
