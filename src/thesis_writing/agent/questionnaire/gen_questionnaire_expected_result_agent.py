from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgentResponse, BaseAgent
from thesis_writing.utils.template_parser import get_template


class GenQuestionnaireExpectedResultInput(BaseAgentInput):
    basic_info: str = Field(..., description="主题与目的")
    target_audience: Optional[str] = Field("", description="目标群体")
    user_input: Optional[str] = Field(None, description="用户输入")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/questionnaire/gen_questionnaire_expected_result.jinja"


class GenQuestionnaireExpectedResultResponse(BaseAgentResponse):
    analysis: str = Field(..., description="分析过程")
    expected_results: str = Field(..., description="预期结果")


class GenQuestionnaireExpectedResultAgent(BaseAgent):
    """
    生成问卷预期结果
    只有在用户未输入调查结果预期时才会调用这个Agent
    """
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/questionnaire/gen_questionnaire_expected_result.jinja"
        self.input_type = GenQuestionnaireExpectedResultInput
        self.output_type = GenQuestionnaireExpectedResultResponse
        super().__init__(options, failover_options_list)

    def render_template_with_input(self, agent_input: BaseAgentInput):
        return get_template(self.system_message_template_path, **agent_input.model_dump())
