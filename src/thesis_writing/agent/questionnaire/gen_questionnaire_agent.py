import json
import re
from typing import Optional, List

from pydantic import Field, BaseModel, conlist

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgentResponse, BaseAgent
from thesis_writing.agent.questionnaire.questionnaire import Question
from thesis_writing.utils.logger import get_logger
from thesis_writing.utils.template_parser import get_template

logger = get_logger(__name__)


class SurveyMeta(BaseModel):
    """问卷元数据校验模型"""
    title: str = Field(..., min_length=5, max_length=50,
                       description="问卷标题，需包含核心关键词")
    description: str = Field(..., min_length=5, max_length=200,
                             description="调研目的说明，需体现研究维度")


question_type = {
    "single_choice": "单选题",
    "multiple_choice": "多选题",
    "likert_scale": "评分题",
    "open_end": "开放题"
}


class GenerateQuestionnaireAgentResponse(BaseAgentResponse):
    """完整的问卷生成响应模型"""
    survey_meta: SurveyMeta = Field(..., description="问卷元数据，包含标题、描述等信息")
    questions: conlist(Question, min_length=2) = Field(..., description="问题列表")


class GenerateCustomQuestionnaireAgentInput(BaseAgentInput):
    basic_info: str = Field(..., description="主题与目的")
    target_audience: Optional[str] = Field("", description="目标群体")
    single_choice_nums: Optional[int] = Field(5, description="单选题数量")
    multiple_choice_nums: Optional[int] = Field(5, description="多选题数量")
    likert_scale_nums: Optional[int] = Field(3, description="评分题数量")
    open_end_nums: Optional[int] = Field(2, description="开放题数量")
    expected_results: str = Field(..., description="期望结果")
    user_input: Optional[str] = Field(None, description="用户输入")


    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/questionnaire/gen_questionnaire_custom.jinja"


class GenerateDefaultQuestionnaireAgentInput(BaseAgentInput):
    basic_info: str = Field(..., description="主题与目的")
    target_audience: Optional[str] = Field("", description="目标群体")
    question_nums: Optional[int] = Field(15, description="题目数量")
    expected_results: str = Field(..., description="期望结果")
    user_input: Optional[str] = Field(None, description="用户输入")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/questionnaire/gen_questionnaire_default.jinja"


class GenerateQuestionnaireAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        super().__init__(options, failover_options_list)

    def deserialize_parser_output(self, output) -> GenerateQuestionnaireAgentResponse:
        val_output = re.sub(
            r'"type":\s*"multi_choice"',  # 匹配 "type": "multi_choice" 模式
            '"type": "multiple_choice"',  # 替换为 "type": "multiple_choice"
            json.dumps(output)
        )
        val_output = re.sub(r'"id":\s*"([A-PR-Z])(\d+)"',  # 将不是以Q开头的字母编号改为以Q开头
                            lambda m: f'"id": "Q{m.group(2)}"',  # 替换为Q+数字
                            val_output)
        response = GenerateQuestionnaireAgentResponse.model_validate(json.loads(val_output))
        for question in response.questions:
            if "单选" in question.stem:
                continue
            if "（最多选" in question.stem:
                if question.type != 'multiple_choice':
                    question.type = 'multiple_choice'
            if question.type != "multiple_choice":
                question.stem = f"{question.stem.strip()}（{question_type.get(question.type)}）"
            del question.distributions
        return response

    def render_template_with_input(self, agent_input: BaseAgentInput):
        return get_template(self.system_message_template_path, **agent_input.model_dump())


class GenerateCustomQuestionnaireAgent(GenerateQuestionnaireAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/questionnaire/gen_questionnaire_custom.jinja"
        self.input_type = GenerateCustomQuestionnaireAgentInput
        self.output_type = GenerateQuestionnaireAgentResponse
        super().__init__(options, failover_options_list)


class GenerateDefaultQuestionnaireAgent(GenerateQuestionnaireAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/questionnaire/gen_questionnaire_default.jinja"
        self.input_type = GenerateDefaultQuestionnaireAgentInput
        self.output_type = GenerateQuestionnaireAgentResponse
        super().__init__(options, failover_options_list)
