from typing import Optional, Literal, Union, Annotated

from pydantic import Field, BaseModel, conlist, constr


class QuestionBase(BaseModel):
    """问题基类"""
    id: constr(pattern=r'^Q\d+$') = Field(
        ...,
        description="问题ID，格式必须为Q后跟数字（如Q123）"
    )
    stem: str = Field(..., min_length=5, max_length=150,
                      description="题干文本，不得包含诱导性表述")
    section_type: Literal["background", "core", "feedback"] = Field(...,
                                                                    description="章节类型：background/core/feedback")


class SingleChoiceQuestion(QuestionBase):
    type: Literal["single_choice"] = "single_choice"
    options: conlist(str, min_length=2, max_length=10) = Field(..., description="选项列表")
    distributions: Optional[conlist(float, min_length=2, max_length=10)] = Field(None, description="选项人数分布")


class MultipleChoiceQuestion(QuestionBase):
    type: Literal["multiple_choice"] = "multiple_choice"
    options: conlist(str, min_length=2, max_length=15) = Field(..., description="多选选项列表")
    distributions: Optional[conlist(float, min_length=2, max_length=10)] = Field(None, description="选项人数分布")


class LikertScaleQuestion(QuestionBase):
    type: Literal["likert_scale"] = "likert_scale"
    options: conlist(str, min_length=5, max_length=10) = Field(...,
                                                               description="五级李克特量表，需包含从负面到正面的递进")
    distributions: Optional[conlist(float, min_length=2, max_length=10)] = Field(None, description="选项人数分布")


class MatrixQuestion(QuestionBase):
    type: Literal["matrix"] = "matrix"
    sub_questions: conlist(str, min_length=3, max_length=7) = Field(...,
                                                                    description="矩阵子问题，至少3个测量维度")
    scale: conlist(str, min_length=3, max_length=7) = Field(...,
                                                            description="统一评价量表")


class OpenEndQuestion(QuestionBase):
    type: Literal["open_end"] = "open_end"
    placeholder: Optional[str] = Field(None, max_length=500, description="输入框占位文本")
    distributions: Optional[conlist(float, min_length=2, max_length=10)] = Field(None, description="人数分布")


class DropDownQuestion(QuestionBase):
    type: Literal["dropdown"]
    options: conlist(str, min_length=2) = Field(..., description="下拉选项")


Question = Annotated[
    Union[SingleChoiceQuestion, MultipleChoiceQuestion, LikertScaleQuestion, OpenEndQuestion],
    Field(discriminator="type")
]


class Analysis(BaseModel):
    analysis_title: str = Field(..., description="数据分析标题")
    analysis_content: str = Field(..., description="数据分析内容")


class Suggestion(BaseModel):
    suggestion_title: str = Field(..., description="建议标题")
    suggestion_content: str = Field(..., description="建议内容")
