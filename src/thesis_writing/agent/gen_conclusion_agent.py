from datetime import datetime
from typing import Optional, List

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class GenerateConclusionAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    introduction: Optional[str] = Field(..., description="绪论")
    main_content: Optional[str] = Field(..., description="正文")
    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    conclusion_writing_plan: str = Field(..., description="结论写作计划", min_length=1)
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_conclusion.jinja"


class AgentOutputParaNode(BaseModel):
    title: str = Field(..., description="章节标题")
    content: Optional[str] = Field(None, description="内容")
    segments: Optional[List["AgentOutputParaNode"]] = Field(None, description="子段落")

    def to_plain_txt(self) -> str:
        if not self.segments:
            return "\n" + self.title + "\n" + (self.content or "")
        else:
            return "\n" + self.title + "\n" + "\n".join([segment.to_plain_txt() for segment in self.segments])


class GenerateConclusionAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="思路")
    conclusion: AgentOutputParaNode = Field(None, description="结论")


class GenerateConclusionAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_conclusion.jinja"
        self.input_type = GenerateConclusionAgentInput
        self.output_type = GenerateConclusionAgentResponse
        super().__init__(options, failover_options_list)
