import json
from typing import Optional, List

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class SegmentToReview(BaseModel):
    title: str = Field(..., description="章节标题")
    segment_id: Optional[str] = Field(None, description="章节ID, segments为空时，该字段不为空")
    content: Optional[str] = Field(None, description="章节内容, segments为空时，该字段不为空")
    segments: Optional[List["SegmentToReview"]] = Field(None, description="小节集合")


class ReviewSegmentsAgentInput(BaseAgentInput):
    focus_segment: SegmentToReview = Field(None, description="当前节内容")
    chapter_list: list[SegmentToReview] = Field(None, description="全文内容")
    focus_segment_content: str = Field(None, description="当前节内容")
    paper_content: str = Field(None, description="全文内容")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/review_segments.jinja"
        self.focus_segment_content = self.focus_segment.model_dump_json(exclude_none=True)
        self.paper_content = json.dumps([chapter.model_dump(exclude_none=True) for chapter in self.chapter_list],
                                        separators=(',', ':'), indent=None, ensure_ascii=False)


class ReviewSegmentsAgentResponse(BaseAgentResponse):
    thought: str = Field(None, description="解释原因")
    results: list[str] = Field(None, description="内容重复的小节ID集合")


class ReviewSegmentsAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/review_segments.jinja"
        self.input_type = ReviewSegmentsAgentInput
        self.output_type = ReviewSegmentsAgentResponse
        super().__init__(options, failover_options_list)
