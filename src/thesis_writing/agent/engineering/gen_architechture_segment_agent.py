import json
import re
from datetime import datetime
from enum import StrEnum
from typing import Optional, List

import xmltodict
from langchain_core.output_parsers import XMLOutputParser
from pydantic import Field, BaseModel, ConfigDict

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import (
    BaseAgentInput,
    BaseAgent,
    ChainRunnableConfig,
    BaseAgentResponse,
)
from thesis_writing.agent.engineering.gen_engineering_segment_agent import (
    DeliverableAddition,
    DeliverableType,
)
from thesis_writing.agent.gen_segment_agent import (
    Reference,
    adjust_chart_position,
    remove_duplicate_tags,
    fix_reference_tags,
    replace_quotes,
    remove_invalid_tags,
    correct_addition_tags,
)
from thesis_writing.utils.reference import ThesisReference
from thesis_writing.utils.template_parser import get_template
from thesis_writing.utils.xml_repair import BadXMLRepair
from thesis_writing_server.models.task_output import ParaPlanNode


class GenArchSegmentAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    complete_writing_plan: List[ParaPlanNode] = Field(
        ..., description="全文写作计划", min_length=1
    )
    segment_title: Optional[str] = Field(None, description="当前目标小节标题")
    segment_writing_plan: ParaPlanNode = Field(..., description="当前目标小节写作计划")
    current_date: str = Field(
        default_factory=lambda: datetime.now().strftime("%Y-%m-%d"),
        description="当前日期",
        min_length=1,
    )
    additions: Optional[List[DeliverableAddition]] = Field(None, description="补充信息")
    context: Optional[str] = Field(None, description="本章已完成小节内容")
    references: Optional[List[ThesisReference]] = Field(None, description="参考文献")
    global_project_info: Optional[str] = Field(None, description="项目全局信息")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = (
            "user_prompt/engineering/gen_architechture_segment.jinja"
        )

    def to_msg(self):
        kwargs = self.model_dump(exclude={"references", "additions", "complete_writing_plan", "segment_writing_plan"})

        cur_chapter_plan = None
        for chapter_plan in self.complete_writing_plan:
            if chapter_plan.get_by_id(self.segment_writing_plan.node_id):
                cur_chapter_plan = chapter_plan

        kwargs["cur_segment"] = self.segment_writing_plan.title
        kwargs["chapter_writing_plan"] = json.dumps(cur_chapter_plan.model_dump(exclude_none=True), ensure_ascii=False)

        if not self.references or len(self.references) == 0:
            kwargs["references"] = '无'
        else:
            kwargs["references"] = "\n".join([reference.to_detail_str() for reference in self.references])

        segment_additions_str = ''
        for each in self.additions:
            segment_additions_str += f"<addition>\n{each.to_chat_string()}\n</addition>\n"
        if len(segment_additions_str) == 0:
            segment_additions_str = "无"
        kwargs["additions"] = segment_additions_str

        return get_template(self.user_message_template_path, **kwargs)


class Segment(BaseModel):
    title: Optional[str] = Field(None, description="段落标题")
    content: str = Field(..., description="段落内容")


class GenerateArchSegmentAgentResponse(BaseAgentResponse):
    reference_thought: Optional[str] = Field(None, description="参考文献思考过程")
    references: Optional[List[Reference]] = Field(None, description="引用的参考文献列表")
    segment_thought: Optional[str] = Field(None, description="小节内容思考过程")
    segments: Optional[List[Segment]] = Field(None, description="段落列表")
    segment_content: Optional[str] = Field(None, description="段落内容")

    def get_combined_content(self) -> str:
        """获取合并后的所有段落内容"""
        if not self.segments:
            return ""
        return "\n\n".join(segment.content for segment in self.segments)


class GenerateArchSegmentAgent(BaseAgent):
    def __init__(
        self, options: AgentOptions, failover_options_list: List[AgentOptions]
    ):
        self.input_type = GenArchSegmentAgentInput
        self.output_type = GenerateArchSegmentAgentResponse
        self.system_message_template_path = (
            "system_prompt/engineering/gen_architechture_segment.jinja"
        )
        super().__init__(options, failover_options_list)

    def invoke(
        self, agent_input: GenArchSegmentAgentInput, config: ChainRunnableConfig = None
    ) -> GenerateArchSegmentAgentResponse:
        result: GenerateArchSegmentAgentResponse = super().invoke(agent_input, config)
        result.segment_content = result.get_combined_content()
        self.repair_segment_content(agent_input, result)
        return result

    async def ainvoke(
        self, agent_input: GenArchSegmentAgentInput, config: ChainRunnableConfig = None
    ) -> GenerateArchSegmentAgentResponse:
        result: GenerateArchSegmentAgentResponse = await super().ainvoke(agent_input, config)
        result.segment_content = result.get_combined_content()
        self.repair_segment_content(agent_input, result)
        return result

    def repair_segment_content(
        self,
        agent_input: GenArchSegmentAgentInput,
        result: GenerateArchSegmentAgentResponse,
    ):
        if not result or not result.segment_content:
            return

        result.segment_content = remove_invalid_tags(
            result.segment_content, agent_input.additions
        )
        result.segment_content = correct_addition_tags(
            result.segment_content, agent_input.additions
        )
        result.segment_content = remove_duplicate_tags(result.segment_content)
        result.segment_content = fix_reference_tags(
            result.segment_content, agent_input.references
        )
        result.segment_content = replace_quotes(result.segment_content)
