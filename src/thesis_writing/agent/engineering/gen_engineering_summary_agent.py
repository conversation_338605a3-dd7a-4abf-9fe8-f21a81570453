from datetime import datetime
from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import (
    BaseAgent,
    BaseAgentResponse,
    BaseAgentInput,
)


class GenerateEngineeringSummaryAgentInput(BaseAgentInput):
    subject: str = Field(..., description="项目标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    project_requirements: str = Field(..., description="项目需求", min_length=1)
    current_date: str = Field(
        default_factory=lambda: datetime.now().strftime("%Y-%m-%d"),
        description="当前日期",
        min_length=1,
    )

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = (
            "user_prompt/engineering/gen_engineering_summary.jinja"
        )


class GenerateEngineeringSummaryAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="分析思路")
    summary: str = Field(..., description="工程项目全文概要描述")


class GenerateEngineeringSummaryAgent(BaseAgent):
    def __init__(
        self, options: AgentOptions, failover_options_list: List[AgentOptions]
    ):
        self.system_message_template_path = (
            "system_prompt/engineering/gen_engineering_summary.jinja"
        )
        self.input_type = GenerateEngineeringSummaryAgentInput
        self.output_type = GenerateEngineeringSummaryAgentResponse
        super().__init__(options, failover_options_list)
