from token import OP
from typing import Optional, List, OrderedDict

from pydantic import ConfigDict, Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse
from thesis_writing.agent.gen_plan_agent import GeneratePlanAgentResponse, GeneratePlanNode, clean_duplicate_deliverable_ids
from thesis_writing.entity.enums import WritingPlanContentType
from thesis_writing.utils.logger import get_logger
from thesis_writing.agent.engineering.gen_engineering_plan_agent import GenerateEngineeringPlanAgentInput

from pydantic import BaseModel, Field

logger = get_logger(__name__)

class ReviseSuggestion(BaseModel):
    node_id: Optional[str] = Field(None, description="节点ID")
    comment: str = Field(..., description="问题描述")
    suggestion: Optional[str] = Field(None, description="建议修改方案")


class ReviseEngineeringPlanAgentInput(GenerateEngineeringPlanAgentInput):
    old_plan: List[GeneratePlanNode] = Field(..., description="旧版写作计划")
    revise_suggestions: List[ReviseSuggestion] = Field(..., description="问题列表")
    all_old_node_ids: Optional[str] = Field(None, description="所有旧节点id")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/engineering/revise_engineering_plan.jinja"
        if self.all_old_node_ids is None:
            node_ids = []
            for each in self.old_plan:
                node_ids.extend(each.get_all_node_ids())
            self.all_old_node_ids = "\n".join(node_ids)

class ReviseEngineeringPlanNode(GeneratePlanNode):
    old_ids: Optional[List[str]] = Field(None, description="旧版写作计划中对应的节点ID")
    children: Optional[List["ReviseEngineeringPlanNode"]] = Field(None, description="子节点")


    def find_node_by_old_id(self, old_id: str) -> Optional["ReviseEngineeringPlanNode"]:
        if self.old_ids and old_id in self.old_ids:
            return self
        if self.children:
            for child in self.children:
                result = child.find_node_by_old_id(old_id)
                if result:
                    return result

    @classmethod
    def reorder_properties(self, schema):
        """重新排序属性"""
        desired_order = [
            'title',
            'content_type',
            'old_ids',
            'writing_length',
            'description',
            'deliverable_ids',
            'children'
        ]
        if 'properties' in schema:
            ordered_props = OrderedDict()
            for field_name in desired_order:
                if field_name in schema['properties']:
                    ordered_props[field_name] = schema['properties'][field_name]
            for field_name, field_schema in schema['properties'].items():
                if field_name == 'id' or field_name == 'node_analysis':
                    continue
                if field_name not in ordered_props:
                    ordered_props[field_name] = field_schema
            schema['properties'] = dict(ordered_props)

ReviseEngineeringPlanNode.model_rebuild()


class ReviseEngineeringPlanAgentResponse(GeneratePlanAgentResponse):
    deleted_analysis: Optional[str] = Field(None, description="被删除的节点分析")
    deleted_nodes_ids: Optional[List[str]] = Field(None, description="被删除的节点ID")
    writing_plan_nodes: List[ReviseEngineeringPlanNode] = Field(None, description="写作计划")

    @classmethod
    def model_json_schema(cls, **kwargs):
        schema = super().model_json_schema(**kwargs)
        desired_order = [
            'analysis',
            'deleted_analysis',
            'deleted_nodes_ids',
            'writing_plan_nodes'
        ]
        if 'properties' in schema:
            ordered_props = OrderedDict()
            for field_name in desired_order:
                if field_name in schema['properties']:
                    ordered_props[field_name] = schema['properties'][field_name]
            for field_name, field_schema in schema['properties'].items():
                if field_name not in ordered_props:
                    ordered_props[field_name] = field_schema
            schema['properties'] = dict(ordered_props)
        return schema


class ReviseEngineeringPlanAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/engineering/revise_engineering_plan.jinja"
        self.input_type = ReviseEngineeringPlanAgentInput
        self.output_type = ReviseEngineeringPlanAgentResponse
        if "aliyun" in options.base_url:
            options.response_format = {"type": "json_object"}
        elif "36.155.134.59" in options.base_url:
            schema = ReviseEngineeringPlanAgentResponse.model_json_schema(mode='serialization')
            self._reorder_node_properties(schema)
            options.response_format = {"type": "json_schema", "json_schema": {"name": "ReviseEngineeringPlanAgentResponse", "schema": schema}}
        super().__init__(options, failover_options_list)

    def _reorder_node_properties(self, schema):
        if isinstance(schema, dict):
            if '$defs' in schema:
                for def_name, def_schema in schema['$defs'].items():
                    if 'ReviseEngineeringPlanNode' in def_name:
                        ReviseEngineeringPlanNode.reorder_properties(def_schema)
                    self._reorder_node_properties(def_schema)
            for value in schema.values():
                if isinstance(value, (dict, list)):
                    self._reorder_node_properties(value)
        elif isinstance(schema, list):
            for item in schema:
                self._reorder_node_properties(item)

    def check_deleted_node_ids(self, old_plan: List[ReviseEngineeringPlanNode], deleted_nodes_ids: List[str], new_nodes: List[ReviseEngineeringPlanNode]):
        input_all_node_ids: List[str] = []
        self.fill_all_leaf_node_ids(old_plan, input_all_node_ids)

        output_all_old_node_ids: List[str] = []
        self.fill_all_old_node_ids(new_nodes, output_all_old_node_ids)
        if deleted_nodes_ids:
            output_all_deleted_ids: List[str] = []
            self.fill_all_deleted_ids(old_plan, deleted_nodes_ids, output_all_deleted_ids)
            deleted_nodes_ids = list(set(deleted_nodes_ids))
            output_all_old_node_ids.extend(deleted_nodes_ids)

        def get_parent_id(node_id: str) -> Optional[str]:
            parts = node_id.split('.')
            if len(parts) <= 1:
                return None
            return '.'.join(parts[:-1])

        for input_node_id in input_all_node_ids:
            if input_node_id in output_all_old_node_ids:
                continue
            parts_count = len(input_node_id.split('.'))
            if parts_count >= 3:
                parent_id = get_parent_id(input_node_id)
                exist = False
                for node in new_nodes:
                    exist_node = node.find_node_by_old_id(parent_id)
                    if exist_node and not exist_node.children:
                        logger.warning(f"Node ID '{input_node_id}' from the old plan is missing in the new plan. Auto add it to node {exist_node.id}.")
                        exist_node.old_ids.append(input_node_id)
                        exist = True
                        break
                if exist:
                    continue

            raise ValueError(f"Node ID '{input_node_id}' from the old plan is missing in the new plan.")

    def fill_all_deleted_ids(self, nodes: List[ReviseEngineeringPlanNode], all_deleted_ids: List[str], output_all_deleted_ids: list[str]):
        for node in nodes:
            if node.id in all_deleted_ids:
                output_all_deleted_ids.append(node.id)
                all_node_ids = []
                self.fill_all_leaf_node_ids(nodes, all_node_ids)
                output_all_deleted_ids.extend(all_node_ids)
            if node.children:
                self.fill_all_deleted_ids(node.children, all_deleted_ids, output_all_deleted_ids)

    def fill_all_leaf_node_ids(self, nodes: List[ReviseEngineeringPlanNode], all_node_ids: List[str]):
        for node in nodes:
            if node.children:
                self.fill_all_leaf_node_ids(node.children, all_node_ids)
            else:
                all_node_ids.append(node.id)

    def fill_all_old_node_ids(self, new_nodes, all_old_node_ids):
        for node in new_nodes:
            if node.old_ids:
                all_old_node_ids.extend(node.old_ids)
            if node.children:
                self.fill_all_old_node_ids(node.children, all_old_node_ids)

    def validate_output(self, input: ReviseEngineeringPlanAgentInput, output: ReviseEngineeringPlanAgentResponse, remaining_retries=0):
        super().validate_output(input, output, remaining_retries)
        output.generate_node_id()
        self.check_deleted_node_ids(input.old_plan, output.deleted_nodes_ids, output.writing_plan_nodes)
        self.check_duplicate_node_ids(output.writing_plan_nodes)
        clean_duplicate_deliverable_ids(output.writing_plan_nodes)

    def check_duplicate_node_ids(self, new_nodes: List[ReviseEngineeringPlanNode]):
        output_all_old_node_ids = []
        self.fill_all_old_node_ids(new_nodes, output_all_old_node_ids)
        if len(output_all_old_node_ids) != len(set(output_all_old_node_ids)):
            raise ValueError("Duplicate node IDs found in the new plan.")