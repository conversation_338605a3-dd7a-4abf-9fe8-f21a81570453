import re
from datetime import datetime
from enum import StrEnum
from typing import Optional, List

from pydantic import Field, BaseModel, ConfigDict

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgent, ChainRunnableConfig
from thesis_writing.agent.gen_segment_agent import GenerateSegmentAgentResponse, adjust_chart_position, \
    fix_reference_tags, replace_quotes, remove_duplicate_tags
from thesis_writing.utils.reference import ThesisReference
from thesis_writing.utils.template_parser import get_template


class DeliverableType(StrEnum):
    Table = 'Table'
    Image = 'Image'


class DeliverableAddition(BaseModel):
    model_config = ConfigDict(populate_by_name=True)
    node_id: Optional[str] = Field(None, description="节点ID")
    id: Optional[str] = Field(None, alias="题注的序号", description="题注的序号")
    deliverable_id: str = Field(..., alias="交付物ID", description="交付物ID")
    type: str = Field(..., alias="类型", description="类型")
    title: Optional[str] = Field(None, alias="名称", description="名称")
    description: Optional[str] = Field(None, alias="描述", description="描述")
    thought: Optional[str] = Field(None, alias="思考过程", description="思考过程")
    data: Optional[str] = Field(None, alias="工作成果", description="工作成果")
    url: Optional[str] = Field(None, alias="内容资源链接", description="内容资源链接")

    def to_chat_string(self) -> str:
        if self.type == DeliverableType.Image or self.type == DeliverableType.Table:
            return f"""\
**类型**:{self.type}
**内容ID**：{self.id}
**标题**：{self.title}
**描述**：{self.description}
**内容**：{self.data or ""}
"""
        return f"""\
**类型**：{self.type}
**内容描述**：\n{self.description}
**思考过程**：\n{self.thought}
**工作过程与成果**：\n{self.data}
"""


class GenEngineeringSegmentAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    segment_title: Optional[str] = Field(None, description="当前目标小节标题")
    segment_writing_plan: str = Field(..., description="当前目标小节写作计划", min_length=1)
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)
    additions: Optional[List[DeliverableAddition]] = Field(None, description="补充信息")
    context: Optional[str] = Field(None, description="本章已完成小节内容")
    references: Optional[List[ThesisReference]] = Field(None, description="参考文献")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/engineering/gen_engineering_segment.jinja"

    def to_msg(self):
        kwargs = self.model_dump(exclude={"references", "additions"})
        if not self.references or len(self.references) == 0:
            kwargs["references"] = '无'
        else:
            kwargs["references"] = "\n".join([reference.to_detail_str() for reference in self.references])

        segment_additions_str = ''
        for each in self.additions:
            segment_additions_str += f"<addition>\n{each.to_chat_string()}\n</addition>\n"
        if len(segment_additions_str) == 0:
            segment_additions_str = "无"
        kwargs["additions"] = segment_additions_str

        return get_template(self.user_message_template_path, **kwargs)


class GenerateEngineeringSegmentAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.input_type = GenEngineeringSegmentAgentInput
        self.output_type = GenerateSegmentAgentResponse
        self.system_message_template_path = "system_prompt/engineering/gen_engineering_segment.jinja"
        super().__init__(options, failover_options_list)

    def invoke(self, agent_input: GenEngineeringSegmentAgentInput, config: ChainRunnableConfig = None) \
            -> GenerateSegmentAgentResponse:
        result: GenerateSegmentAgentResponse = super().invoke(agent_input, config)
        self._repair_segment_content(agent_input, result)
        return result

    async def ainvoke(self, agent_input: GenEngineeringSegmentAgentInput, config: ChainRunnableConfig = None) \
            -> GenerateSegmentAgentResponse:
        result: GenerateSegmentAgentResponse = await super().ainvoke(agent_input, config)
        self._repair_segment_content(agent_input, result)
        return result

    def _repair_segment_content(self, agent_input: GenEngineeringSegmentAgentInput,
                                result: GenerateSegmentAgentResponse):
        if not result or not result.segment_content:
            return
        self._remove_invalid_tags(result, agent_input.additions)
        self._correct_addition_tags(result, agent_input.additions)
        remove_duplicate_tags(result)
        fix_reference_tags(result, agent_input.references)
        replace_quotes(result)

    def _remove_invalid_tags(self, result: GenerateSegmentAgentResponse, additions: List[DeliverableAddition]):
        invalid_chart_or_table = self._get_invalid_chart_or_table(result.segment_content, additions)
        for tag, id in invalid_chart_or_table:
            result.segment_content = re.sub(rf'<{tag} id="{id}"[^>]*/>', '', result.segment_content)
            result.segment_content = re.sub(rf'<{tag} id="{id}"[^>]*>.*?</{tag}>', '', result.segment_content)
        result.segment_content = result.segment_content.replace("**", "").replace('<addition id="None"/>', "").replace(
            '<addition id="None"></addition>', "").replace('<addition>None</addition>', "")

    def _get_invalid_chart_or_table(self, segment_content, additions):
        additions_ids = {each.id for each in additions} if additions else set()
        invalid_chart_or_table = [
            match for match in re.findall(r"<(chart|figure|table|addition) id=\"(.*?)\"", segment_content)
            if match[1] not in additions_ids
        ]
        return invalid_chart_or_table

    def _correct_addition_tags(self, result: GenerateSegmentAgentResponse, additions: List[DeliverableAddition]):
        tag_types = ["table", "image", "figure", "addition", "chart"]
        for addition in additions:
            if addition.type not in [DeliverableType.Image, DeliverableType.Table]:
                continue
            correct_tag = f'<chart id="{addition.id}"/>'
            incorrect_tags = [f'<{tag} id="{addition.id}"/>' for tag in tag_types]
            incorrect_tags += [
                re.compile(rf'<{tag} id="{addition.id}"[^>]*>.*?</{tag}>', re.DOTALL)
                for tag in tag_types
            ]
            for incorrect in incorrect_tags:
                if isinstance(incorrect, str):
                    result.segment_content = result.segment_content.replace(incorrect, correct_tag)
                else:
                    result.segment_content = incorrect.sub(correct_tag, result.segment_content)
            adjust_chart_position(result, correct_tag, addition.id)
