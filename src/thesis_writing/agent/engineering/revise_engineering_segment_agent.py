import re
from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentResponse
from thesis_writing.agent.engineering.gen_engineering_segment_agent import GenEngineeringSegmentAgentInput, GenerateEngineeringSegmentAgent
from thesis_writing.agent.engineering.revise_engineering_plan_agent import ReviseSuggestion
from thesis_writing.agent.gen_segment_agent import GenerateSegmentAgentResponse, correct_addition_tags, remove_duplicate_tags, remove_invalid_tags, replace_quotes
from thesis_writing.utils.reference import ReferenceManager, ThesisReference

class ReviseEngineeringSegmentAgentInput(GenEngineeringSegmentAgentInput):
    segment_content: str = Field(..., description="当前小节内容")
    suggestions: str = Field(..., description="当前小节的专家评估建议")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/engineering/revise_engineering_segment.jinja"


class ReviseEngineeringSegmentAgent(GenerateEngineeringSegmentAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        super().__init__(options, failover_options_list)
        self.system_message_template_path = "system_prompt/engineering/revise_engineering_segment.jinja"
        self.input_type = ReviseEngineeringSegmentAgentInput

    def validate_output(
        self, input: ReviseEngineeringSegmentAgentInput, output: GenerateSegmentAgentResponse, remaining_retries=0
    ):
        super().validate_output(input, output, remaining_retries)
        self._check_placeholders(input, output)

    def _repair_segment_content(self, agent_input: ReviseEngineeringSegmentAgentInput, result: GenerateSegmentAgentResponse):
        if not result or not result.segment_content:
            return
        result.segment_content = remove_invalid_tags(result.segment_content, agent_input.additions)
        result.segment_content = correct_addition_tags(result.segment_content, agent_input.additions)
        result.segment_content = remove_duplicate_tags(result.segment_content)
        self._fix_reference_tags(result, agent_input.references)
        result.segment_content = replace_quotes(result.segment_content)
        self._check_placeholders(agent_input, result)
        self._replace_placeholders(result)


    def _fix_reference_tags(self, result: GenerateSegmentAgentResponse, references: List[ThesisReference]):
        reference_manager = ReferenceManager()
        reference_manager.extend_references(references)
        result.segment_content = self.split_strict_nested_brackets(result.segment_content)
        for ref in references:
            tag, wrong_tag = f'[[{ref.id}]]', f'[{ref.id}]'
            if tag not in result.segment_content and wrong_tag in result.segment_content:
                result.segment_content = result.segment_content.replace(wrong_tag, tag)
        _, not_found_ids = reference_manager.collect_ref(result.segment_content)
        for each in not_found_ids:
            result.segment_content = result.segment_content.replace(f'[[{each}]]', '')

    def split_strict_nested_brackets(self, s):
        pattern = r'\[\[([\d\,，、\s\]\[]+)\]\]'

        def replacer(match):
            content = match.group(1)
            content = content.replace(' ', '')
            replace_pattern = r'[\]\[,，、\s]+'
            content = re.sub(replace_pattern, ',', content)
            parts = content.split(',')
            if all(part.isdigit() for part in parts):
                return ''.join(f'[[{part}]]' for part in parts)

            return match.group(0)

        result = re.sub(pattern, replacer, s)
        return result

    def _check_placeholders(self, agent_input: ReviseEngineeringSegmentAgentInput, result: GenerateSegmentAgentResponse):
        if not result or not result.segment_content:
            return
        pattern = r'\[\[\[[^\]]*\]\]\]'
        old_placeholders = re.findall(pattern, agent_input.segment_content)
        if not old_placeholders:
            return
        new_placeholders = re.findall(pattern, result.segment_content)
        for each in new_placeholders:
            if each not in old_placeholders:
                raise ValueError(f"Placeholder '{each}' not found in old segment content.")

    def _replace_placeholders(self, result: GenerateSegmentAgentResponse):
        def custom_replace(match):
            start = match.start()
            prefix = result.segment_content[max(0, start-20):start]
            if re.search(r'text=\[\d+((-\d+)|(,\d+))*$', prefix):
                return ']]]]'
            else:
                return ']]]'
        result.segment_content = result.segment_content.replace('[[[[', '[[[').replace("[[[text]]]", "")
        result.segment_content = re.sub(r'\]\]\]\]', lambda m: custom_replace(m), result.segment_content)
        pattern = r'\[\[\[[^\]]*\]\]\]'
        new_placeholders = re.findall(pattern, result.segment_content)
        if not new_placeholders:
            return
        for placeholder in new_placeholders:

            if f'{placeholder}。' in result.segment_content:
                result.segment_content = result.segment_content.replace(f'{placeholder}。', f'。{placeholder}')