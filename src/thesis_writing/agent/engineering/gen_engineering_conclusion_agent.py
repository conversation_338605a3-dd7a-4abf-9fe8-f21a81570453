from datetime import datetime
from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import (
    BaseAgent,
    BaseAgentResponse,
    BaseAgentInput,
)
from thesis_writing.agent.gen_conclusion_agent import AgentOutputParaNode


class ConclusionSegment(BaseAgentResponse):
    title: str = Field(..., description="段落标题")
    content: str = Field(..., description="段落内容")


class Conclusion(BaseAgentResponse):
    title: str = Field(..., description="结论标题")
    content: str = Field(..., description="结论内容（当没有子节点时）")
    segments: List[ConclusionSegment] = Field(default=[], description="结论段落列表")


class GenerateEngineeringConclusionAgentInput(BaseAgentInput):
    subject: str = Field(..., description="毕业设计标题", min_length=1)
    user_design: str = Field(..., description="毕业设计工作内容", min_length=1)
    conclusion_writing_plan: str = Field(..., description="结论写作计划", min_length=1)
    current_date: str = Field(
        default_factory=lambda: datetime.now().strftime("%Y-%m-%d"),
        description="当前日期",
        min_length=1,
    )

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = (
            "user_prompt/engineering/gen_engineering_conclusion.jinja"
        )


class GenerateEngineeringConclusionAgentResponse(BaseAgentResponse):
    thought: Optional[str] = Field(None, description="思路")
    conclusion: AgentOutputParaNode = Field(None, description="结论")


class GenerateEngineeringConclusionAgent(BaseAgent):
    def __init__(
        self, options: AgentOptions, failover_options_list: List[AgentOptions]
    ):
        self.system_message_template_path = (
            "system_prompt/engineering/gen_engineering_conclusion.jinja"
        )
        self.input_type = GenerateEngineeringConclusionAgentInput
        self.output_type = GenerateEngineeringConclusionAgentResponse
        super().__init__(options, failover_options_list)
