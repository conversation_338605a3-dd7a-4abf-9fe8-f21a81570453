from typing import Optional, List
from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import (
    BaseAgent,
    BaseAgentResponse,
    BaseAgentInput,
)


class GenerateEngineeringTaskStatementAgentInput(BaseAgentInput):
    major: str = Field(..., description="学生专业", min_length=1)
    task_catalog: str = Field(..., description="任务书目录", min_length=1)
    example: Optional[str] = Field(default=None, description="任务书样本参考")
    project_name: Optional[str] = Field(None, description="项目名称")
    project_requirements: Optional[str] = Field(None, description="项目各阶段任务信息")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = (
            "user_prompt/engineering/gen_engineering_task_statement.jinja"
        )


class TaskContent(BaseAgentResponse):
    title: str = Field(..., description="任务书目录中的内容模块名称")
    content: str = Field(..., description="输出本内容模块的具体内容")
    chart: Optional[str] = Field(None, description="输出本内容模块的图表")


class GenerateEngineeringTaskStatementAgentResponse(BaseAgentResponse):
    thought: str = Field(..., description="一步一步思考任务要求，输出思考过程")
    task_contents: List[TaskContent] = Field(..., description="任务书内容列表")


class GenerateEngineeringTaskStatementAgent(BaseAgent):
    def __init__(
        self, options: AgentOptions, failover_options_list: List[AgentOptions]
    ):
        self.system_message_template_path = (
            "system_prompt/engineering/gen_engineering_task_statement.jinja"
        )
        self.input_type = GenerateEngineeringTaskStatementAgentInput
        self.output_type = GenerateEngineeringTaskStatementAgentResponse
        super().__init__(options, failover_options_list)
