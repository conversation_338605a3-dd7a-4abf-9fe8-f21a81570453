from datetime import datetime
from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import (
    BaseAgent,
    BaseAgentResponse,
    BaseAgentInput,
)
from thesis_writing.agent.gen_abstraction_agent import GenerateAbstractionAgentResponse


class GenerateEngineeringAbstractKeywordsAgentInput(BaseAgentInput):
    subject: str = Field(..., description="毕业设计标题", min_length=1)
    user_design: str = Field(..., description="毕业设计工作内容", min_length=1)
    introduction: str = Field(..., description="绪论", min_length=1)
    conclusion: str = Field(..., description="结论", min_length=1)
    current_date: str = Field(
        default_factory=lambda: datetime.now().strftime("%Y-%m-%d"),
        description="当前日期",
        min_length=1,
    )

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = (
            "user_prompt/engineering/gen_engineering_abstraction_keywords.jinja"
        )


class GenerateEngineeringAbstractKeywordsAgent(BaseAgent):
    def __init__(
        self, options: AgentOptions, failover_options_list: List[AgentOptions]
    ):
        self.system_message_template_path = (
            "system_prompt/engineering/gen_engineering_abstraction_keywords.jinja"
        )
        self.input_type = GenerateEngineeringAbstractKeywordsAgentInput
        self.output_type = GenerateAbstractionAgentResponse
        super().__init__(options, failover_options_list)
