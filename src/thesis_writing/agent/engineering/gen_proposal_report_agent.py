import json
from typing import Optional, List
from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import (
    BaseAgent,
    BaseAgentResponse,
    BaseAgentInput,
)


class GenerateEngineeringProposalAgentInput(BaseAgentInput):
    major: str = Field(..., description="专业", min_length=1)
    topic_name: str = Field(..., description="课题名称", min_length=1)
    project_requirements: str = Field(..., description="项目各阶段任务信息")
    toc: Optional[str] = Field(..., description="开题报告目录", min_length=1)
    reference: Optional[str] = Field(..., description="参考文献")
    word_count: int = Field(2000, description="开题报告字数")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = (
            "user_prompt/engineering/gen_proposal_report.jinja"
        )


class ProposalContent(BaseAgentResponse):
    title: str = Field(..., description="开题报告目录中的内容模块名称")
    thought: str = Field(..., description="一步步思考当前内容模块应该重点写作哪些内容")
    content: str = Field(..., description="使用markdown格式输出本内容模块的具体内容")
    chart: Optional[str] = Field(None, description="本内容模块的图表")


class GenerateEngineeringProposalAgentResponse(BaseAgentResponse):
    proposal_contents: List[ProposalContent] = Field(
        ..., description="开题报告内容列表"
    )

    def main_content(self):
        return '\n'.join([f"{proposal_content.title}\n{proposal_content.content}" for proposal_content in self.proposal_contents])


class GenerateEngineeringProposalAgent(BaseAgent):
    def __init__(
        self, options: AgentOptions, failover_options_list: List[AgentOptions]
    ):
        self.system_message_template_path = (
            "system_prompt/engineering/gen_proposal_report.jinja"
        )
        self.input_type = GenerateEngineeringProposalAgentInput
        self.output_type = GenerateEngineeringProposalAgentResponse
        super().__init__(options, failover_options_list)
