from typing import Optional, Any, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgentResponse, BaseAgent


class GenerateEnAbstractAgentInput(BaseAgentInput):
    abstract_zh: str = Field(..., description="中文摘要", min_length=1)

    def __init__(self, **data: Any):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_en_abstraction.jinja"


class GenerateEnAbstractAgentResponse(BaseAgentResponse):
    translation: Optional[str] = Field(..., description="英文摘要")


class GenerateEnAbstractAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_en_abstraction.jinja"
        self.input_type = GenerateEnAbstractAgentInput
        self.output_type = GenerateEnAbstractAgentResponse
        super().__init__(options, failover_options_list)
