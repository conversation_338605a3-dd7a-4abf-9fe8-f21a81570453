from typing import List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput


class GenerateKeywordsAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(default="无", description="专业")
    keywords_num: str = Field(default="10个", description="关键词数量")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_keywords.jinja"


class GenerateKeywordsAgentResponse(BaseAgentResponse):
    keywords: List[str] = Field(None, description="关键词")


class GenerateKeywordsAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_keywords.jinja"
        self.input_type = GenerateKeywordsAgentInput
        self.output_type = GenerateKeywordsAgentResponse
        super().__init__(options, failover_options_list)
