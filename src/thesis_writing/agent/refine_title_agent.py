from typing import List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentInput, BaseAgentResponse


class RefineTitleAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/refine_title.jinja"


class RefineTitleAgentResponse(BaseAgentResponse):
    refined_titles: List[str] = Field(None, description="润色后标题")


class RefineTitleAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/refine_title.jinja"
        self.input_type = RefineTitleAgentInput
        self.output_type = RefineTitleAgentResponse
        super().__init__(options, failover_options_list)
