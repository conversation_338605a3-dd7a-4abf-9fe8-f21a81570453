from datetime import datetime
from typing import List
from typing import Optional

from langchain_core.output_parsers import XMLOutputParser
from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentInput, BaseAgentResponse
from thesis_writing.utils.logger import get_logger
from thesis_writing.utils.xml_repair import BadQuantumChartXMLRepair, BadXMLRepair, BadFlowChartXMLRepair

logger = get_logger(__name__)


class GenerateDiagramAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    complete_writing_plan: str = Field(None, description="全文写作计划", min_length=1)
    segment_title: str = Field(None, description="当前目标小节标题")
    segment_content: str = Field(..., description="当前目标小节内容", min_length=1)
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)
    chart_title: str = Field(..., description="当前图表标题")
    chart_info: str = Field(None, description="当前图表信息")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_diagram.jinja"


class GenerateDiagramAgentResponse(BaseAgentResponse):
    thought: str = Field(..., description="分析过程")
    diagram: str = Field(..., description="Diagram代码")


class GenerateDiagramAgent(BaseAgent):
    # 初始为可变集合，后续转换为 frozenset
    _mermaid_classes = set()  # 暂时用 set 存储
    _plantuml_classes = set()

    @classmethod
    def register_mermaid_class(cls, subclass):
        """装饰器：注册子类到 MERMAID_AGENT_CLASSES"""
        cls._mermaid_classes.add(subclass)
        return subclass  # 必须返回 subclass，以便用作装饰器

    @classmethod
    def register_plantuml_class(cls, subclass):
        """装饰器：注册子类到 MERMAID_AGENT_CLASSES"""
        cls._plantuml_classes.add(subclass)
        return subclass  # 必须返回 subclass，以便用作装饰器

    @classmethod
    def finalize_mermaid_classes(cls):
        """将可变集合转换为不可变的 frozenset"""
        cls.MERMAID_AGENT_CLASSES = frozenset(cls._mermaid_classes)
        del cls._mermaid_classes  # 清理临时变量

    @classmethod
    def finalize_plantuml_classes(cls):
        """将可变集合转换为不可变的 frozenset"""
        cls.PLANTUML_AGENT_CLASSES = frozenset(cls._plantuml_classes)
        del cls._plantuml_classes  # 清理临时变量

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        super().__init__(options, failover_options_list)

        self.input_type = GenerateDiagramAgentInput
        self.output_type = GenerateDiagramAgentResponse


    def custom_parser(self):
        bad_xml_repair = BadXMLRepair()
        xml_parser = XMLOutputParser()
        return bad_xml_repair | xml_parser

    def deserialize_parser_output(self, parser_output: dict):
        root = parser_output['root']
        thought = root[0]['thought']
        diagram = root[1]['diagram']
        return GenerateDiagramAgentResponse(thought=thought, diagram=diagram)


@GenerateDiagramAgent.register_mermaid_class
class GenerateERDiagramAgent(GenerateDiagramAgent):
    """
    Mermaid格式的实体关系图
    示例：https://mermaid.nodejs.cn/syntax/entityRelationshipDiagram.html
    """

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_mermaid_erdiagram_prompt.jinja"
        super().__init__(options, failover_options_list)


@GenerateDiagramAgent.register_mermaid_class
class GenerateSequenceDiagramAgent(GenerateDiagramAgent):
    """
    Mermaid格式的时序图
    示例：https://mermaid.nodejs.cn/syntax/sequenceDiagram.html
    """

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_mermaid_sequence_diagram_prompt.jinja"
        super().__init__(options, failover_options_list)


@GenerateDiagramAgent.register_mermaid_class
class GenerateClassDiagramAgent(GenerateDiagramAgent):
    """
    Mermaid格式的类图
    示例：https://mermaid.nodejs.cn/syntax/classDiagram.html
    """

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_mermaid_class_diagram_prompt.jinja"
        super().__init__(options, failover_options_list)


@GenerateDiagramAgent.register_mermaid_class
class GenerateStateDiagramAgent(GenerateDiagramAgent):
    """
    Mermaid格式的状态图
    示例：https://mermaid.nodejs.cn/syntax/stateDiagram.html
    """

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_mermaid_state_diagram_prompt.jinja"
        super().__init__(options, failover_options_list)


@GenerateDiagramAgent.register_mermaid_class
class GenerateGanttDiagramAgent(GenerateDiagramAgent):
    """
    Mermaid格式的甘特图
    示例：https://mermaid.js.org/syntax/gantt.html
    """

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_mermaid_gantt_diagram_prompt.jinja"
        super().__init__(options, failover_options_list)


@GenerateDiagramAgent.register_mermaid_class
class GenerateQuadrantChartAgent(GenerateDiagramAgent):
    """
    Mermaid格式的象限图
    示例：https://mermaid.js.org/syntax/quadrantChart.html
    """

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_mermaid_quadrant_chart_prompt.jinja"
        super().__init__(options, failover_options_list)

    def custom_parser(self):
        bad_xml_repair = BadQuantumChartXMLRepair()
        xml_parser = XMLOutputParser()
        return bad_xml_repair | xml_parser


@GenerateDiagramAgent.register_plantuml_class
class GenerateMindMapDiagramAgent(GenerateDiagramAgent):
    """
    plantUML格式的思维导图
    实测大模型生成mermaid格式的思维导图时会存在很严重的缩进问题，因此这里改为使用plantUML的思维导图
    示例：https://plantuml.com/zh/mindmap-diagram
    """

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_plantUML_mindmap_diagram_prompt.jinja"
        super().__init__(options, failover_options_list)


@GenerateDiagramAgent.register_mermaid_class
class GenerateTimeLineChartAgent(GenerateDiagramAgent):
    """
    Mermaid格式的时间线图
    示例：https://mermaid.js.org/syntax/timeline.html
    """

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_mermaid_timeline_chart_prompt.jinja"
        super().__init__(options, failover_options_list)


@GenerateDiagramAgent.register_mermaid_class
class GenerateSanKeyDiagramAgent(GenerateDiagramAgent):
    """
    Mermaid格式的SanKey图
    示例：https://mermaid.js.org/syntax/sankey.html
    """

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_mermaid_sankey_daigram_prompt.jinja"
        super().__init__(options, failover_options_list)


@GenerateDiagramAgent.register_mermaid_class
class GeneratePacketDiagram(GenerateDiagramAgent):
    """
    数据包图：数据包图是用于说明网络数据包的结构和内容的可视化表示
    限制：使用范围较小，官网上给了TCP和UDP数据包的两个例子。
    示例：https://mermaid.js.org/syntax/packet.html
    """

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_mermaid_packet_diagram_prompt.jinja"
        super().__init__(options, failover_options_list)


@GenerateDiagramAgent.register_mermaid_class
class GenerateKanBanChartAgent(GenerateDiagramAgent):
    """
    Mermaid格式的看板图
    示例：https://mermaid.js.org/syntax/kanban.html
    """

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_mermaid_kanban_chart_prompt.jinja"
        super().__init__(options, failover_options_list)


@GenerateDiagramAgent.register_plantuml_class
class GenerateArchitectureDiagramAgent(GenerateDiagramAgent):
    """
    plantUML格式的架构图/组件图
    由于mermaid的架构图api不是很完善，所以这里使用plantUML的组件图
    示例：https://plantuml.com/zh/component-diagram
    """

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_plantUML_architecture_diagram_prompt.jinja"
        super().__init__(options, failover_options_list)


@GenerateDiagramAgent.register_plantuml_class
class GenerateUseCaseDiagramAgent(GenerateDiagramAgent):
    """
    PlantUML格式的UseCase图
    示例：https://plantuml.com/zh/use-case-diagram
    """

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_plantUML_usecase_diagram_prompt.jinja"
        super().__init__(options, failover_options_list)


@GenerateDiagramAgent.register_mermaid_class
class GenerateFlowChartAgent(GenerateDiagramAgent):
    """
    Mermaid格式的流程图
    示例：https://mermaid.nodejs.cn/syntax/flowchart.html
    """

    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_mermaid_flowchart_prompt.jinja"
        super().__init__(options, failover_options_list)

    def custom_parser(self):
        bad_xml_repair = BadFlowChartXMLRepair()
        xml_parser = XMLOutputParser()
        return bad_xml_repair | xml_parser


class GenerateTechniqueRoadAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    content: str = Field(..., description="当前内容", min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_technique_roadmap.jinja"


@GenerateDiagramAgent.register_mermaid_class
class GenerateTechniqueRoadAgent(GenerateFlowChartAgent):
    """
    Mermaid格式的技术路线图
    """
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        super().__init__(options, failover_options_list)
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_technique_roadmap.jinja"
        self.input_type = GenerateTechniqueRoadAgentInput


GenerateDiagramAgent.finalize_mermaid_classes()
GenerateDiagramAgent.finalize_plantuml_classes()
