class ChartCategory:
    chart_type = {
        "表格": ["表格"],
        "数据图": ["折线图", "柱状图", "饼图", "桑基图", "象限图", "散点图", "雷达图"],
        "UML图": ["实体关系图", "用例图", "类图", "时序图", "状态图", "架构图"],
        "流程图": ["流程图"],
        "其他类型的图": ["甘特图", "思维导图", "时间线图", "数据包图", "看板图"]
    }
    _reverse_map = {value: key for key, values in chart_type.items() for value in values}

    @staticmethod
    def find_key(value: str) -> str or None:
        """
        根据 value 查找对应的 key
        :param value: 需要查找的图表类型名称
        :return: 对应的分类 key，若未找到返回 None
        """
        return ChartCategory._reverse_map.get(value)