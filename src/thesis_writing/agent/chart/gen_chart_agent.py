import os
import re
from datetime import datetime
from typing import List
from typing import Optional
from uuid import uuid4

import plotly.graph_objects as go
import pyecharts.options as opts
from langchain_core.exceptions import OutputParserException
from langchain_core.output_parsers import BaseGenerationOutputParser
from langchain_core.outputs import ChatGeneration, Generation
from pydantic import Field, BaseModel
from pyecharts.charts import Line, Pie, Bar, Radar
from pyecharts.globals import ThemeType
from snapshot_selenium import snapshot

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentResponse, BaseAgentInput
from thesis_writing.utils.logger import get_logger

logger = get_logger(__name__)


class MarkdownTableParser(BaseGenerationOutputParser[str]):

    def parse_result(self, result: List[Generation], *, partial: bool = False) -> dict:
        if len(result) != 1:
            raise OutputParserException(
                "This output parser can only be used with a single generation."
            )
        generation = result[0]
        if not isinstance(generation, ChatGeneration):
            raise OutputParserException(
                "This output parser can only be used with a chat generation."
            )

        content  = generation.message.content

        table_pattern = (
            r"^[\r\n]*\s*\|(?:([^\r\n\|]*)\|)+\s*\r?\n\s*\|(?:(\s?\:?-+\:?\s?)\|)+\s*\r?\n(\s*\|(?:([^\r\n\|]*)\|)+\s*\r?\n)*(\s*\|(?:([^\r\n\|]*)\|)+\s*\r?\n?)[\r\n]*$"
        )

        # if not re.match(table_pattern, table_string, re.MULTILINE):
        #     raise OutputParserException("The output is not a valid Markdown table.")
        match = re.search(table_pattern, content , re.MULTILINE)
        if not match:
            raise OutputParserException("The output is not a valid Markdown table.")
        table_string = match.group(0).strip()
        lines = table_string.split('\n')
        header = lines[0]
        separator = lines[1]
        rows = lines[2:]

        def split_cells(line: str) -> List[str]:
            return re.split(r'(?<!\\)\|', line)[1:-1]

        header_cells = split_cells(header)
        separator_cells = split_cells(separator)
        num_columns = len(header_cells)

        if len(separator_cells) != num_columns:
            raise OutputParserException("Header and separator column count mismatch.")

        for row in rows:
            row_cells = split_cells(row)
            if len(row_cells) != num_columns:
                raise OutputParserException("Row and header column count mismatch in a row.")

        if "$$" in table_string:
            raise OutputParserException("$$ for independent formula blocks is prohibited.")

        # 2. 提取 "注：" 开头的内容
        note_string: Optional[str] = None
        # 该正则表达式查找以 "注：" 开头的行（允许行首有空格）
        # re.MULTILINE 使得 ^ 匹配每行的开始
        note_pattern = r"^\s*注：.*"
        note_match = re.search(note_pattern, content, re.MULTILINE)
        if note_match:
            note_string = note_match.group(0).strip()  # .group(0) 获取整个匹配的行
        note = note_string if note_string else ""
        return {"table": table_string,
                "note": note}


class GenerateChartAgentResponse(BaseAgentResponse):

    def render_html(self) -> str:
        raise NotImplementedError()

    def render_image(self) -> bytes:
        temp_dir = "tmp"
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
        temp_path = os.path.join(temp_dir, f"{uuid4()}.html")
        try:
            html = self.render_html()
            with open(temp_path, "w") as f:
                f.write(html)
            context = snapshot.make_snapshot(
                temp_path, 'base64'
            )
            return context
        finally:
            if os.path.exists(temp_path):
                os.remove(temp_path)


class GenerateChartAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    complete_writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    segment_title: str = Field(None, description="当前目标小节标题")
    segment_content: str = Field(..., description="当前目标小节内容", min_length=1)
    data: str = Field('无', description="数据")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)
    chart_info: str = Field(None, description="当前图表信息")
    segment_chart_info: Optional[str] = Field(None, description="当前目标小节其他图表信息")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_chart.jinja"


class YAxisData(BaseModel):
    label: str = Field(..., description="系列标签")
    data: List[float] = Field(..., description="数据")


class GenerateLineChartAgentResponse(GenerateChartAgentResponse):
    x_axis_label: str = Field(..., description="X轴标签")
    y_axis_label: str = Field(..., description="Y轴标签")
    x_axis_data: List[str] = Field(..., description="X轴数据")
    y_axis_data: List[YAxisData] = Field(..., description="Y轴数据")

    def render_html(self) -> str:
        c = (
            Line(init_opts=opts.InitOpts(theme=ThemeType.LIGHT))
            .add_xaxis(self.x_axis_data)
            .set_global_opts(
                xaxis_opts=opts.AxisOpts(name=self.x_axis_label),
                yaxis_opts=opts.AxisOpts(name=self.y_axis_label),
                title_opts=opts.TitleOpts(is_show=False),
            )
        )
        for y_data in self.y_axis_data:
            c = c.add_yaxis(y_data.label, y_data.data)
        return c.render_embed()


class GenerateBarChartAgentResponse(GenerateChartAgentResponse):
    x_axis_label: str = Field(..., description="X轴标签")
    y_axis_label: str = Field(..., description="Y轴标签")
    x_axis_data: List[str] = Field(..., description="X轴数据")
    y_axis_data: List[YAxisData] = Field(..., description="Y轴数据")

    def render_html(self) -> str:
        c = (
            Bar(init_opts=opts.InitOpts(theme=ThemeType.LIGHT))
            .add_xaxis(self.x_axis_data)
            .set_global_opts(
                xaxis_opts=opts.AxisOpts(name=self.x_axis_label),
                yaxis_opts=opts.AxisOpts(name=self.y_axis_label),
                title_opts=opts.TitleOpts(is_show=False),
            )
        )
        for y_data in self.y_axis_data:
            c = c.add_yaxis(y_data.label, y_data.data)
        return c.render_embed()


class PieChartData(BaseModel):
    label: str = Field(..., description="标签")
    value: float = Field(..., description="数据")


class GeneratePieChartAgentResponse(GenerateChartAgentResponse):
    data: List[PieChartData] = Field(..., description="数据")

    def render_html(self) -> str:
        data_pair = [(d.label, d.value) for d in self.data]
        c = (
            Pie(init_opts=opts.InitOpts(theme=ThemeType.LIGHT))
            .add("", data_pair)
            .set_global_opts(title_opts=opts.TitleOpts(is_show=False))
            .set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {c} ({d}%)"))
        )
        return c.render_embed()


class GenerateTableAgentResponse(BaseAgentResponse):
    content: str = Field(..., description="表格内容")
    note: Optional[str] = Field(None, description="表格注释")


class GenerateScatterChartAgentResponse(GenerateChartAgentResponse):
    x_axis_label: str = Field(..., description="X轴标签")
    y_axis_label: str = Field(..., description="Y轴标签")
    x_axis_data: List = Field(..., description="X轴数据")
    y_axis_data: List = Field(..., description="Y轴数据")
    value: List[YAxisData] = Field(None, description="数据")

    def render_html(self, full_html=False, include_plotlyjs=None) -> str:
        """
        Note： 这里使用plotly而不是pyechart的原因是，pyechart的散点图只支持展示x和y之间的关系，无法用颜色深浅和点的大小表示第三第四个变量与x、y之间的关系
        """
        data = go.Scatter(
            x=self.x_axis_data,
            y=self.y_axis_data,
            mode='markers',
        )

        marker = dict()
        text = [f'{self.x_axis_label}: {x}<br>{self.y_axis_label}: {y}<br>' for x, y in
                zip(self.x_axis_data, self.y_axis_data)]
        if self.value:
            marker['color'] = self.value[0].data  # 利用颜色值显示第三个变量与x/y的关系
            color_label = self.value[0].label
            marker['colorbar'] = dict(title=color_label)
            marker['colorscale'] = 'Viridis'
            marker['showscale'] = True
            text = [f'{self.x_axis_label}: {x}<br>{self.y_axis_label}: {y}<br>{color_label}: {v1}<br>' for x, y, v1 in
                    zip(self.x_axis_data, self.y_axis_data, self.value[0].data)]

            if len(self.value) == 2:
                marker['size'] = self.value[1].data  # 利用点的大小展示第四个变量与x/y的关系
                size_label = self.value[1].label
                text = [f'{self.x_axis_label}: {x}<br>{self.y_axis_label}: {y}<br>{color_label}: {v1}<br> \
                            {size_label}: {v2}' for x, y, v1, v2 in
                        zip(self.x_axis_data, self.y_axis_data, self.value[0].data, self.value[1].data)]
            data['marker'] = marker
        data['text'] = text
        data['hoverinfo'] = 'text'

        fig = go.Figure(
            data=data,
        )
        fig.update_layout(
            xaxis_title=self.x_axis_label,
            yaxis_title=self.y_axis_label
        )
        # 设置 include_plotlyjs='cdn'，这样 Plotly.js 库将从 CDN 加载，而不是嵌入到 HTML 文件中
        # 设置 full_html=False，这样只会生成包含图表的 <div> 标签，而不是完整的 HTML 文档
        # 以上两个设置都是为了减少to_html()方法生成的HTML文件的大小
        return fig.to_html(full_html=full_html, include_plotlyjs=include_plotlyjs)


class GenerateRadarChartAgentResponse(GenerateChartAgentResponse):
    indicators: List[str] = Field(..., description="指标名称")
    datas: List[YAxisData] = Field(..., description="数据")

    def render_html(self) -> str:
        values = []
        series = []
        for data in self.datas:
            values.append(data.data)
            series.append(data.label)
        schema = []

        def find_column_max(matrix):
            return [max(col) for col in zip(*matrix)]

        row_max = find_column_max(values)
        for ind, indicator in enumerate(self.indicators):
            item = opts.RadarIndicatorItem(name=indicator, max_=int(row_max[ind] * 1.2))
            schema.append(item)

        radar = (Radar(init_opts=opts.InitOpts(bg_color="#CCCCCC"))
        .add_schema(
            schema=schema,
            splitarea_opt=opts.SplitAreaOpts(
                is_show=True, areastyle_opts=opts.AreaStyleOpts(opacity=1)
            ),
            textstyle_opts=opts.TextStyleOpts(color="#000000"),
        ))
        for serie, value in zip(series, values):
            radar.add(series_name=serie, data=[value])
        return radar.render_embed()


class GenerateLineChartAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_line_chart.jinja"
        self.input_type = GenerateChartAgentInput
        self.output_type = GenerateLineChartAgentResponse
        super().__init__(options, failover_options_list)


class GenerateBarChartAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_bar_chart.jinja"
        self.input_type = GenerateChartAgentInput
        self.output_type = GenerateBarChartAgentResponse
        super().__init__(options, failover_options_list)


class GeneratePieChartAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_pie_chart.jinja"
        self.input_type = GenerateChartAgentInput
        self.output_type = GeneratePieChartAgentResponse
        super().__init__(options, failover_options_list)


class GenerateTableAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_table.jinja"
        self.input_type = GenerateChartAgentInput
        self.output_type = GenerateTableAgentResponse
        super().__init__(options, failover_options_list)

    def custom_parser(self):
        return MarkdownTableParser()

    def deserialize_parser_output(self, parser_output: dict) -> BaseAgentResponse:
        return GenerateTableAgentResponse(content=parser_output['table'], note=parser_output['note'])


class GenerateScatterChartAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_scatter_chart.jinja"
        self.input_type = GenerateChartAgentInput
        self.output_type = GenerateScatterChartAgentResponse
        super().__init__(options, failover_options_list)


class GenerateRadarChartAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/diagram_system_prompt/gen_radar_chart.jinja"
        self.input_type = GenerateChartAgentInput
        self.output_type = GenerateRadarChartAgentResponse
        super().__init__(options, failover_options_list)
