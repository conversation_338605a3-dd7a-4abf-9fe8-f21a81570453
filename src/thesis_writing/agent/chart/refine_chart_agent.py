from datetime import datetime
from typing import Optional, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgent, BaseAgentInput, BaseAgentResponse


class RefineChartAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    major: str = Field(..., description="专业", min_length=1)
    keywords: Optional[str] = Field(None, description="关键词")
    summary: Optional[str] = Field(None, description="全文概述")
    writing_plan: str = Field(..., description="全文写作计划", min_length=1)
    current_plan_title: str = Field(..., description="当前小节标题")
    current_plan_description: str = Field(None, description="当前小节描述")
    chart_type: str = Field(..., description="图表类型")
    chart_title: Optional[str] = Field(None, description="图表标题")
    chart_description: Optional[str] = Field(None, description="图表描述")
    current_date: str = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"), description="当前日期",
                              min_length=1)

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/refine_chart.jinja"


class RefineChartAgentResponse(BaseAgentResponse):
    analysis: str = Field(..., description="分析过程")
    title: str = Field(..., description="图表标题")
    description: str = Field(..., description="图表描述")
    purpose: str = Field(..., description="图表目的")


class RefineChartAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/refine_chart.jinja"
        self.input_type = RefineChartAgentInput
        self.output_type = RefineChartAgentResponse
        super().__init__(options, failover_options_list)
