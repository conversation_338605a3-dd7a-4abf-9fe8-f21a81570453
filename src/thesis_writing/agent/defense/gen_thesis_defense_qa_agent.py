from typing import List

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgentResponse, BaseAgent


class GenerateThesisDefenseQAAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    thesis_content: str = Field(..., description="论文正文")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_thesis_defense_qa.jinja"


class ThesisDefenseQA(BaseModel):
    question: str = Field(..., description="问题")
    answer: str = Field(..., description="答案")


class GenerateThesisDefenseQAAgentResponse(BaseAgentResponse):
    qa_pairs: list[ThesisDefenseQA] = Field(..., description="答辩问答")


class GenerateThesisDefenseQAAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_thesis_defense_qa.jinja"
        self.input_type = GenerateThesisDefenseQAAgentInput
        self.output_type = GenerateThesisDefenseQAAgentResponse
        super().__init__(options, failover_options_list)
