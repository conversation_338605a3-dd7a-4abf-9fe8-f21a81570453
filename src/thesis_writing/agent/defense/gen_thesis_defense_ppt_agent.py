from typing import Optional, List

from pydantic import Field, BaseModel

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgentResponse, BaseAgent


class GenerateThesisDefensePPTAgentInput(BaseAgentInput):
    subject: str = Field(..., description="标题", min_length=1)
    thesis_content: Optional[str] = Field(..., description="论文正文")
    thesis_defense_ppt: Optional[str] = Field(..., description="已完成的答辩PPT")
    all_outline: Optional[str] = Field(..., description="所有章节的答辩PPT大纲")
    outline: Optional[str] = Field(..., description="当前需完成的答辩PPT大纲")

    def __init__(self, **data):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_thesis_defense_ppt.jinja"

    @staticmethod
    def group_outline(outline_items: List[str]):
        group_size = 3
        num_items = len(outline_items)
        grouped_items = [outline_items[i:i + group_size] for i in range(0, num_items, group_size)]
        result = ['\n'.join(group) for group in grouped_items]
        return result


class ThesisDefensePPTAnalysis(BaseModel):
    chapter_title: str = Field(..., description="章节标题")
    chapter_analysis: str = Field(..., description="章节分析")


class ThesisDefensePPTContentDetail(BaseModel):
    title: Optional[str] = Field(None, description="标题")
    content: Optional[str] = Field(None, description="内容，markdown 格式")
    segments: Optional[List["ThesisDefensePPTContentDetail"]] = Field(None, description="子节点")


class ThesisDefensePPTContent(BaseModel):
    page_number: int = Field(..., description="页码")
    ppt_title: str = Field(..., description="该页面ppt 标题，markdown 格式")
    ppt_type: str = Field(..., description="该页面 ppt 类型")
    ppt_content: Optional[ThesisDefensePPTContentDetail] = Field(None, description="该页面 ppt 内容")
    defense_content: str = Field(..., description="该页面 ppt 对应答辩自述稿")


class GenerateThesisDefensePPTAgentResponse(BaseAgentResponse):
    analysis: List[ThesisDefensePPTAnalysis] = Field(..., description="答辩分析")
    contents: List[ThesisDefensePPTContent] = Field(..., description="答辩内容")


class GenerateThesisDefensePPTAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_thesis_defense_ppt.jinja"
        self.input_type = GenerateThesisDefensePPTAgentInput
        self.output_type = GenerateThesisDefensePPTAgentResponse
        super().__init__(options, failover_options_list)
