from typing import Optional, Any, List

from pydantic import Field

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.base.base_agent import BaseAgentInput, BaseAgentResponse, BaseAgent


class GenerateEnKeywordsAgentInput(BaseAgentInput):
    abstract_en: str = Field(..., description="英文摘要", min_length=1)
    keywords_zh: str = Field(..., description="中文关键词", min_length=1)

    def __init__(self, **data: Any):
        super().__init__(**data)
        self.user_message_template_path = "user_prompt/gen_en_keywords.jinja"


class GenerateEnKeywordsAgentResponse(BaseAgentResponse):
    translation: Optional[str] = Field(..., description="英文关键字")


class GenerateEnKeywordsAgent(BaseAgent):
    def __init__(self, options: AgentOptions, failover_options_list: List[AgentOptions]):
        self.system_message_template_path = "system_prompt/gen_en_keywords.jinja"
        self.input_type = GenerateEnKeywordsAgentInput
        self.output_type = GenerateEnKeywordsAgentResponse
        super().__init__(options, failover_options_list)
