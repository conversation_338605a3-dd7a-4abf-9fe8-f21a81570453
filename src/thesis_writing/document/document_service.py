import os
from typing import Optional

from dotenv import load_dotenv
from langfuse.decorators import observe
from openai import BaseModel
from pydantic import Field

from thesis_writing.utils.http_request import post
from thesis_writing.utils.logger import get_logger

load_dotenv()

logger = get_logger(__name__)


class Document(BaseModel):
    lang: Optional[str] = Field(default=None, description="语言")
    summary: Optional[str] = Field(default=None, description="主要观点")


class DocumentService:
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DocumentService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self._initialized:
            return
        self.document_service_url = os.environ.get('DOCUMENT_SERVICE_URL')

    @observe(name="document")
    def gen_thesis_document(self, url: str) -> Document:
        try:
            response = post(self.document_service_url + "/document/summary", body={
                "url": url
            }, timeout=60)
            if response and 'data' in response and response['data']:
                return Document(**response['data'])
        except Exception as e:
            logger.error(f"Failed to get document summary: {e}")
        return Document()
