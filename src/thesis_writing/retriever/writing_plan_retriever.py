from typing import List

from thesis_writing.retriever.es_client_factory import es_client_factory
from thesis_writing.retriever.index.writing_plan import WritingPlan
from thesis_writing.retriever.retriever import Retriever, RetrieverQuery, QueryType
from thesis_writing.utils.logger import get_logger
from thesis_writing.utils.major_alias_util import MajorAliasUtil

logger = get_logger(__name__)


class WritingPlanRetriever(Retriever):

    def __init__(self, embedding_url: str, rerank_url: str, es_config: dict, index_name: str = "writing_plan",
                 output_type: type = WritingPlan):
        super().__init__(embedding_url, rerank_url, es_config, index_name, output_type)

    async def search_summary(self, major: str, title: str, keywords: str, size: int = 3, threshold: float = 0.0):
        rerank_query = f"{major}\n{title}\n{keywords}"
        queries = [
            [RetrieverQuery(query=major_alias, field="major") for major_alias in
             MajorAliasUtil.get_major_alias(major)] +
            [
                RetrieverQuery(query=title, field="title"),
                RetrieverQuery(query=keywords or "", field="keywords"),
                RetrieverQuery(query=title, field="summary"),
            ],
            [
                RetrieverQuery(query=rerank_query, boost=2, field="summary_embedding", type=QueryType.KNN, k=size * 5,
                               num_candidates=size * 20),
            ]
        ]
        result = await self.rrf_retrieve(queries, size * 5)
        return self.rerank(rerank_query, result, size, threshold,
                           lambda doc: f"{doc.major}\n{doc.title}\n{doc.keywords}\n{doc.summary}")

    async def search_writing_plan(self, major: str, title: str, keywords: str, summary: str, size: int = 3,
                                  threshold: float = 0.0):
        queries = [
            [RetrieverQuery(query=major_alias, field="major") for major_alias in
             MajorAliasUtil.get_major_alias(major)] +
            [
                RetrieverQuery(query=title, field="title"),
                RetrieverQuery(query=keywords or "", field="keywords"),
                RetrieverQuery(query=summary, field="summary"),
            ],
            [
                RetrieverQuery(query=summary, boost=2, field="summary_embedding", type=QueryType.KNN, k=size * 5,
                               num_candidates=size * 20),
            ]
        ]
        rerank_query = f"{major}\n{title}\n{keywords}\n{summary}"
        result = await self.rrf_retrieve(queries, size * 5)
        return self.rerank(rerank_query, result, size, threshold,
                           lambda doc: f"{doc.major}\n{doc.title}\n{doc.keywords}\n{doc.writing_plan}")

    async def search_topic(self, major: str, size: int = 3) -> List[WritingPlan]:
        _es_client = None
        try:
            _es_client = es_client_factory(self.es_config)
            result = await _es_client.options(request_timeout=30).search(index=self.index_name, body={
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"major": major}}
                        ]
                    }
                },
                "_source": {
                    "excludes": ["*embedding"]
                },
                "sort": {
                    "_script": {
                        "type": "number",
                        "script": {
                            "source": "Math.random()"
                        },
                        "order": "asc"
                    }
                },
                "size": size
            })
        except Exception as e:
            logger.error(f"Error during es_search: {e}")
        finally:
            if _es_client:
                await _es_client.close()

        docs = []
        for hit in result["hits"]["hits"]:
            try:
                docs.append(self.output_type.model_validate(hit["_source"]))
            except Exception as e:
                logger.exception({
                    "hit": hit,
                    "exception_class": e.__class__.__name__,
                    "exception_message": str(e),
                })
        return docs
