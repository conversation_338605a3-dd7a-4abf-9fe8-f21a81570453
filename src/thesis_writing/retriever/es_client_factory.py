import logging
from typing import Union, Optional, Dict

from elasticsearch import AsyncElasticsearch
from pydantic import BaseModel, Field, ValidationError

logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)


class ESClientConfig(BaseModel):
    hosts: str = Field(default="localhost:9200")
    username: Optional[str] = Field(default="elastic")
    password: Optional[str] = Field(default="password")


class ESClientFactory:
    _clients = {}

    @classmethod
    def get_client(cls, config: Union[ESClientConfig, Dict[str, Union[str, Dict]]]) -> AsyncElasticsearch:
        try:
            if isinstance(config, dict):
                config = ESClientConfig.model_validate(config)
            elif not isinstance(config, ESClientConfig):
                raise TypeError("config must be a dict or ESClientConfig")

            client = AsyncElasticsearch(
                hosts=config.hosts.split(","),
                basic_auth=(config.username, config.password),
                request_timeout=30
            )
            logger.info(f"Created new ES client with id: {id(client)}")
            return client
        except ValidationError as e:
            logger.error(f"Validation error in ESClientConfig: {e}")
            raise
        except Exception as e:
            logger.error(f"Error creating ES client: {e}")
            raise


def es_client_factory(config: Union[ESClientConfig, Dict[str, Union[str, Dict]]]) -> AsyncElasticsearch:
    return ESClientFactory.get_client(config)
