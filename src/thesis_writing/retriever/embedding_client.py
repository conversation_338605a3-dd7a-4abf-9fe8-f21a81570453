from abc import ABC, abstractmethod
from typing import List

import aiohttp
import numpy as np


def cosine_similarity(vector1, vector2):
    vector1 = np.array(vector1)
    vector2 = np.array(vector2)

    dot_product = np.dot(vector1, vector2)
    magnitude1 = np.linalg.norm(vector1)
    magnitude2 = np.linalg.norm(vector2)

    if magnitude1 == 0 or magnitude2 == 0:
        return 0  # Avoid division by zero

    return dot_product / (magnitude1 * magnitude2)


class BaseEmbeddingClient(ABC):

    @abstractmethod
    def embedding(self, text: str) -> List[float]:
        raise NotImplementedError

    @abstractmethod
    def batch_embedding(self, texts: list[str]) -> List[List[float]]:
        raise NotImplementedError


class EmbeddingClient(BaseEmbeddingClient):

    def __init__(self, base_url: str):
        """
        :param base_url: http(s)://host:port ends without `/`
        """
        self.base_url: str = base_url

    async def batch_embedding(self, texts: List[str]) -> List[List[float]]:
        """
        Convert text_list to List[List[float]]
        :param texts: list of text
        :return: embedding list
        """
        if texts is None or len(texts) == 0:
            return []
        async with aiohttp.ClientSession() as session:
            async with session.post(self.base_url, json={"inputs": texts}) as response:
                return await response.json()

    async def embedding(self, text: str) -> List[float]:
        """
        Convert text to List[float]
        :param text: text
        :return: embedding
        """
        if text is None:
            return []
        embeddings = await self.batch_embedding([text])
        return embeddings[0]
