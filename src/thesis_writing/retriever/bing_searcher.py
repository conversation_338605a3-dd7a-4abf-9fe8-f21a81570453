import os
import uuid
from contextlib import suppress
import random
from queue import Queue
from typing import List
from urllib.parse import quote

import requests
from bs4 import <PERSON>Soup
from playwright.async_api import async_playwright
from thesis_writing.retriever.qwen_searcher import <PERSON>wenSearcher

from thesis_writing.retriever.base_searcher import BaseSearcher
from thesis_writing.retriever.base_searcher import SearchResult
from thesis_writing.utils.logger import get_logger


logger = get_logger(__name__)

class BingSearcher(BaseSearcher):
    def __init__(self):
        super().__init__()
        self._subscription_key = os.environ['BING_SEARCH_V7_SUBSCRIPTION_KEY']
        self._endpoint = os.environ['BING_SEARCH_V7_ENDPOINT'] + "/v7.0/search"
        self.search_engine = "Bing"

    async def search(self, query: str, size: int = 10) -> List[SearchResult]:
        if not query:
            return []

        headers = {"Ocp-Apim-Subscription-Key": self._subscription_key}
        params = {"q": query, "count": size, "mkt": 'zh-CN'}
        session_id = str(uuid.uuid4())
        response = requests.get(self._endpoint, headers=headers, params=params)
        response.raise_for_status()
        data = response.json()
        results = [SearchResult(title=web_page['name'], url=web_page['url'], query=query, session_id=session_id,
                                snippet=web_page['snippet'])
                   for web_page in data['webPages']['value']]
        return results


class BingSearcher2(BaseSearcher):
    _instance = None
    cookie_queue = Queue()

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(BingSearcher2, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        super().__init__()
        self.search_engine = "Bing"

        proxy_url = os.environ.get("HTTP_PROXY1")
        proxy_auth = os.environ.get('HTTP_PROXY1_AUTH')

        if proxy_url and proxy_auth:
            scheme, netloc = proxy_url.split("://")
            self.proxies = {
                "http": f"{scheme}://{proxy_auth}@{netloc}",
                "https": f"{scheme}://{proxy_auth}@{netloc}"
            }
        else:
            self.proxies = None


    async def _generate_cookie(self):
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            try:
                context = await browser.new_context()
                page = await context.new_page()
                try:
                    await page.goto(f"https://cn.bing.com/search?q={random.randint(1, 1000000)}", timeout=3000)
                    with suppress(Exception):
                        await page.wait_for_load_state('load', timeout=2000)
                except Exception as e:
                    logger.error(f"Failed goto bing page: {e}")

                cookie_dict = {cookie['name']: cookie['value'] for cookie in await context.cookies()}
                for _ in range(15):
                    self.cookie_queue.put(cookie_dict)
                return cookie_dict
            except Exception as e:
                logger.error(f"Failed to launch browser: {e}")
            finally:
                await browser.close()


    async def search(self, query: str, size: int = 10) -> List[SearchResult]:
        """
           先不使用代理搜索，如果失败再使用代理, 如果代理也失败，再使用API调用
        """
        try:
            results = await self._fetch_result(query, size)
            if results:
                logger.info(f"Bing search success for query: {query}")
                return results
        except Exception as e:
            logger.error(f"Bing search failed for query: {query}, error: {e}")

        if self.proxies:
            try:
                results = await self._fetch_result(query, size, proxies=self.proxies)
                if results:
                    logger.info(f"Bing search success by proxy for query: {query}")
                    return results
            except Exception as e:
                logger.error(f"Bing search failed by proxy for query: {query}, error: {e}")

        logger.warning(f"Bing search failed, using API for query: {query}")
        return await QwenSearcher().search(query, size)

    async def _fetch_result(self, query, size, proxies=None):
        if self.cookie_queue.empty():
            cookie = await self._generate_cookie()
        else:
            cookie = self.cookie_queue.get_nowait()

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Encoding": "gzip, deflate",
            "ect": "4g",
            "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-full-version": "\"135.0.3179.54\"",
            "sec-ch-ua-arch": "\"x86\"",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-ch-ua-platform-version": "\"15.0.0\"",
            "sec-ch-ua-model": "\"\"",
            "sec-ch-ua-bitness": "\"64\"",
            "sec-ch-ua-full-version-list": "\"Microsoft Edge\";v=\"135.0.3179.54\", \"Not-A.Brand\";v=\"*******\", \"Chromium\";v=\"135.0.7049.42\"",
            "upgrade-insecure-requests": "1",
            "x-edge-shopping-flag": "0",
            "preferanonymous": "1",
            "sec-fetch-site": "none",
            "sec-fetch-mode": "navigate",
            "sec-fetch-user": "?1",
            "sec-fetch-dest": "document",
            "accept-language": "zh-CN,zh;q=0.9",
            "priority": "u=0, i",
            "Referer": "https://cn.bing.com/"
        }
        response = requests.get(f"https://cn.bing.com/search?q={quote(query)}", headers=headers, timeout=3,
                                proxies=proxies, cookies=cookie)
        return self._bs4_extractor(response, query, size)

    def _bs4_extractor(self, response, query: str, size: int) -> List[SearchResult]:
        response.raise_for_status()
        session_id = str(uuid.uuid4())
        soup = BeautifulSoup(response.text, "lxml")
        li_elements = soup.select('#b_results > li')
        results = []
        for li_element in li_elements:
            a_element = li_element.select_one('h2 > a')
            if not a_element:
                continue
            snippet_element = li_element.select_one('.b_caption')
            results.append(SearchResult(title=a_element.text, url=a_element.attrs['href'],
                                        snippet=snippet_element.text if snippet_element else '',
                                        query=query, session_id=session_id))
        if not results:
            logger.error(f"No results found in Bing search for text: {soup.text}")
        return results[:size]
