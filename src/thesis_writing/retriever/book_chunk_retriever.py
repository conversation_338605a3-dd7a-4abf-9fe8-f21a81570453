from thesis_writing.retriever.index.book_chunk import BookChunk
from thesis_writing.retriever.retriever import Retriever, RetrieverQuery, QueryType


class BookChunkRetriever(Retriever):

    def __init__(self, embedding_url: str, rerank_url: str, es_config: dict, index_name: str = "book_chunk",
                 output_type: type = BookChunk):
        super().__init__(embedding_url, rerank_url, es_config, index_name, output_type)

    async def search_book_chunk(self, major: str, title: str, keywords: str,
                                query: str, size: int = 3, threshold: float = 0):
        queries = [
            [
                RetrieverQuery(query=query, boost=2, field="content", type=QueryType.MATCH),
                RetrieverQuery(query=keywords, field="keywords", type=QueryType.MATCH),
                RetrieverQuery(query=query, field="summary", type=QueryType.MATCH),
            ],
            [
                RetrieverQuery(query=major, field="embedding", type=QueryType.KNN, k=size * 5,
                               num_candidates=size * 20),
                RetrieverQuery(query=query, boost=3, field="embedding", type=QueryType.KNN, k=size * 5,
                               num_candidates=size * 20),
                RetrieverQuery(query=query, boost=3, field="summary_embedding", type=QueryType.KNN, k=size * 5,
                               num_candidates=size * 20),
            ]
        ]
        result = await self.rrf_retrieve(queries, size * 5)
        rerank_query = f"{title}\n{keywords}\n{query}"
        return self.rerank(rerank_query, result, size, threshold, lambda doc: doc.content)
