import asyncio
import os
from concurrent.futures import as_completed
from typing import List, Tuple

from langfuse.decorators import observe

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.retrieval_analysis.purge_retrieval_chunk_agent import PurgeRetrievalChunkAgentInput, PurgeRetrievalChunkAgent
from thesis_writing.retriever.index.base import RerankDocument, BaseChunk
from thesis_writing.retriever.index.web_chunk import WebChunk
from thesis_writing.retriever.retriever import Retriever, RetrieverQuery, QueryType
from thesis_writing.utils.context_preserve_thread_pool import ContextPreserveThreadPoolExecutor
from thesis_writing.utils.logger import get_logger

logger = get_logger(__name__)


class WebChunkRetriever(Retriever):
    def __init__(self, embedding_url: str, rerank_url: str,
                 purge_web_agent_options: AgentOptions, purge_failover_agent_options: List[AgentOptions],
                 es_config: dict, index_name: str = "web_chunk", output_type: type = WebChunk):
        super().__init__(embedding_url, rerank_url, es_config, index_name, output_type)
        self.agent = PurgeRetrievalChunkAgent(purge_web_agent_options, purge_failover_agent_options)

    async def search_web_chunk(self, query: str, session_id: str, size: int = 10, threshold: float = 0.2,
                               purge_result: bool = True):
        queries = [
            [
                RetrieverQuery(query=session_id, field="session_id", type=QueryType.MUST_TERM),
                RetrieverQuery(query=query, field="content", type=QueryType.MATCH),
            ],
            [
                RetrieverQuery(query=session_id, field="session_id", type=QueryType.MUST_TERM),
                RetrieverQuery(query=query, boost=5, field="embedding", type=QueryType.KNN, k=size * 5,
                               num_candidates=size * 20)
            ],
        ]
        result = await self.rrf_retrieve(queries, size * 5)
        rerank_res = self.rerank(query, result, size, threshold, lambda doc: doc.content)

        if not rerank_res:
            return []

        if not purge_result:
            return rerank_res

        return await super().purge_retrieval_chunk(self.agent, query, rerank_res)

