import asyncio
import datetime
import json
import os
from enum import Str<PERSON>num
from typing import List, <PERSON>ple

from dotenv import load_dotenv
from langfuse.decorators import observe

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.entity.enums import SearchEngine
from thesis_writing.retriever.bing_searcher import BingSearcher2
from thesis_writing.retriever.book_chunk_retriever import BookChunkRetriever
from thesis_writing.retriever.embedding_client import EmbeddingClient, cosine_similarity
from thesis_writing.retriever.index.base import BaseDocument
from thesis_writing.retriever.index.book_chunk import BookChunk
from thesis_writing.retriever.index.reference import Reference
from thesis_writing.retriever.index.standard_chunk import StandardChunk
from thesis_writing.retriever.index.thesis_chunk import ThesisChunk
from thesis_writing.retriever.index.user_feed_chunk import UserFeedChunk
from thesis_writing.retriever.index.web_chunk import WebChunk
from thesis_writing.retriever.index.writing_plan import WritingPlan
from thesis_writing.retriever.qwen_searcher import QwenSearcher
from thesis_writing.retriever.reference_retriever import ReferenceRetriever
from thesis_writing.retriever.standard_retriever import StandardRetriever
from thesis_writing.retriever.thesis_chunk_retriever import ThesisChunkRetriever
from thesis_writing.retriever.user_feed_retriever import UserFeedRetriever
from thesis_writing.retriever.web_retriever import WebChunkRetriever
from thesis_writing.retriever.writing_plan_retriever import WritingPlanRetriever
from thesis_writing.utils.logger import get_logger

load_dotenv()

logger = get_logger(__name__)


class RetrievalSourceType(StrEnum):
    Book = "参考书籍"
    Web = "搜索引擎"
    Paper = "参考论文"


class RetrieveService:
    _instance = None
    _initialized = False

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(RetrieveService, cls).__new__(cls)
        return cls._instance

    def __init__(self, purge_agent_options=None, purge_failover_agent_options=None):
        if self._initialized:
            return

        self.web_chunk_retrieve_url = os.environ.get('WEB_CHUNK_RETRIEVE_URL')
        embedding_url = os.environ["EMBEDDING_URL"]
        rerank_url = os.environ["RERANK_URL"]

        if not purge_agent_options:
            purge_agent_options = AgentOptions(
                base_url=os.environ["QINIU_LLM_BASE_URL"],
                api_key=os.environ["QINIU_LLM_API_KEY"],
                model="qwen3-235b-a22b-instruct-2507",
                retries=1,
                temperature=0.4,
            )
        if not purge_failover_agent_options:
            purge_failover_agent_options = AgentOptions(
                base_url=os.environ["ALIYUN_MODEL_BASE_URL"],
                api_key=os.environ["ALIYUN_MODEL_API_KEY"],
                model="qwen-plus",
                temperature=0.4,
            )

        es_config = {
            "hosts": os.environ["ELASTICSEARCH_HOSTS"],
            "username": os.environ["ELASTICSEARCH_USERNAME"],
            "password": os.environ["ELASTICSEARCH_PASSWORD"]
        }
        self.writing_plan_retriever = WritingPlanRetriever(embedding_url, rerank_url, es_config,
                                                           index_name="writing_plan")
        self.book_chunk_retriever = BookChunkRetriever(embedding_url, rerank_url, es_config,
                                                       index_name="book_with_query")
        self.web_chunk_retriever = WebChunkRetriever(embedding_url, rerank_url,
                                                     purge_agent_options, [purge_failover_agent_options],
                                                     es_config, index_name="web_chunk")
        self.thesis_chunk_retriever = ThesisChunkRetriever(embedding_url, rerank_url, es_config, index_name="thesis")
        self.user_feed_retriever = UserFeedRetriever(embedding_url, rerank_url,
                                                     purge_agent_options, [purge_failover_agent_options],
                                                     es_config, index_name="user_feed_chunk")

        self.standard_retriever = StandardRetriever(embedding_url, rerank_url, es_config, index_name="standard_chunk")
        self.reference_retriever = ReferenceRetriever(embedding_url, rerank_url, es_config, index_name="reference")

        self.embedding_client = EmbeddingClient(embedding_url)

    @observe(name="retriever summary")
    async def search_thesis_summary(self, major: str, title: str, keywords: str, size: int = 3,
                                    threshold: float = 0.0) -> List[tuple[float, WritingPlan]]:
        return await self.writing_plan_retriever.search_summary(major, title, keywords, size, threshold)

    @observe(name="retriever topic")
    async def search_thesis_topic(self, major: str, size: int = 3) -> List[WritingPlan]:
        return await self.writing_plan_retriever.search_topic(major, size)

    @observe(name="retriever writing plan")
    async def search_thesis_writing_plan(self, major: str, title: str, keywords: str, summary: str, size: int = 3,
                                         threshold: float = 0.0) -> List[tuple[float, WritingPlan]]:
        try:
            return await self.writing_plan_retriever.search_writing_plan(major, title, keywords, summary, size,
                                                                         threshold)
        except Exception as ex:
            logger.exception({
                "title": f"Search writing plan error. major: {major} title: {title} keywords: {keywords}",
                "exception_class": ex.__class__.__name__,
                "exception_message": str(ex),
            })
            return []

    @observe(name="retriever book chunk")
    async def search_book_chunk(self, major: str, title: str, keywords: str, query: str, size: int = 3,
                                threshold: float = 0) -> List[tuple[float, BookChunk]]:
        try:
            return await self.book_chunk_retriever.search_book_chunk(major, title, keywords, query, size, threshold)
        except Exception as ex:
            logger.exception({
                "title": f"Search book chunk error. major: {major} title: {title} query: {query} keywords: {keywords} ",
                "exception_class": ex.__class__.__name__,
                "exception_message": str(ex),
            })
            return []

    @observe(name="retriever thesis chunk")
    async def search_thesis_chunk(self, major: str, title: str, keywords: str, query: str, size: int = 3,
                                  threshold: float = 0) -> List[tuple[float, ThesisChunk]]:
        try:
            return await self.thesis_chunk_retriever.search_thesis_chunk(major, title, keywords, query, size, threshold)
        except Exception as ex:
            logger.exception({
                "title": f"Search thesis chunk error. major: {major} title: {title} query: {query} keywords: {keywords}",
                "exception_class": ex.__class__.__name__,
                "exception_message": str(ex),
            })
            return []

    @observe(name="retriever thesis reference")
    async def search_thesis_reference(self, query: str, subject: str = '', language: str = '', size: int = 3,
                                      threshold: float = 0) -> List[tuple[float, Reference]]:
        try:
            return await self.reference_retriever.search_reference(query, subject, language, size, threshold)
        except Exception as ex:
            logger.exception({
                "title": f"Search thesis reference error. query: {query}",
                "exception_class": ex.__class__.__name__,
                "exception_message": str(ex),
            })
            return []

    @observe(name="retriever standard reference")
    async def search_standard_reference(self, query: str, size: int = 3,
                                        threshold: float = 0) -> List[tuple[float, StandardChunk]]:
        try:
            return await self.standard_retriever.search_reference(query, size, threshold)
        except Exception as ex:
            logger.exception({
                "title": f"Search standard reference error. query: {query}",
                "exception_class": ex.__class__.__name__,
                "exception_message": str(ex),
            })
            return []

    @observe(name="retriever user_feed chunk")
    async def search_user_feed_chunk(self, job_id: int, query: str, size: int = 3,
                                     threshold: float = 0.6, purge_result: bool = False) -> List[
        tuple[float, UserFeedChunk]]:
        if job_id is None:
            return []

        try:
            return await self.user_feed_retriever.search_chunks(job_id, query, size, threshold, purge_result)
        except Exception as ex:
            logger.exception({
                "title": f"Search user_feed chunk error. job_id: {job_id} query: {query} ",
                "exception_class": ex.__class__.__name__,
                "exception_message": str(ex),
            })
            return []

    @observe(name="retriever web chunk")
    async def search_web_chunk(self, major: str, title: str, query: str, size: int = 3, threshold: float = 0.6,
                                purge_result: bool = True) -> List[tuple[float, WebChunk]]:

        return await self.search_web_chunk_local(query, size * 2, size, threshold=threshold, purge_result=purge_result)

    @observe(name="local retriever web chunk")
    async def search_web_chunk_local(self, query: str, web_search_count: int = 10, size: int = 10,
                                     search_engine: str = 'Bing', threshold: float = 0.2, purge_result: bool = True) \
            -> List[tuple[float, WebChunk]]:
        if search_engine == 'Bing':
            web_engine = BingSearcher2()
        else:
            raise ValueError(f"Unknown search engine: {search_engine}")
        try:
            results = await self.web_engine_search(web_engine, query, web_search_count, size, threshold,
                                                   purge_result)
            if results is None or len(results) == 0:
                results = await QwenSearcher().search(query, size)
                chunks = [WebChunk(
                    title=r.title,
                    content=r.snippet or "",
                    url=r.url,
                    query=r.query,
                    search_engine=SearchEngine.Qwen,
                    date=datetime.datetime.now().strftime("%Y-%m-%d")
                ) for r in results if r.snippet]
                return [(1.0, chunk) for chunk in chunks]
            return results

        except Exception as ex:
            logger.exception({
                "title": f"Search web chunk error. query: {query} ",
                "exception_class": ex.__class__.__name__,
                "exception_message": str(ex),
            })
            return []

    async def web_engine_search(self, web_engine, query, web_search_count, size, threshold,
                                purge_result):
        results = await web_engine.search(query, web_search_count)
        if results is None or len(results) == 0:
            return []
        chunks: list[WebChunk] = await web_engine.parse(results)
        if not chunks:
            return []

        await self.web_chunk_retriever.persist_chunks(chunks)
        return await self.web_chunk_retriever.search_web_chunk(query, chunks[0].session_id, size, threshold,
                                                               purge_result)

    @observe(name="QuickWebSearch")
    async def search_web_quick(self, query: str, size: int = 5, search_engine: str = 'Bing', threshold: float = 0.2) \
            -> List[str]:
        if search_engine == 'Bing':
            web_engine = BingSearcher2()
        else:
            raise ValueError(f"Unknown search engine: {search_engine}")
        try:
            search_results = await web_engine.search(query, size)
            if not search_results:
                search_results = await QwenSearcher().search(query, size)
            query_emb = await self.embedding_client.embedding(query)
            texts = []
            for each in search_results:
                text = f"{each.title}\n{each.snippet}"
                texts.append(text)
            emb_list = await self.embedding_client.batch_embedding(texts)
            result = []
            for i, each in enumerate(search_results):
                score = cosine_similarity(query_emb, emb_list[i])
                if score > threshold:
                    result.append(texts[i])
            return result
        except Exception as ex:
            logger.exception({
                "title": f"Search web chunk error. query: {query} ",
                "exception_class": ex.__class__.__name__,
                "exception_message": str(ex),
            })
            return []

    async def retrieve_chunks(self, source_type: RetrievalSourceType, queries: List[str],
                              major, title, keywords: str, job_id: int = None) -> List[tuple[str, List]]:
        if not queries:
            return []

        async def process_query(query) -> tuple[str, List]:
            # 根据不同来源调用对应的检索方法
            if source_type == RetrievalSourceType.Web:
                chunks = await self.search_web_chunk(
                    major, title, query, 3, 0.6)
            elif source_type == RetrievalSourceType.Book:
                chunks = await self.search_book_chunk(
                    major, title, keywords, query, 3, 0.6)
            else:  # Paper
                chunks = await self.search_thesis_chunk(
                    major, title, keywords, query, 3, 0.6)

            chunks_only = [chunk for _, chunk in chunks]
            return query, chunks_only

        results = await asyncio.gather(*[process_query(query) for query in queries])
        return results

    async def retrieve_web_chunks(self, queries: List[str], major: str, title: str, purge_result: bool = True) \
            -> List[tuple[str, List]]:
        async def process_query(query) -> tuple[str, List]:
            chunks = await self.search_web_chunk(major, title, query, 3, 0.6, purge_result)
            if not chunks:
                return query, []
            chunks_only = [chunk for _, chunk in chunks]
            return query, chunks_only

        results = await asyncio.gather(*[process_query(query) for query in queries])
        return results

    async def retrieve_book_chunks(self, queries: List[str], major: str, title: str, keywords: str) -> List[
        tuple[str, List]]:
        async def process_query(query) -> tuple[str, List]:
            chunks = await self.search_book_chunk(major, title, keywords, query, 3, 0.6)
            chunks_only = [chunk for _, chunk in chunks]
            return query, chunks_only

        results = await asyncio.gather(*[process_query(query) for query in queries])
        return results

    async def retrieve_user_feed_chunks(self, queries: List[str], job_id: int,
                                        purge_result: bool = False) -> List[tuple[str, List]]:
        if not queries:
            return []

        async def process_query(query) -> tuple[str, List]:
            chunks = await self.search_user_feed_chunk(job_id, query, 3, 0.6, purge_result)
            chunks_only = [chunk for _, chunk in chunks]
            return query, chunks_only

        results = await asyncio.gather(*[process_query(query) for query in queries or []])
        return results

    async def retrieve_thesis_chunks(self, queries: List[str], major: str, title: str, keywords: str) -> List[
        tuple[str, List]]:
        async def process_query(query) -> tuple[str, List]:
            chunks = await self.search_thesis_chunk(major, title, keywords, query, 3, 0.6)
            chunks_only = [chunk for _, chunk in chunks]
            return query, chunks_only

        results = await asyncio.gather(*[process_query(query) for query in queries])
        return results

    @staticmethod
    def dump_json_str_list(results: List[Tuple[str, List[BaseDocument]]]) -> str:
        dict_arr = []
        for q, chunk_list in results:
            dict_arr.append({"query": q, "chunks": [item.model_dump() for item in chunk_list]})
        dump_str = json.dumps(dict_arr, ensure_ascii=False)
        return dump_str

    @staticmethod
    def dump_json_str_single(result: tuple[str, BaseDocument]) -> str:
        dict_arr = [{"query": result[0], "chunks": result[1].model_dump()}]
        dump_str = json.dumps(dict_arr, ensure_ascii=False)
        return dump_str
