from typing import List, Optional

from pydantic import Field

from thesis_writing.retriever.index.base import BaseChunk, uuid4


class ThesisChunk(BaseChunk):
    thesis_id: str = Field(default_factory=uuid4, title="论文ID", json_schema_extra={"es_mapping": {"type": "keyword"}})

    subject: str = Field(title="学科", examples="计算机科学与技术",
                         json_schema_extra={"es_mapping": {"type": "keyword"}})
    title: str = Field(title="论文标题", examples="计算机技术发展态势分析",
                       json_schema_extra={"es_mapping": {"type": "text"}})
    title_embedding: Optional[List[float]] = Field(default=None, title="标题向量", max_length=1024, min_length=1024,
                                                   json_schema_extra={
                                                       "es_mapping": {"type": "dense_vector", "dims": 1024}})
    author: str = Field(title="作者", examples="张三", json_schema_extra={"es_mapping": {"type": "keyword"}})
    year: str = Field(title="年份", examples="2021", json_schema_extra={"es_mapping": {"type": "keyword"}})
    college: str = Field(title="学校", examples="哈尔滨工业大学", json_schema_extra={"es_mapping": {"type": "keyword"}})
    thesis_keywords: str = Field(title="论文关键词", examples="计算机技术；发展态势",
                                 json_schema_extra={"es_mapping": {"type": "text"}})
    chunk_hierarchy_path: str = Field(title="chunk在论文中的层级路径",
                                      examples="计算机技术发展态势分析-1 绪论-1.1 研究背景",
                                      json_schema_extra={"es_mapping": {"type": "text"}})
    chunk_hierarchy_path_embedding: Optional[List[float]] = Field(default=None, title="chunk层级路径向量",
                                                                  max_length=1024, min_length=1024,
                                                                  json_schema_extra={
                                                                      "es_mapping": {"type": "dense_vector",
                                                                                     "dims": 1024}})
