from typing import List, Optional

from pydantic import Field

from thesis_writing.retriever.index.base import BaseDocument


class Reference(BaseDocument):
    id: int = Field(..., title="参考文献ID", json_schema_extra={"es_mapping": {"type": "integer"}})
    year: int = Field(title="2021", json_schema_extra={"es_mapping": {"type": "integer"}})
    title: str = Field(title="论文标题", examples="计算机技术发展态势分析",
                       json_schema_extra={"es_mapping": {"type": "text"}})
    keywords: Optional[str] = Field(title="关键词", json_schema_extra={"es_mapping": {"type": "text"}})
    authors: str = Field(title="作者", examples="张三", json_schema_extra={"es_mapping": {"type": "text"}})
    source: str = Field(title="来源", examples="计算机科学与技术", json_schema_extra={"es_mapping": {"type": "text"}})
    citation: str = Field(title="引用", json_schema_extra={"es_mapping": {"type": "text"}})
    subject: Optional[str] = Field(default=None, title="学科", examples="计算机科学",
                                   json_schema_extra={"es_mapping": {"type": "text"}})
    lang: Optional[str] = Field(default=None, title="语言", examples="zh / en",
                                json_schema_extra={"es_mapping": {"type": "keyword"}})
    summary: str = Field(title="主要观点", json_schema_extra={"es_mapping": {"type": "text"}})
    summary_embedding: Optional[List[float]] = Field(default=None, title="向量", max_length=1024, min_length=1024,
                                                     json_schema_extra={
                                                         "es_mapping": {"type": "dense_vector", "dims": 1024}})
