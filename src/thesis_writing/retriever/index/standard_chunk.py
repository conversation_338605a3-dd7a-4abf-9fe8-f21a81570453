from typing import List, Optional

from pydantic import Field

from thesis_writing.retriever.index.base import uuid4, RerankDocument, BaseChunk


class StandardChunk(BaseChunk):
    standard_code: str = Field(title="行业标准代码", examples=["GB55037-2022"], json_schema_extra={"es_mapping": {"type": "keyword"}}),
    standard_name: str = Field(title="行业标准名称", examples=["《建筑防火通用规范》 GB55037-2022"],
                               json_schema_extra={"es_mapping": {"type": "text"}}),

    effective_date: Optional[str] = Field(title="生效时间", examples=["2021-01-01"],
                                          json_schema_extra={
                                              "es_mapping": {"type": "date", "format": "yyyy-MM-dd",
                                                             "ignore_malformed": True}})

    section_title: str = Field(title="章节名称", json_schema_extra={"es_mapping": {"type": "text"}})
