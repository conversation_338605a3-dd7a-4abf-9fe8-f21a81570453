from typing import Optional

from pydantic import Field

from thesis_writing.retriever.index.base import uuid4, BaseChunk


class BookChunk(BaseChunk):
    book_id: str = Field(default_factory=uuid4, title="书ID",
                         json_schema_extra={"es_mapping": {"type": "keyword"}})

    publish_date: str = Field(title="出版日期", examples="2021-01-01",
                              json_schema_extra={
                                  "es_mapping": {"type": "date", "format": "yyyy-MM-dd", "ignore_malformed": True}})
    genre: str = Field(title="类别", examples="科幻小说", json_schema_extra={"es_mapping": {"type": "keyword"}})
    language: str = Field(title="语言", examples="简体中文", json_schema_extra={"es_mapping": {"type": "keyword"}})
    subject: str = Field(title="学科", examples="化学", json_schema_extra={"es_mapping": {"type": "keyword"}})
    book_name: str = Field(title="书名", examples="三体", json_schema_extra={"es_mapping": {"type": "text"}})
    chunk_hierarchy_path: Optional[str] = Field(default=None, title="chunk在论文中的层级路径",
                                                examples="计算机技术发展态势分析-1 序",
                                                json_schema_extra={"es_mapping": {"type": "text"}})
