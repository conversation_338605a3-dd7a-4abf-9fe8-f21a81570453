from typing import Optional

from pydantic import Field

from thesis_writing.retriever.index.base import uuid4, BaseChunk


class BookChunk(BaseChunk):
    book_id: str = Field(default_factory=uuid4, title="书ID",
                         json_schema_extra={"es_mapping": {"type": "keyword"}})

    publish_date: str = Field(title="出版日期", examples="2021-01-01",
                              json_schema_extra={
                                  "es_mapping": {"type": "date", "format": "yyyy-MM-dd", "ignore_malformed": True}})
    genre: str = Field(title="类别", examples="科幻小说", json_schema_extra={"es_mapping": {"type": "keyword"}})
    language: str = Field(title="语言", examples="简体中文", json_schema_extra={"es_mapping": {"type": "keyword"}})
    subject: str = Field(title="学科", examples="化学", json_schema_extra={"es_mapping": {"type": "keyword"}})
    book_name: str = Field(title="书名", examples="三体", json_schema_extra={"es_mapping": {"type": "text"}})
    chunk_hierarchy_path: Optional[str] = Field(default=None, title="chunk在论文中的层级路径",
                                                examples="计算机技术发展态势分析-1 序",
                                                json_schema_extra={"es_mapping": {"type": "text"}})

    def to_dict(self) -> dict:
        """
        返回包含所有属性的字典对象
        包括本身的属性以及从父类继承的属性

        Returns:
            dict: 包含所有属性的字典
        """
        return {
            # 从 RerankDocument 继承的属性
            'temp_id': self.temp_id,
            'chunk_id': self.chunk_id,
            'content': self.content,
            'refine_content': self.refine_content,

            # 从 BaseChunk 继承的属性
            'summary': self.summary,
            'tags': self.tags,

            # BookChunk 自身的属性
            'book_id': self.book_id,
            'publish_date': self.publish_date,
            'genre': self.genre,
            'language': self.language,
            'subject': self.subject,
            'book_name': self.book_name,
            'chunk_hierarchy_path': self.chunk_hierarchy_path
        }
