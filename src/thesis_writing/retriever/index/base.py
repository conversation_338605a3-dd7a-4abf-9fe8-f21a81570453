import uuid
from abc import ABC
from typing import List, Optional

from pydantic import Field, BaseModel


class BaseDocument(BaseModel, ABC):

    @classmethod
    def to_es_mapping(cls, analyzer: str, search_analyzer: str) -> dict:
        es_mapping: dict = {}
        properties: dict = es_mapping.setdefault("mappings", {}).setdefault("properties", {})
        for name, field in cls.model_fields.items():
            if not field.json_schema_extra or "es_mapping" not in field.json_schema_extra:
                continue
            field_mapping = field.json_schema_extra["es_mapping"]
            if field_mapping["type"] == "text" and len(analyzer) > 0 and len(search_analyzer) > 0:
                field_mapping["analyzer"] = analyzer
                field_mapping["search_analyzer"] = search_analyzer
            if field_mapping["type"] == "nested":
                for sub_name, sub_field_mapping in field_mapping["properties"].items():
                    if sub_field_mapping["type"] == "text" and len(analyzer) > 0 and len(search_analyzer) > 0:
                        sub_field_mapping["analyzer"] = analyzer
                        sub_field_mapping["search_analyzer"] = search_analyzer
            properties[name] = field_mapping
        return es_mapping


def uuid4() -> str:
    return str(uuid.uuid4())


class RerankDocument(BaseDocument):
    chunk_id: str = Field(default_factory=uuid4, title="Chunk ID", json_schema_extra={"es_mapping": {"type": "keyword"}})
    temp_id: Optional[str] = Field(default=None, title="临时ID, 用于标记检索结果中的顺序")
    content: str = Field(title="chunk内容", json_schema_extra={"es_mapping": {"type": "text"}})
    refine_content: Optional[str] = Field(default=None, title="经过提炼的内容")

    def to_es_bulk(self) -> dict:
        dt = self.model_dump()
        dt["_id"] = self.chunk_id

        return dt


class BaseChunk(RerankDocument):

    keywords: str = Field(title="chunk的关键词", json_schema_extra={"es_mapping": {"type": "text"}})
    keywords_embedding: Optional[List[float]] = Field(default=None, title="关键词向量", max_length=1024,
                                                      min_length=1024,
                                                      json_schema_extra={
                                                          "es_mapping": {"type": "dense_vector", "dims": 1024}})

    summary: str = Field(title="chunk的摘要", json_schema_extra={"es_mapping": {"type": "text"}})
    summary_embedding: Optional[List[float]] = Field(default=None, title="摘要向量", max_length=1024, min_length=1024,
                                                     json_schema_extra={
                                                         "es_mapping": {"type": "dense_vector", "dims": 1024}})

    embedding: Optional[List[float]] = Field(default=None, title="向量", max_length=1024, min_length=1024,
                                             json_schema_extra={"es_mapping": {"type": "dense_vector", "dims": 1024}})

    tags: List[str] = Field(default_factory=list, title="标签", json_schema_extra={"es_mapping": {"type": "keyword"}})

