from typing import List, Optional

from pydantic import Field

from thesis_writing.retriever.index.base import uuid4, RerankDocument


class WebChunk(RerankDocument):
    session_id: str = Field(default_factory=uuid4, title="Session ID",
                            json_schema_extra={"es_mapping": {"type": "keyword"}})
    embedding: Optional[List[float]] = Field(default=None, title="向量", max_length=1024, min_length=1024,
                                             json_schema_extra={"es_mapping": {"type": "dense_vector", "dims": 1024}})

    query: str = Field(title="查询关键词", examples="计算机技术发展态势分析",
                       json_schema_extra={"es_mapping": {"type": "keyword"}})
    url: str = Field(title="URL", examples="https://www.baidu.com",
                     json_schema_extra={"es_mapping": {"type": "keyword"}})
    title: str = Field(title="标题", examples="百度一下，你就知道", json_schema_extra={"es_mapping": {"type": "text"}})
    search_engine: str = Field(title="搜索引擎", examples="Bing", json_schema_extra={"es_mapping": {"type": "keyword"}})
    date: str = Field(title="搜索日期", examples="2024-01-01",
                      json_schema_extra={"es_mapping": {"type": "date", "format": "yyyy-MM-dd"}})

    def to_dict(self) -> dict:
        """
        返回包含所有属性的字典对象
        包括本身的属性以及从父类继承的属性

        Returns:
            dict: 包含所有属性的字典
        """
        return {
            # 从 RerankDocument 继承的属性
            'temp_id': self.temp_id,
            'chunk_id': self.chunk_id,
            'content': self.content,
            'refine_content': self.refine_content,

            # WebChunk 自身的属性
            'url': self.url,
            'title': self.title,
            'search_engine': self.search_engine,
            'date': self.date
        }
