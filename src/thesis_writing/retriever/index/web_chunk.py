from typing import List, Optional

from pydantic import Field

from thesis_writing.retriever.index.base import uuid4, RerankDocument


class WebChunk(RerankDocument):
    session_id: str = Field(default_factory=uuid4, title="Session ID",
                            json_schema_extra={"es_mapping": {"type": "keyword"}})
    embedding: Optional[List[float]] = Field(default=None, title="向量", max_length=1024, min_length=1024,
                                             json_schema_extra={"es_mapping": {"type": "dense_vector", "dims": 1024}})

    query: str = Field(title="查询关键词", examples="计算机技术发展态势分析",
                       json_schema_extra={"es_mapping": {"type": "keyword"}})
    url: str = Field(title="URL", examples="https://www.baidu.com",
                     json_schema_extra={"es_mapping": {"type": "keyword"}})
    title: str = Field(title="标题", examples="百度一下，你就知道", json_schema_extra={"es_mapping": {"type": "text"}})
    search_engine: str = Field(title="搜索引擎", examples="Bing", json_schema_extra={"es_mapping": {"type": "keyword"}})
    date: str = Field(title="搜索日期", examples="2024-01-01",
                      json_schema_extra={"es_mapping": {"type": "date", "format": "yyyy-MM-dd"}})
