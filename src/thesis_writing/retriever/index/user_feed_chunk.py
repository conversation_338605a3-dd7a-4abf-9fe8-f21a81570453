from typing import List, Optional

from pydantic import Field

from thesis_writing.retriever.index.base import uuid4, BaseDocument, RerankDocument, BaseChunk


class UserFeedChunk(BaseChunk):
    session_id: str = Field(default_factory=uuid4, title="Session ID",
                            json_schema_extra={"es_mapping": {"type": "keyword"}})
    material_id: str = Field(default_factory=uuid4, title="Material ID",
                             json_schema_extra={"es_mapping": {"type": "keyword"}})

    title: str = Field(title="来源", json_schema_extra={"es_mapping": {"type": "text"}})
