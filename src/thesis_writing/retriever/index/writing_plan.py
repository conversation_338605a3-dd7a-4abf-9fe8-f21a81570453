import uuid
from typing import List, Optional

from pydantic import Field

from thesis_writing.retriever.index.base import BaseDocument


def uuid4() -> str:
    return str(uuid.uuid4())


class WritingPlan(BaseDocument):
    """
    论文写作计划
    """
    thesis_id: str = Field(default_factory=uuid4, title="论文ID", json_schema_extra={"es_mapping": {"type": "keyword"}})
    major: str = Field(title="专业", examples="计算机科学与技术", json_schema_extra={"es_mapping": {"type": "keyword"}})
    college: str = Field(title="清华大学", json_schema_extra={"es_mapping": {"type": "keyword"}})
    year: int = Field(title="2021", json_schema_extra={"es_mapping": {"type": "integer"}})

    title: str = Field(title="论文标题", examples="计算机技术发展态势分析",
                       json_schema_extra={"es_mapping": {"type": "text"}})
    title_embedding: Optional[List[float]] = Field(default=None, title="向量", max_length=1024, min_length=1024,
                                                   json_schema_extra={
                                                       "es_mapping": {"type": "dense_vector", "dims": 1024}})

    keywords: str = Field(title="论文关键词", json_schema_extra={"es_mapping": {"type": "text"}})

    summary: str = Field(title="全文概括", json_schema_extra={"es_mapping": {"type": "text"}})
    summary_embedding: Optional[List[float]] = Field(default=None, title="向量", max_length=1024, min_length=1024,
                                                     json_schema_extra={
                                                         "es_mapping": {"type": "dense_vector", "dims": 1024}})

    writing_plan: str = Field(title="写作计划", json_schema_extra={"es_mapping": {"type": "text"}})
    writing_plan_embedding: Optional[List[float]] = Field(default=None, title="向量", max_length=1024, min_length=1024,
                                                          json_schema_extra={
                                                              "es_mapping": {"type": "dense_vector", "dims": 1024}})
