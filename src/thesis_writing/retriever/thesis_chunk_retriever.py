from thesis_writing.retriever.es_client_factory import es_client_factory
from thesis_writing.retriever.index.thesis_chunk import ThesisChunk
from thesis_writing.retriever.retriever import Retriever, RetrieverQuery, QueryType
from thesis_writing.utils.major_util import MajorUtil


def get_index_from_major(major: str) -> str | None:
    major_info = MajorUtil.get_major_by_name(major)
    if major_info is None:
        # todo 根据相似度匹配最可能的专业大类
        return None
    return f"thesis-{major_info.discipline_code}"


class ThesisChunkRetriever(Retriever):

    def __init__(self, embedding_url: str, rerank_url: str, es_config: dict, index_name: str = "thesis",
                 output_type: type = ThesisChunk):
        super().__init__(embedding_url, rerank_url, es_config, index_name, output_type)

    async def search_thesis_chunk(self, major: str, title: str, keywords: str,
                                  query: str, size: int = 3, threshold: float = 0):
        index = get_index_from_major(major)
        _es_client = es_client_factory(self.es_config)
        if index and not await _es_client.indices.exists(index=index):
            index = None
        if _es_client:
            await _es_client.close()
        queries = [
            [
                RetrieverQuery(query=query, boost=2, field="content", type=QueryType.MATCH),
                RetrieverQuery(query=keywords, boost=1, field="keywords", type=QueryType.MATCH)
            ],
            [
                RetrieverQuery(query=query, boost=5, field="embedding", type=QueryType.KNN, k=size * 5,
                               num_candidates=size * 20),
                RetrieverQuery(query=query, boost=5, field="summary_embedding", type=QueryType.KNN, k=size * 5,
                               num_candidates=size * 20),
                RetrieverQuery(query=title, boost=1, field="title_embedding", type=QueryType.KNN, k=size * 5,
                               num_candidates=size * 20),
            ],
        ]
        result = await self.rrf_retrieve(queries, size * 5, index)
        rerank_query = f"{title}\n{keywords}\n{query}"
        return self.rerank(rerank_query, result, size, threshold, lambda doc: doc.content)
