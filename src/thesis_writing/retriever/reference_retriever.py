from thesis_writing.retriever.index.reference import Reference
from thesis_writing.retriever.retriever import Retriever, RetrieverQuery, QueryType


class ReferenceRetriever(Retriever):

    def __init__(self, embedding_url: str, rerank_url: str, es_config: dict, index_name: str = "reference",
                 output_type: type = Reference):
        super().__init__(embedding_url, rerank_url, es_config, index_name, output_type)

    async def search_reference(self, query: str, subject: str, lang: str, size: int = 10, threshold: float = 0.0):
        queries = [
            [
                RetrieverQuery(query=lang, field="lang", type=QueryType.MUST_TERM),
                RetrieverQuery(query=query, boost=3, field="summary"),
                RetrieverQuery(query=subject, field="subject"),
            ],
            [
                RetrieverQuery(query=lang, field="lang", type=QueryType.MUST_TERM),
                RetrieverQuery(query=query, field="summary_embedding", type=QueryType.KNN, k=size * 5,
                               num_candidates=size * 20),
            ]
        ]
        result = await self.rrf_retrieve(queries, size * 5)
        return self.rerank(query, result, size, threshold, lambda doc: doc.summary)
