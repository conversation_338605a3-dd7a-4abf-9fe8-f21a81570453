import datetime
import dotenv
import os
from abc import ABC, abstractmethod
from elasticsearch import helpers
from pydantic import BaseModel, <PERSON>
from typing import List
from typing import Optional

from thesis_writing.parser.web_parser import web_parser_factory
from thesis_writing.retriever.embedding_client import EmbeddingClient
from thesis_writing.retriever.es_client_factory import es_client_factory
from thesis_writing.retriever.index.web_chunk import WebChunk
from thesis_writing.utils.logger import get_logger

dotenv.load_dotenv()
logger = get_logger(__name__)


class SearchResult(BaseModel):
    title: str = Field(..., title="标题")
    url: str = Field(..., title="链接")
    query: str = Field(..., title="查询关键词")
    session_id: Optional[str] = Field(default=None, title="Session ID")
    snippet: Optional[str] = Field(default=None, title="摘要")


def filter_search_results(search_result_list: List[SearchResult]) -> List[SearchResult]:
    # 过滤掉下载链接，如pdf、docx等
    for search_result in search_result_list:
        if search_result.url.endswith(".pdf") or search_result.url.endswith(".docx"):
            search_result_list.remove(search_result)
    return search_result_list


class BaseSearcher(ABC):
    def __init__(self):
        _web_parser_proxy_url = os.environ.get("HTTP_PROXY1")
        _web_parser_proxy_auth = os.environ.get('HTTP_PROXY1_AUTH')
        self.parser = web_parser_factory(_web_parser_proxy_url, _web_parser_proxy_auth)

        self.embedding_url = os.environ['EMBEDDING_URL']
        self.embedding_client = EmbeddingClient(self.embedding_url)
        self.search_engine = "Unknown"

    @abstractmethod
    def search(self, query: str, size: int):
        raise NotImplementedError

    async def parse(self, search_result_list: List[SearchResult]) -> List[WebChunk]:
        try:
            filtered_result_list = filter_search_results(search_result_list)
            urls = [each.url for each in filtered_result_list]
            parse_result_list = await self.parser.batch_invoke(urls)

            chunk_list = self._create_chunks(filtered_result_list, parse_result_list)
            if not chunk_list:
                return []

            texts = [each.content for each in chunk_list]
            embeddings = await self._get_batch_embeddings(texts)
            self._assign_embeddings(chunk_list, embeddings)

            return chunk_list
        except Exception as e:
            logger.error(f"Error parse: {e}")
            return []

    def _create_chunks(self, search_result_list: List[SearchResult], parse_result_list: List[Optional[List[str]]]) \
            -> List[WebChunk]:
        chunk_list = []
        for search_result, result in zip(search_result_list, parse_result_list):
            if result is None:
                continue
            for text in result:
                if text is None:
                    continue
                chunk = WebChunk(
                    session_id=search_result.session_id,
                    content=text,
                    search_engine=self.search_engine,
                    title=search_result.title,
                    query=search_result.query,
                    url=search_result.url,
                    date=datetime.datetime.now().strftime("%Y-%m-%d"),
                )
                chunk_list.append(chunk)
        return chunk_list

    async def _get_batch_embeddings(self, texts: List[str], batch_size: int = 128) -> List:
        embeddings = []
        for i in range(0, len(texts), batch_size):
            embeddings += await self.embedding_client.batch_embedding(texts[i:i + batch_size])
        return embeddings

    def _assign_embeddings(self, chunk_list: List[WebChunk], embeddings: List):
        for chunk, embedding in zip(chunk_list, embeddings):
            chunk.embedding = embedding

