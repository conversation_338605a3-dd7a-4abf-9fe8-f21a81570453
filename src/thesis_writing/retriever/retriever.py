import asyncio
import os
from collections import defaultdict

from enum import Enum

from langfuse.decorators import observe
from pydantic import BaseModel, Field
from typing import List, Type, Union, Callable, Dict

from thesis_writing.agent.retrieval_analysis.purge_retrieval_chunk_agent import PurgeRetrievalChunkAgentInput, \
    PurgeRetrievalChunkAgent
from thesis_writing.retriever.embedding_client import EmbeddingClient
from thesis_writing.retriever.es_client_factory import es_client_factory
from thesis_writing.retriever.index.base import BaseChunk, BaseDocument, RerankDocument
from thesis_writing.retriever.rerank_client import RerankClient
from thesis_writing.utils.logger import get_logger

logger = get_logger(__name__)


class QueryType(str, Enum):
    MATCH = "match"
    KNN = "knn"
    MUST_TERM = "must_term"


class RetrieverQuery(BaseModel):
    query: str = Field(default=None, title="查询文本")
    boost: float = Field(1.0, title="查询文本权重")
    field: str = Field(..., title="查询字段")
    type: QueryType = Field(QueryType.MATCH, title="查询类型")
    k: int = Field(100, title="查询返回数量")
    num_candidates: int = Field(1000, title="候选数量")


class Retriever:
    def __init__(self,
                 embedding_url: str,
                 rerank_url: str,
                 es_config: dict,
                 index_name: str,
                 output_type: Type = BaseChunk):
        self.embedding_url: str = embedding_url
        self.rerank_url: str = rerank_url
        self.es_config: dict = es_config
        self.embedding_client = EmbeddingClient(embedding_url)
        self.rerank_client = RerankClient(rerank_url)
        self.index_name = index_name
        self.output_type = output_type

    def _rrf_score(self, ranked_lists: List[List[dict]], k: int = 60) -> Dict[str, float]:
        """Calculate Reciprocal Rank Fusion (RRF) score for each document across ranked lists."""
        scores = defaultdict(float)
        for ranked_list in ranked_lists:
            for rank, hit in enumerate(ranked_list, start=1):
                doc_id = hit["_id"]  # TODO: Use custom id field
                scores[doc_id] += 1 / (k + rank)
        return scores

    async def _body_func(self, query_list: Union[List[RetrieverQuery], RetrieverQuery], size: int = 20):
        if not isinstance(query_list, list):
            query_list = [query_list]
        query_list = [each for each in query_list if each.query is not None and each.query != ""]
        match_queries = []
        must_queries = []
        knn_queries = []

        for query in query_list:
            if query.type == QueryType.MATCH and query.query:
                match_queries.append({"match": {query.field: {"query": query.query, "boost": query.boost}}})
            elif query.type == QueryType.MUST_TERM:
                must_queries.append({"term": {query.field: query.query}})
            elif query.type == QueryType.KNN and query.query:
                knn_queries.append({
                    "field": query.field,
                    "query_vector": await self.embedding_client.embedding(query.query),
                    "k": query.k,
                    "num_candidates": query.num_candidates,
                    "boost": query.boost,
                })
        req_body = {
            "_source": {
                "excludes": ["*embedding"]
            },
            "size": size,
        }

        if len(match_queries) > 0:
            req_body["query"] = {
                "bool": {
                    "should": match_queries,
                }
            }
            if len(must_queries) > 0:
                req_body["query"]["bool"]["must"] = must_queries

        if knn_queries:
            if len(must_queries) > 0:
                for each in knn_queries:
                    each["filter"] = {
                        "bool": {
                            "must": must_queries
                        }
                    }
            req_body['knn'] = knn_queries

        if not knn_queries and not match_queries and len(must_queries) > 0:
            req_body["query"] = {
                "bool": {
                    "must": must_queries,
                }
            }

        return req_body

    def rerank(self, query: str, docs: List[BaseChunk], top_k: int = 10, threshold: float = None,
               to_rank_string_func: Callable[..., str] = None) -> List[tuple[float, BaseChunk]]:
        if to_rank_string_func is not None and query is not None and len(docs) > 0:
            texts = [to_rank_string_func(doc) for doc in docs]
            rerank_result = self.rerank_client.rerank(query, texts)
            if threshold is not None:
                rerank_result = [each for each in rerank_result if each.score >= threshold]
            results = [(each.score, docs[each.index]) for each in rerank_result]
            # 获取results中分数最高的top_k个
            return results[:top_k]
        else:
            logger.warning("rerank failed")
            return []

    async def rrf_retrieve(self,
                           query_list: List[Union[List[RetrieverQuery], RetrieverQuery]],
                           search_size: int = 20, index_name: str = None) -> List[BaseChunk]:
        ranked_lists = []
        _es_client = None
        index_name = index_name if index_name else self.index_name
        try:
            _es_client = es_client_factory(self.es_config)
            for query in query_list:
                req_body = await self._body_func(query, search_size)
                result = await _es_client.search(index=index_name, body=req_body)
                ranked_lists.append(result["hits"]["hits"])
        except Exception as e:
            logger.error(f"Error during es_search: {e}")
        finally:
            if _es_client:
                await _es_client.close()

        rrf_scores = self._rrf_score(ranked_lists)
        sorted_hits = sorted(rrf_scores.items(), key=lambda x: x[1], reverse=True)
        docs = []
        for doc_id, _ in sorted_hits:
            hit = next(hit for rank_list in ranked_lists for hit in rank_list if hit["_id"] == doc_id)
            try:
                docs.append(self.output_type.model_validate(hit["_source"]))
            except Exception as e:
                logger.exception({
                    "hit": hit,
                    "exception_class": e.__class__.__name__,
                    "exception_message": str(e),
                })

        return docs[:search_size]

    async def retrieve(self,
                       query_list: Union[List[RetrieverQuery], RetrieverQuery],
                       search_size: int = 20,
                       from_param: int = 0) -> List[BaseChunk]:
        result = None
        req_body = await self._body_func(query_list, search_size)
        req_body["from"] = from_param
        
        _es_client = None
        try:
            _es_client = es_client_factory(self.es_config)
            result = await _es_client.search(index=self.index_name, body=req_body)
        except Exception as e:
            logger.error(f"Error during es_search: {e}")
        finally:
            if _es_client:
                await _es_client.close()

        docs: List[BaseChunk] = []
        if not result or not result["hits"]:
            return docs

        hits = result["hits"]["hits"]
        for hit in hits:
            try:
                docs.append(self.output_type.model_validate(hit["_source"]))
            except Exception as e:
                logger.exception({
                    "hit": hit,
                    "exception_class": e.__class__.__name__,
                    "exception_message": str(e),
                })
        return docs

    async def persist_chunks(self, chunk_list: List[BaseDocument]):
        if not chunk_list:
            return

        _es_client = None
        try:
            _es_client = es_client_factory(self.es_config)

            await self._bulk_insert_chunks(_es_client, chunk_list)
            await _es_client.indices.refresh(index=self.index_name)
        except Exception as e:
            logger.error(f"Error persisting chunks: {e}")
        finally:
            if _es_client:
                await _es_client.close()

    async def _bulk_insert_chunks(self, _es_client, chunk_list: List[BaseDocument]):
        insert_batch_size = 128
        for i in range(0, len(chunk_list), insert_batch_size):
            batch_chunk_list = chunk_list[i:i + insert_batch_size]
            
            # 构建批量插入操作
            bulk_operations = []
            for chunk in batch_chunk_list:
                # 使用to_es_bulk方法将文档转换为ES可用的格式
                doc = chunk.to_es_bulk()
                action = {
                    "_op_type": "index",  # 可以是 index/create/update/delete
                    "_index": self.index_name,
                    "_source": {k: v for k, v in doc.items() if k != "_id"}
                }
                if "_id" in doc:
                    action["_id"] = doc["_id"]
                bulk_operations.append(action)
            
            # 执行批量插入
            if bulk_operations:
                await self._process_batch(_es_client, bulk_operations)

    async def bulk_update_chunks(self, updates: List[dict]):

        """
        批量更新chunks的字段值
        
        Args:
            updates: 包含更新信息的列表，每个元素格式为:
                    {
                        "chunk_id": "xxx",
                        "fields": {"field1": "new_value1", "field2": "new_value2"}
                    }
        """
        if not updates:
            return

        _es_client = None
        try:
            _es_client = es_client_factory(self.es_config)
            
            # 构建批量更新操作
            bulk_operations = []
            for update in updates:
                bulk_operations.append({
                    "_op_type": "update",
                    "_index": self.index_name,
                    "_id": update["chunk_id"],
                    "doc": update["fields"]
                })
            
            # 分批处理，每批128个
            batch_size = 128
            for i in range(0, len(bulk_operations), batch_size):
                batch = bulk_operations[i:i + batch_size]
                # async bulk update
                if batch:
                    await self._process_batch(_es_client, batch)

            # 刷新索引以使更改立即可见
            await _es_client.indices.refresh(index=self.index_name)
            
        except Exception as e:
            logger.error(f"Error bulk updating chunks: {e}")
            raise e
        finally:
            if _es_client:
                await _es_client.close()

    async def _process_batch(self, _es_client, batch: List[Dict]):
        """处理单个批次"""
        from elasticsearch.helpers import async_bulk

        try:
            # 执行异步批量操作
            success_count, errors = await async_bulk(
                _es_client,
                actions=batch,
                raise_on_error=False,
                raise_on_exception=False
            )
            return success_count, len(errors) if errors else 0
        except Exception as e:
            print(f"批量插入出错: {str(e)}")
            return 0, len(batch)

    async def create_index_if_need(self, _es_client, document: BaseDocument):
        if not await _es_client.indices.exists(index=self.index_name):
            mappings = document.to_es_mapping(os.environ['ELASTICSEARCH_INDEX_ANALYZER'],
                                              os.environ['ELASTICSEARCH_SEARCH_ANALYZER'])
            _es_client.indices.create(index=self.index_name, body=mappings)
            
    async def delete_by_query(self, query: dict, index_name: str = None) -> int:
        """
        根据查询条件删除文档
        
        Args:
            query: 查询条件
            index_name: 索引名称，如果为None则使用self.index_name
            
        Returns:
            删除的文档数量
        """
        index_name = index_name if index_name else self.index_name
        _es_client = None
        try:
            _es_client = es_client_factory(self.es_config)
            
            # 执行删除操作
            result = await _es_client.delete_by_query(
                index=index_name,
                body=query,
                refresh=True
            )
            
            # 返回删除的文档数量
            deleted_count = result.get("deleted", 0)
            return deleted_count
            
        except Exception as e:
            logger.error(f"删除文档时出错: {e}")
            raise e
        finally:
            if _es_client:
                await _es_client.close()

    @classmethod
    async def purge_retrieval_chunk(cls, agent: PurgeRetrievalChunkAgent, query: str,
                                    rerank_res: List[tuple[float, RerankDocument]]):
        @observe(name="purge retrieval chunk")
        async def purge_retrieval_chunk(the_query: str, chunk: RerankDocument):
            try:
                agent_input = PurgeRetrievalChunkAgentInput(query=the_query, origin_content=chunk.content)
                agent_res = await agent.ainvoke(agent_input)
                if agent_res and agent_res.result:
                    chunk.refine_content = agent_res.result
            except Exception as e:
                logger.exception({
                    "exception_class": e.__class__.__name__,
                    "exception_message": str(e),
                })

        tasks = [purge_retrieval_chunk(query, chunk) for _, chunk in rerank_res]
        await asyncio.gather(*tasks)

        # 剔除掉refine_content为空的文档，意味着模型认为这个文档没有相关内容
        return [(_, doc) for _, doc in rerank_res if doc.refine_content]
