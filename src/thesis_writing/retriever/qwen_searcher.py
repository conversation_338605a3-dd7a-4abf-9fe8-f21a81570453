import os
import time
import uuid
from datetime import datetime, timezone
from typing import List

from langchain_core.messages import HumanMessage

from thesis_writing.agent.base.base_agent import JsonWithRepairParser
from thesis_writing.agent.base.llm_proxy import create_llm_proxy
from thesis_writing.retriever.base_searcher import BaseSearcher, SearchResult
from thesis_writing.utils.logger import get_logger

logger = get_logger(__name__)


class QwenSearcher(BaseSearcher):
    def __init__(self):
        super().__init__()
        self.api_key = os.environ.get('QWEN_SEARCH_API_KEY')
        self.base_url = os.environ.get('QWEN_SEARCH_MODEL_BASE_URL')
        self.model = os.environ.get('QWEN_SEARCH_MODEL_NAME')
        self.search_engine = "Qwen"
        self.max_retries = 3
        self.timeout = 120
        self.json_parser = JsonWithRepairParser()
        self.llm = create_llm_proxy(
            api_key=self.api_key,
            model=self.model,
            base_url=self.base_url,
            max_tokens=8192,
            temperature=0.1,
            timeout=self.timeout,
            enable_thinking=False
        )

        if 'qwen' in self.model.lower():
            self.llm.extra_body = {
                **getattr(self.llm, 'extra_body', {}),
                "enable_search": True
            }

    async def search(self, query: str, size: int = 10) -> List[SearchResult]:
        if not query:
            return []

        # 获取当前时间信息
        current_time = datetime.now(timezone.utc)
        current_date = current_time.strftime("%Y年%m月%d日")

        for attempt in range(self.max_retries):
            try:
                example = """
  [
    {
      "title": "老年痴呆",
      "snippet": "老年痴呆\n一、老年痴呆的症状\n1、生活功能改变。\n发病早期主要表现为记忆力下降，对患者的一般生活功能影响不大，但是从事高智力活动的患者会出现工作能力和效率下降。\n2、精神和行为症状。\n部分症状表现不明显，易被患者本人、家属及接诊医生忽视。\n即使在疾病早期，患者也会出现精神和行为的改变，如患者变得主动性缺乏、活动减少、孤独、自私、对周围环境兴趣减少、对周围人较为冷淡，甚至对亲人也漠不关心，情绪不稳、易激惹。",
    },
    {
      "title": "老年痴呆的症状有哪些?",
      "snippet": "老年痴呆的症状有哪些？\n老年痴呆，也被称为阿尔茨海默病，是一种常见的神经退行性疾病，主要影响老年人的认知功能。\n这种疾病的发展是渐进的，初期症状可能较轻微，但随着病情恶化，患者的生活质量会受到严重影响。\n以下是一些主要的老年痴呆症状，详细描述如下：\n1. 记忆力减退：这是老年痴呆最常见的早期症状。\n患者可能会发现很难记住最近发生的事情，如新的人名、日期或日常活动。\n虽然记忆力减退在老年人中常见，但如果这种现象持续且影响到日常生活，可能是老年痴呆的早期信号。\n2. 语言和沟通困难：患者可能会发现难以找到合适的词语来表达自己的想法，或者理解他人的对话。\n这可能导致交流困难，使患者感到困惑和沮丧。",
    },
    {
      "title": "老年痴呆的症状-",
      "snippet": "它会导致记忆力和认知能力的丧失，严重影响患者的生活质量。\n老年痴呆症状的早期迹象可能很难察觉，但随着病情的恶化，这些症状会逐渐加重。\n老年痴呆的主要症状表现为记忆力减退、思维能力下降以及行为异常。\n记忆力减退是最常见的症状，患者会忘记曾经发生的事情，以及进行简单的日常活动，如拖鞋放哪里、家门锁没锁。\n思维能力下降会导致患者在解决问题、进行推理和判断时出现困难。\n行为异常主要体现为易怒、焦虑、抑郁等情绪变化，有时患者还会有幻觉和妄想的表现。",
    }
  ]
"""

                prompt = f"""当前时间：{current_date}

以下是输出案例：
{example}
请仿照输出案例的结构输出。
请搜索关于'{query}'的信息，返回真实的搜索结果列表，格式为JSON数组，每个元素必须包含title、snippet字段。
请注意：snippet 字段必须对搜索结果进行具体、详细、充分的描述，内容要涵盖关键信息，避免简单、笼统或片面的表述，确保用户通过 snippet 能全面了解该结果的核心内容。如果是时效性信息，请优先返回最新数据。
如果无法搜索到数据，则不需要返回结果，snippet直接返回""即可，最多返回{size}个结果。"""

                message = HumanMessage(content=prompt)
                response = await self.llm.ainvoke([message])

                session_id = str(uuid.uuid4())
                results = self._parse_qwen_response(response.content, query, session_id, size)

                if results:
                    return results
                else:
                    logger.warning(f"Attempt {attempt + 1}: No results parsed for query: {query}")

            except Exception as e:
                logger.error(f"Attempt {attempt + 1}: Unexpected error for query: {query}, error: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)
                    continue
        return []

    def _parse_qwen_response(self, content: str, query: str, session_id: str, size: int) -> List[SearchResult]:
        results = []

        try:
            if content:
                try:
                    search_data = self.json_parser.parse(content)
                    if isinstance(search_data, list):
                        for item in search_data[:size]:
                            if isinstance(item, dict):
                                results.append(SearchResult(
                                    title=item.get('title', ''),
                                    url='',
                                    query=query,
                                    session_id=session_id,
                                    snippet=item.get('snippet', '')
                                ))
                    elif isinstance(search_data, dict):
                        # 如果返回的是单个对象而不是数组，也处理一下
                        results.append(SearchResult(
                            title=search_data.get('title', f"搜索结果: {query}"),
                            url='',
                            query=query,
                            session_id=session_id,
                            snippet=search_data.get('snippet', content)
                        ))
                    else:
                        results.append(SearchResult(
                            title=f"搜索结果: {query}",
                            url="",
                            query=query,
                            session_id=session_id,
                            snippet=content
                        ))
                except Exception as parse_error:
                    logger.warning(f"JSON parsing failed for query '{query}': {parse_error}")
                    results.append(SearchResult(
                        title=f"搜索结果: {query}",
                        url="",
                        query=query,
                        session_id=session_id,
                        snippet=content
                    ))
        except Exception as e:
            logger.error(f"Failed to parse Qwen response: {e}")

        return results
