import asyncio
from typing import <PERSON><PERSON>, List

from langchain_text_splitters import RecursiveCharacterTextSplitter

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.retrieval_analysis.purge_retrieval_chunk_agent import PurgeRetrievalChunkAgent
from thesis_writing.retriever.embedding_client import Embedding<PERSON><PERSON>
from thesis_writing.retriever.index.base import uuid4
from thesis_writing.retriever.index.user_feed_chunk import UserFeedChunk
from thesis_writing.retriever.retriever import Retriever, RetrieverQuery, QueryType


def _to_session_id(job_id):
    return f"job_{job_id}"


class UserFeedRetriever(Retriever):

    def __init__(self, embedding_url: str, rerank_url: str,
                 purge_web_agent_options: AgentOptions, purge_failover_agent_options: List[AgentOptions],
                 es_config: dict, index_name: str):
        super().__init__(embedding_url, rerank_url, es_config, index_name, output_type=UserFeedChunk)
        self.embedding_client = EmbeddingClient(embedding_url)
        self.agent = PurgeRetrievalChunkAgent(purge_web_agent_options, purge_failover_agent_options)

    async def save_user_feed(self, materials: list[str], job_id: int) -> list[str]:
        chunk_size = 1000
        chunk_overlap = 100
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size, chunk_overlap=chunk_overlap
        )

        user_feed_chunks = []
        user_feed_chunk_embeddings = []

        session_id = _to_session_id(job_id)
        material_ids = []
        for material in materials:
            material_id = uuid4()
            material_ids.append(material_id)

            splits = text_splitter.split_text(material)
            for split in splits:
                # Create UserFeedChunk without embedding
                user_feed_chunk = UserFeedChunk(
                    session_id=session_id,
                    material_id=material_id,
                    chunk_id=uuid4(),
                    title="",
                    keywords="",
                    keywords_embedding=None,
                    summary="",
                    summary_embedding=None,
                    content=split,
                    embedding=None
                )
                user_feed_chunks.append(user_feed_chunk)

            # Generate summary embeddings in batches
            content_embeddings = await self.embedding_client.batch_embedding(splits)
            user_feed_chunk_embeddings.extend(content_embeddings)

        # Assign embeddings to the corresponding UserFeedChunk objects
        for user_feed_chunk, embedding in zip(user_feed_chunks, user_feed_chunk_embeddings):
            user_feed_chunk.embedding = embedding

        # Index to Elasticsearch
        await super().persist_chunks(user_feed_chunks)

        return material_ids

    async def update_summary(self, chunks: List[UserFeedChunk]):
        """
        批量更新UserFeedChunk的摘要和关键词信息
        
        Args:
            chunks: 需要更新摘要和关键词的UserFeedChunk列表
        """
        if not chunks:
            return

        # 收集所有需要生成embedding的文本
        summaries = [f"片段来源：{chunk.title} 片段总结：{chunk.summary}" for chunk in chunks if chunk.summary]
        keywords = [f"片段来源：{chunk.title} 片段关键词：{chunk.keywords}" for chunk in chunks if chunk.keywords]

        # 批量生成embeddings
        summary_to_embedding = {}
        keywords_to_embedding = {}

        if summaries:
            summary_embeddings = await self.embedding_client.batch_embedding(summaries)
            summary_to_embedding = dict(zip(summaries, summary_embeddings))

        if keywords:
            keywords_embeddings = await self.embedding_client.batch_embedding(keywords)
            keywords_to_embedding = dict(zip(keywords, keywords_embeddings))

        # 构建批量更新操作的数据结构
        updates = []
        for chunk in chunks:
            fields = {
                "title": chunk.title,
                "summary": chunk.summary,
                "keywords": chunk.keywords,
                "summary_embedding": summary_to_embedding.get(
                    f"片段来源：{chunk.title} 片段总结：{chunk.summary}") if chunk.summary else None,
                "keywords_embedding": keywords_to_embedding.get(
                    f"片段来源：{chunk.title} 片段关键词：{chunk.keywords}") if chunk.keywords else None
            }

            updates.append({
                "chunk_id": chunk.chunk_id,
                "fields": fields
            })

        # 使用父类的bulk_update_chunks方法执行批量更新
        await super().bulk_update_chunks(updates)

    async def get_user_feeds(self, material_ids: list[str]) -> dict[str, list[UserFeedChunk]]:
        page_size = 150
        material_map: dict[str, list[UserFeedChunk]] = {}

        async def fetch_user_feed_chunks(material_id):
            all_chunks = []
            page = 0
            while True:
                # 构建分页查询
                queries = [
                    RetrieverQuery(query=material_id, field="material_id", type=QueryType.MUST_TERM)
                ]

                # 添加分页参数
                from_param = page * page_size
                size_param = page_size

                # 执行查询
                chunks = await self.retrieve(queries, size_param, from_param=from_param)

                # 如果没有更多结果，退出循环
                if not chunks:
                    break

                all_chunks.extend(chunks)
                page += 1

                # 如果返回的结果少于页面大小，说明已经是最后一页
                if len(chunks) < page_size:
                    break

            return all_chunks

        tasks = [fetch_user_feed_chunks(material_id) for material_id in material_ids]
        results = await asyncio.gather(*tasks)

        for material_id, user_feed_chunks in zip(material_ids, results):
            material_map[material_id] = user_feed_chunks

        return material_map

    async def search_chunks(self, job_id: int, query: str, size: int = 3, threshold: float = 0.6,
                            purge_result: bool = False) -> List[tuple[float, UserFeedChunk]]:
        session_id = _to_session_id(job_id)

        queries = [
            [
                RetrieverQuery(query=session_id, field="session_id", type=QueryType.MUST_TERM),
                RetrieverQuery(query=query, field="embedding", type=QueryType.KNN, k=size * 5,
                               num_candidates=size * 20),
                RetrieverQuery(query=query, boost=5, field="summary_embedding", type=QueryType.KNN, k=size * 5,
                               num_candidates=size * 20)
            ],
        ]

        user_feed_chunks = await self.rrf_retrieve(queries, size * 5)

        rerank_query = f"{query}"

        rerank_res = self.rerank(rerank_query, user_feed_chunks, size, threshold, lambda doc: doc.content)
        if not rerank_res:
            return []

        if not purge_result:
            return rerank_res

        return await super().purge_retrieval_chunk(self.agent, query, rerank_res)

    async def delete_chunks_by_job_id(self, job_id: int) -> int:
        """
        根据job_id删除所有相关的chunks
        
        Args:
            job_id: 要删除的job_id
            
        Returns:
            删除的文档数量
        """
        session_id = _to_session_id(job_id)

        # 构建删除查询
        query = {
            "query": {
                "term": {
                    "session_id": session_id
                }
            }
        }

        # 调用父类的delete_by_query方法执行删除操作
        return await super().delete_by_query(query)
