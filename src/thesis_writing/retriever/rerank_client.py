from abc import ABC, abstractmethod
from typing import List

from pydantic import BaseModel, Field

from thesis_writing.utils.http_request import post


class BaseRerankClient(ABC):

    @abstractmethod
    def rerank(self, query: str, texts: List[str]) -> List[float]:
        raise NotImplementedError


class RerankResult(BaseModel):
    score: float = Field(..., description="分数")
    index: int = Field(..., description="在texts中的数组下标")


class RerankClient(BaseRerankClient):
    def __init__(self, base_url: str):
        self.base_url: str = base_url

    def rerank(self, query: str, texts: List[str]) -> List[RerankResult]:
        """
        query: str, 查询文本
        texts: List[str], 待排序文本
        return: List[RerankResult], 排序结果, 每个元素包含score和index, score为分数，index为在texts中的数组下标, 顺序按score从高到低
        """
        if texts is None or len(texts) == 0:
            return []
        url: str = f"{self.base_url}/rerank"
        response = post(url, body={"query": query, "texts": texts})
        return [RerankResult(**result) for result in response]
