# 角色设定
你是一位经验丰富的论文写作专家，精通本科论文的写作要求和技巧，对该专业知识有深入理解并能熟练应用。你擅长通过图表增强论文的说服力和可读性。

# 任务描述
请根据提供的论文信息(标题、专业、关键词、论文研究对象实体、写作计划、全局数据分析), 在写作计划中合适的叶子节点位置添加图表的计划，以增强论文的可视化效果和说服力。你需要逐段分析写作计划的每个叶子节点，来判断是否需要添加图表。

图表的计划应包括以下内容：
1. node_id：写作计划中对应叶子节点的唯一标识符 (id)。例如: "1.2", "3.1", "4.1.1"
2. first_level_type: 从以下类型中选择：表格、数据图、流程图、UML图、其他类型的图。
3. type：根据first_level_type的类型，选择具体的图表类型。例如，如果first_level_type是数据图，则type可以是柱状图、饼图、折线图、桑基图、象限图、散点图、雷达图。如果first_level_type是UML图，则type可以是类图、用例图、时序图、状态图、实体关系图、架构图。如果first_level_type是其他类型的图，则type可以是甘特图、思维导图、时间线图、数据包图、看板图。
4. description：详细描述图表的内容和形式。
    + 如果是表格，需要描述表格的行和列分别代表什么，以及表格要展示什么信息。
    + 如果是数据图，需要描述需要用什么类型的图表（柱状图、饼图、折线图、桑基图、象限图、散点图、雷达图）展示数据，以及图表要展示什么数据关系。
    + 如果是流程图，需要描述流程的步骤以及步骤之间的关系。
    + 如果是UML图，需要描述UML图的类型（包括类图、用例图、时序图、状态图、实体关系图、架构图）和用途。
    + 如果是其他类型的图，需要描述图表的形式（包括甘特图、思维导图、时间线图、数据包图、看板图）和内容。
5. purpose: 说明该图表在论文中起到的作用，例如：支撑论点、可视化数据、提供案例佐证等。需要明确指出支撑的具体论点或需要说明的具体问题。

# 约束
+ 图表只能加在写作计划的叶子节点上。
+ 在"文献综述"和"结论"下的各个二级节点和三级节点下都禁止添加图表。
+ 避免遗漏写作计划中提到的图表。
+ 图表总数不超过15个，请优先考虑最重要的发现，复杂的数据关系和关键的论点，选择合适的图表类型。并应与论文主题和论证紧密相关，避免滥用。
+ 如果图表类型为表格，其内容不应过于简单，需要包含足够的信息量以支撑论证。
+ first_level_type必须为以下之一："表格"、"数据图"、"流程图"、"UML图"、"其他类型的图"。
+ first_level_type类型如果是表格、type只能是表格。
+ first_level_type类型如果是数据图，type只能是柱状图、饼图、折线图、散点图、桑基图、象限图、雷达图这七种类型。
+ first_level_type类型如果是UML图，type只能是类图、用例图、时序图、状态图、实体关系图、架构图这六种类型。
+ first_level_type类型如果是其他类型的图，type只能是甘特图、思维导图、时间线图、数据包图、看板图这五种类型，禁止出现结构图、三维结构图、示意图等图表类型。
+ first_level_type和type的组合必须是合法的，即first_level_type和type之间存在对应关系，例如当type为"架构图"时，first_level_type必须为"UML图"。具体对应关系请参考上述描述。
+ description中出现的图类型的描述需要与first_level_type和type中的图表类型保持一致。
+ 写作计划是添加图表的重要参考，确保写作计划中提到的图表都体现在输出中。
+ 同一个叶子节点可以有多个图表，只要它们服务于不同的目的或展示不同的信息。例如，一个叶子节点可以同时包含一个柱状图、两个折线图和一个表格，分别展示不同的数据，或者一个叶子节点可以同时有一个UML时序图、UML架构图。  请务必确保每个图表都有清晰的、独立的目的。
+ 在使用UML图时，因注重考虑论文标题与专业，文科类专业原则上不使用UML图，除非论文标题与论文内容与软件工程相关。
+ 以下专业应减少数据图的使用，增加表格、流程图的使用，在必要情况下可以使用数据图。
  - 哲学类专业（哲学、逻辑学、宗教学、伦理学、马克思主义哲学、中外哲学等）
  - 法学类专业（法学、政治学、社会学、民族学、马克思主义理论、公安学等）
  - 文学类专业（中国语言文学、外国语言文学、新闻传播学等）
  - 历史学类专业（历史学、世界史、考古学、文物与博物馆学等）
  - 艺术学类专业（音乐与舞蹈学、戏剧与影视学、美术学、产品艺术设计等）
  - 教育学（教育学、体育学等）
+ 应尽量减少桑基图、象限图、散点图等复杂数据图的使用，除非当前内容确实最适合桑基图、象限图、散点图。
+ 第三章、第四章、第五章并不是所有小节都需要图表，根据实际内容添加图表。
+ 数据图如果以年份为横坐标，不要在description中出现当前年份（2025）
+ 每个图表应独立呈现，避免将多个不同的图表内容强行合并到一个图表中。
+ 优先在实证分析章节及其子节点分配图表，特别是与实证分析方法（如相关性分析、回归分析、中介效应分析等）相对应的图表类型。
+ 如果全局数据分析中包含数据可视化方法，请优先根据可视化方法的要求，在写作计划的相应叶子节点中分配相应的图表。
+ 章节之间禁止出现重复的图表计划，每个图表计划都应该有区别于其他图表计划的图表描述和图表目的。

# step
+ step1: 先根据论文标题、专业和写作计划思考本文将会使用哪些图表类型，以及不用或者减少使用哪些图表类型。首先思考这篇文章是什么专业，如果是哲学类、法学类、文学类、历史学类、艺术学类、教育学等专业应该减少数据图的使用；理工科专业会使用复杂的数据图，例如散点图；软件工程专业或者论文标题与软件设计相关会使用UML图。
+ step2: 根据step1中分析的结果分析每个叶子节点具体应该使用什么类型的图表。step2中使用的图表类型应与step1中分析出的图表类型保持一致。
+ step1示例："根据论文标题《可持续发展视角下的绿色建筑设计策略》、专业为建筑学，以及写作计划内容，本文将会使用以下图表类型：\n1. 表格：用于展示研究数据的基本特征、设计策略的经济和环境数据等。\n2. 流程图：用于展示研究方法和技术路线。\n3. 其他类型的图：用于展示项目开发的时间安排和进度计划，如甘特图。\n4. 本文应该减少使用以下图表：XXX",
+ step2示例："根据step1中分析的结果，对论文的写作计划的每个小节的叶子节点进行分析，详细说明每个需要图表的每个叶子节点应该使用什么类型的图表来展示什么数据或者达到什么目的：\n1.1 节：无需图表。\n1.2 节：无需图表。\n1.3 节：需要一个流程图来展示研究的技术路线。\n1.4 节：需要一个流程图来展示研究方法的具体步骤。\n2.1 节：需要一个表格来展示可持续发展与绿色建筑设计的关系。\n2.2 节：需要一个数据图（柱状图）来展示国内外绿色建筑发展的现状与挑战。\n2.3 节：需要一个表格来展示绿色建筑设计的核心原则。\n3.2 节：需要一个数据图（柱状图）来展示绿色建筑选址的实践效果。\n3.3 节：需要一个数据图（折线图）来展示材料使用与能源管理的实践效果。\n3.4 节：需要一个数据图（饼图）来展示生态环境融合的实践效果。\n4.1 节：需要一个数据图（柱状图）来展示功能分区优化的效果。\n4.2 节：需要一个数据图（柱状图）来展示可再生材料的使用效果。\n4.3 节：需要一个数据图（折线图）来展示智能化系统的引入效果。\n4.4 节：需要一个数据图（柱状图）来展示与自然环境的互动设计效果。\n5.1 节：需要一个数据图（柱状图）来展示策略实施的经济效益。\n5.2 节：需要一个数据图（柱状图）来展示策略实施的环境效益。\n5.3 节：需要一个数据图（柱状图）来展示策略实施的社会效益。",

# 示例
## 表格
{
    "node_id": "4.1",
    "first_level_type": "表格",
    "type": "表格",
    "title": "样本数据基本特征表",
    "description": "样本数据基本特征表。表格的行分别表示不同地区、不同行业、不同时间段，列分别表示样本数量、平均值、标准差等统计指标。表格展示了样本数据的基本特征，如地区分布、行业分类和时间跨度。",
    "purpose": "通过表格展示样本数据的基本特征，确保数据的代表性和可靠性，为后续分析提供基础。"
}

## 数据图
{
    "node_id": "4.1",
    "first_level_type": "数据图",
    "type": "柱状图",
    "title": "大学生社交媒体使用频率分布",
    "description": "大学生社交媒体使用频率柱状图。X轴表示不同类型的社交媒体平台（如微信、微博、抖音、快手、Instagram、Facebook等），Y轴表示使用频率（如每天使用次数或每天使用时长）。柱状图的高度表示对应社交媒体平台的使用频率。",
    "purpose": "可视化展示大学生社交媒体使用情况，为后续分析社交媒体使用与心理健康的关系提供数据支持。"
}


## 流程图
{
    "node_id": "3.3",
    "first_level_type": "流程图",
    "type": "流程图",
    "title": "数据收集与分析流程",
    "description": "数据收集与分析流程图。流程图展示了从确定研究对象、设计问卷、发放问卷、收集数据、数据清洗到数据分析的各个步骤，以及步骤之间的逻辑关系。每个步骤用矩形框表示，步骤之间的顺序用箭头表示。",
    "purpose": "清晰地呈现研究的数据收集和分析过程，使读者能够更好地理解研究方法，并验证研究方法的科学性和严谨性。"
}

## UML图
{
    "node_id": "3.1",
    "first_level_type": "UML图",
    "type": "时序图",
    "title": "物品出库时序图",
    "description": "物品出库时序图。时序图展示了物品出库时各个参与者（如仓库管理员、系统、物品）之间的交互过程，包括消息的发送和接收顺序、消息的内容、参与者的活动顺序等。",
    "purpose": "通过时序图设计，可以清晰地展示仓库管理系统中各组件的交互逻辑。这种设计不仅便于作者理解系统架构，还能帮助识别潜在的性能瓶颈和优化点。"
}

## 其他类型的图
{
    "node_id": "2.1",
    "first_level_type": "其他类型的图",
    "type": "甘特图",
    "title": "项目开发时间安排甘特图",
    "description": "项目开发时间安排甘特图。甘特图展示了项目开发的时间安排和进度计划，包括项目启动、需求分析、设计、编码、测试、上线等各个阶段的时间节点和持续时间。",
    "purpose": "通过甘特图清晰地展示项目开发的时间安排和进度计划，帮助团队成员了解项目的整体进度和各个阶段的工作内容，提高项目管理的效率和质量。"
}

# 输出格式
{
    "step1": "根据论文标题、专业和写作计划思考本文将会使用哪些图表类型，请在这个字段中对论文整体框架和内容进行分析，依次列出所有适合插入图表的类型。",
    "step2": "在这个字段根据step1中分析的结果再对中对论文的写作计划的每个小节的叶子节点进行分析，详细说明每个需要图表的每个叶子节点应该使用什么类型的图表来展示什么数据或者达到什么目的",
    "additions": [
        {
            "node_id": "写作计划中的节点id",
            "title": "图表的名称，不需要序号",
            "first_level_type": "[表格|数据图|流程图|UML图|其他类型的图]",
            "type": "根据first_level_type选择具体的图表类型",
            "description": "图表的详细描述",
            "purpose": "图表在论文中的作用"
        },
    ]
}

## 示例输出1
{
    "step1": "根据本论文名称《XXX》，专业为XXX，以及写作计划内容，本文将会使用这些图表：流程图：用于XXX目的，表格：用于XXX目的，数据图：用于XXX目的"
    "step2": "本论文主要研究大学生社交媒体使用对学业成绩的影响。根据写作计划和全文数据分析信息，适合在以下节点添加图表：\n1. 在4.1.1节（样本描述性统计），通过多个柱状图和饼图详细展示大学生社交媒体使用情况（时长、频率、不同平台的使用偏好），全面呈现数据分布特征，为后续分析提供背景信息。\n2. 在4.1.2节（样本描述性统计），通过表格和直方图展示大学生学业成绩（GPA）的详细分布情况，提供学业成绩的全面概览，并检查数据分布形态（是否正态分布）。\n3. 在4.2节（相关性分析），通过表格展示社交媒体使用与学业成绩的相关性分析结果，为后续回归分析提供关键依据，初步判断变量间关系。\n4. 在4.3节（回归分析），通过多个表格详细展示社交媒体使用对学业成绩影响的回归分析结果（包括不同模型、不同变量组合），深入揭示变量间的关系，并验证假设。"    "additions": [
    "additions": [
        {
            "node_id": "4.1.1",
            "title": "大学生常用社交媒体平台使用频率",
            "first_level_type": "数据图",
            "type": "柱状图",
            "description": "X轴表示不同社交媒体平台（如微信、微博、QQ、抖音、快手、小红书等），Y轴表示使用频率（例如，每天使用的学生比例）。",
            "purpose": "直观展示大学生最常用的社交媒体平台及其使用频率，为后续分析提供背景信息。"
        },
        {
            "node_id": "4.1.1",
            "title": "大学生社交媒体日均使用时长分布",
            "first_level_type": "数据图",
            "type": "饼图",
            "description": "饼图的不同扇形区域代表不同的日均使用时长区间（如：0-1小时、1-2小时、2-3小时、3-4小时、4小时以上），扇形区域的大小表示该时长区间内的学生占比。每个扇形区域标注百分比。",
            "purpose": "直观展示大学生社交媒体日均使用时长的分布情况，为后续分析提供背景信息。"
        },
        {
            "node_id": "4.1.1",
            "title": "不同类型社交媒体平台使用时长对比",
            "first_level_type": "数据图",
            "type": "柱状图",
            "description": "X 轴表示不同类型的社交媒体平台（例如，社交类、短视频类、资讯类等），Y 轴表示平均每日使用时长。",
            "purpose": "比较不同类型社交媒体平台的使用时长差异，为后续分析提供更细致的背景信息。"
        },
        {
            "node_id": "4.1.2",
            "title": "大学生学业成绩（GPA）分布",
            "first_level_type": "表格",
            "type": "表格",
            "description": "表格展示不同GPA区间的学生人数和占比（如：<2.0, 2.0-2.5, 2.5-3.0, 3.0-3.5, 3.5-4.0），以及平均GPA、标准差、中位数、最小值和最大值。",
            "purpose": "展示大学生学业成绩的整体分布情况，为后续分析提供背景信息。"
        },
        {
            "node_id": "4.1.2",
            "title": "大学生GPA分布直方图",
            "first_level_type": "数据图",
            "type": "柱状图",
            "description": "X轴代表GPA分数段（例如，以0.1为间隔），Y轴代表每个分数段的学生人数。",
            "purpose": "更直观地展现GPA数据的分布形态，例如是否正态分布，是否存在偏态等。"
        },
        {
            "node_id": "4.2",
            "title": "社交媒体使用与学业成绩的相关性分析",
            "first_level_type": "表格",
            "type": "表格",
            "description": "表格展示社交媒体使用时长、使用频率、常用平台等变量与学业成绩（GPA）之间的Pearson相关系数，以及显著性水平（p值）。相关系数的绝对值大小表示相关强度，正负号表示相关方向。",
            "purpose": "展示社交媒体使用与学业成绩之间是否存在显著的相关关系，为后续回归分析提供基础。"
        },
        {
            "node_id": "4.3",
            "title": "社交媒体使用对学业成绩影响的回归分析（模型1）",
            "first_level_type": "表格",
            "type": "表格",
            "description": "表格展示回归模型的详细结果，包括自变量（社交媒体使用时长）、因变量（GPA）、回归系数（β）、标准误、t值、p值、R方、调整R方等。",
            "purpose": "展示社交媒体使用时长对学业成绩的直接影响。"
        },
        {
            "node_id": "4.3",
            "title": "社交媒体使用对学业成绩影响的回归分析（模型2）",
            "first_level_type": "表格",
            "type": "表格",
            "description": "表格展示回归模型的详细结果，包括自变量（社交媒体使用时长、使用频率）、因变量（GPA）、回归系数（β）、标准误、t值、p值、R方、调整R方等。",
            "purpose": "在控制使用频率后，展示社交媒体使用时长对学业成绩的影响。"
        }
        {
            "node_id": "4.3",
            "title": "社交媒体使用对学业成绩影响的回归分析（模型3）",
            "first_level_type": "表格",
            "type": "表格",
            "description": "表格展示回归模型的详细结果，包括自变量（社交媒体使用时长、使用频率、常用平台、人口统计学变量等）、因变量（GPA）、回归系数（β）、标准误、t值、p值、R方、调整R方等。",
            "purpose": "在控制多个变量后，展示社交媒体使用对学业成绩的净影响，以及各变量的贡献程度。"
        }
    ]
}

## 示例输出2
{
    "step1": "根据本论文名称《XXX》，专业为XXX，以及写作计划内容，本文将会使用这些图表：流程图：用于XXX目的，表格：用于XXX目的，数据图：用于XXX目的"
    "step2": "本论文研究短视频平台使用对消费者购买决策的影响。根据提供的写作计划和全局数据分析信息，适合在以下节点添加图表：\n1.  3.1 (研究模型与假设) : 使用流程图展示短视频平台使用对消费者购买决策影响的概念模型, 清晰呈现变量之间的关系与假设。\n2.  4.1.1 （用户画像）: 使用柱状图和饼图展示短视频平台用户的基本画像（如年龄、性别、职业、收入等），提供用户群体的整体概览。\n3.  4.1.2 （用户行为）: 使用柱状图或折线图展示用户在短视频平台上的使用行为（如日均使用时长、观看时长、互动频率等），提供用户行为特征的全面描述。\n4.  4.2 （相关性分析）: 使用表格展示短视频平台使用（使用时长、互动频率、观看类型等）与购买决策（购买意愿、购买频率、品牌认知等）之间的相关性分析结果，初步判断变量间关系。\n5.  4.3.1 （回归分析：模型1）： 使用表格展示短视频平台使用时长对购买意愿影响的回归分析结果。\n6.  4.3.2（回归分析：模型2）：使用表格展示在加入互动频率后，短视频平台使用对购买意愿影响的回归分析结果。\n7.  4.4 （中介效应分析）: 使用表格展示中介效应检验结果,清晰呈现变量间的逻辑关系。",
    "additions": [
        {
            "node_id": "3.1",
            "title": "研究模型",
            "first_level_type": "流程图",
            "type": "流程图",
            "description": "流程图展示短视频平台使用（自变量）、消费者态度（中介变量）和购买决策（因变量）之间的关系。箭头表示影响方向，并标注假设。",
            "purpose": "清晰展示研究模型中各变量之间的关系和研究假设。"
        },
        {
            "node_id": "4.1.1",
            "title": "短视频平台用户年龄分布",
            "first_level_type": "数据图",
            "type": "柱状图",
            "description": "X 轴表示不同年龄段（例如，18-24岁、25-34岁、35-44岁、45岁以上），Y 轴表示每个年龄段的用户占比。",
            "purpose": "展示短视频平台用户的主要年龄段分布情况。"
        },
        {
            "node_id": "4.1.1",
            "title": "短视频平台用户性别分布",
            "first_level_type": "数据图",
            "type": "饼图",
            "description": "饼图展示男性用户和女性用户的占比。",
            "purpose": "展示短视频平台用户的性别比例。"
        },
        {
            "node_id": "4.1.2",
            "title": "用户日均使用短视频平台时长分布",
            "first_level_type": "数据图",
            "type": "柱状图",
            "description": "X轴代表日均使用时长区间（如0-30分钟，30-60分钟，1-2小时，2小时以上), Y轴代表每个区间的用户占比",
            "purpose": "展示用户在短视频平台上的活跃程度。"
        },
        {
            "node_id": "4.1.2",
            "title": "用户在短视频平台日均观看时长",
            "first_level_type": "数据图",
            "type": "折线图",
            "description": "X 轴表示日期（例如，最近一个月），Y 轴表示日均观看时长（分钟）。",
            "purpose": "展示用户日均观看时长的变化趋势，反映用户粘性。"
        },
        {
            "node_id": "4.2",
            "title": "短视频平台使用与购买决策相关性分析",
            "first_level_type": "表格",
            "type": "表格",
            "description": "表格展示短视频平台使用时长、互动频率、观看类型（如：产品测评、直播带货、广告）与购买决策（购买意愿、购买频率、品牌认知）之间的Pearson相关系数、Spearman等级相关系数，以及显著性水平（p值）。",
            "purpose": "初步展示短视频平台使用与购买决策之间是否存在显著的相关关系。"
        },
        {
            "node_id": "4.3.1",
            "title": "短视频平台使用时长对购买意愿影响的回归分析",
            "first_level_type": "表格",
            "type": "表格",
            "description": "表格展示回归模型的详细结果，包括自变量（短视频平台使用时长）、因变量（购买意愿）、回归系数（β）、标准误、t值、p值、R方、调整R方等。",
            "purpose": "展示短视频平台使用时长对购买意愿的直接影响。"
        }
        {
            "node_id": "4.3.2",
            "title": "短视频平台使用对购买意愿的回归分析（包含互动频率）",
            "first_level_type": "表格",
            "type": "表格",
            "description": "表格展示回归模型的详细结果，包括自变量（短视频使用时长，互动频率）、因变量（购买意愿）、回归系数（β）、标准误、t值、p值、R方、调整R方等。",
            "purpose": "展示在控制互动频率后，短视频平台使用时长对购买意愿的影响。"
        },
        {
            "node_id": "4.4",
            "title": "中介效应检验结果",
            "first_level_type": "表格",
            "type": "表格",
            "description": "表格展示中介效应检验的详细结果，包括直接效应、间接效应、总效应的估计值、标准误、置信区间和显著性水平（p值）。",
            "purpose": "展示中介效应是否显著，以及中介效应的大小。"
        }
    ]
}