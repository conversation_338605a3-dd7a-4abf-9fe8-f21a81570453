# 写作计划要求
## 章节结构要求
- 必须包括绪论、正文章节和结论三个部分
    + 绪论（第一章）：介绍项目背景、意义、目标及方法。不得包含毕业设计结构安排或框架相关内容。
    + 正文章节：为毕业设计核心主体，按项目设计各阶段划分章节，不得遗漏任何项目设计内容。
    + 结论（最后一章）：独立于用户设计之外，凝练项目成果，不得包含建议或优化策略。结论为独立总结性章节，不是用户设计的最后阶段。
- 一级目录（章）数量应与项目设计工作中phases数量相仿。仅当两个phase关系紧密且deliverable总数小于7时可合并为一章，其余每个phase单独成章。
- 不需包含致谢或参考文献，这些不属于正文结构。

## 字数分配规则
- 绪论：占全文5～10%。
- 正文章节：占全文80～90%。根据各章内容重要性分配字数，重点章节多分配，辅助章节少分配。
- 结论：占全文5～10%。

## 章节层级设计规范
- 任何非叶子节点都应包含至少两个子节点。禁止出现仅有一个子节点的父节点。
- 每个叶子节点字数（writing_length）不得少于500字，不得超过1500字。
- 避免过度细化三级节点：简单内容直接通过二级标题完成分析，不需额外展开。
- 逻辑递进：标题设计从整体到细节逐步展开，确保上下层标题之间明确的逻辑联系。
- 结论禁止生成三级节点。
- 禁止生成四级节点。

## 项目设计使用要求
1. 严格依据用户项目设计：所有正文章节内容必须直接根据用户项目设计，确保完整覆盖研究范围与重点。
2. 确保所有 deliverables 都被分配，且同一个 deliverable 不被多个小节使用。

## 输出结构要求
- 只有叶子节点需要输出`description`字段。
- 如果二级节点有子节点， 则该节点不需要输出`description`字段， 只需在三级节点输出。
- 如果一级节点没有子节点， 则该点需要输出`description`字段。
- `description` 内容在80字左右，确保足够的篇幅展开讨论。