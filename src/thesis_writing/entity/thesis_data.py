from typing import Optional, List

from pydantic import BaseModel, Field


class TOCItem(BaseModel):
    id: int = Field(default=None, description="目录id")
    title: str = Field(default=None, description="目录项标题")
    level: int = Field(default=None, description="目录项层级")
    page_num: int = Field(default=None, description="页码")
    child_items: Optional[List['TOCItem']] = Field(default=None, description="子目录")


class Chapter(BaseModel):
    title: str = Field(default="", description="章节标题")
    content: str = Field(default="", description="章节内容")
    level: int = Field(default=None, description="章节层级")
    child_items: Optional[List['Chapter']] = Field(default=None, description="子章节")


class Thesis(BaseModel):
    name: str = Field(default=None, description="论文名称")
    subject: str = Field(default=None, description="论文学科")
    abstract_zh: str = Field(default=None, description="中文摘要")
    keywords_zh: str = Field(default=None, description="中文关键词")
    abstract_en: str = Field(default=None, description="英文摘要")
    keywords_en: str = Field(default=None, description="中文关键词")
    toc: TOCItem = Field(default=None, description="目录")
    chapters: List[Chapter] = Field(default_factory=list, description="章节")

class ProcessDocumentation(BaseModel):
    title: str = Field(..., description="章节标题")
    content: str = Field(..., description="章节内容")
    thought: Optional[str] = Field(default=None, description="思考过程")
    chart: Optional[str] = Field(default=None, description="章节的图表")

    def get_content(self):
        return self.title + "\n" + self.content
