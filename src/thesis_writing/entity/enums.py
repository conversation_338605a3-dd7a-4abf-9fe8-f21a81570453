from enum import IntEnum, StrEnum


class AgentType(StrEnum):
    GEN_SUMMARY = "gen_summary"

    ANALYZE_CORE_ELEMENTS = "analyze_core_elements"

    GEN_PLAN = "gen_plan"
    REFINE_PLAN_NODE = "refine_plan_node"
    COMPLETE_PLAN = "complete_plan"

    GEN_GLOBAL_DATA_QUERY = "gen_global_data_query"
    EXTRACT_GLOBAL_DATA_QUERY_RESULT = "extract_global_data_query_result"
    SUMMARIZE_GLOBAL_DATA = "summarize_global_data"

    ANALYSIS_EMPIRICAL_DATA = "analysis_empirical_data"
    GEN_EMPIRICAL_QUERY = "gen_empirical_query"
    EXTRACT_EMPIRICAL_QUERY_RESULT = "extract_empirical_query_result"
    FAKE_EMPIRICAL_DATA = "fake_empirical_data"

    GEN_PLAN_ADDITION = "gen_plan_addition"

    GEN_FEED_GUIDE = "gen_feed_guide"

    GEN_RETRIEVAL_REFERENCE_QUERY = "gen_retrieval_reference_query"
    GEN_CHAPTER_QUERY = "gen_chapter_query"
    GEN_USER_FEED_QUERY = "gen_user_feed_query"
    REWRITE_CHAPTER_QUERY = "rewrite_chapter_query"
    SUMMARIZE_TEXT = "summarize_text"
    SUMMARIZE_CHUNK = "summarize_chunk"

    CHOOSE_RETRIEVAL_RESULT = "choose_retrieval_result"
    GEN_SEGMENT = "gen_segment"

    REVIEW_SEGMENTS = "review_segments"
    REFINE_SEGMENTS = "refine_segments"

    GEN_CONCLUSION = "gen_conclusion"
    GEN_ABSTRACT_KEYWORDS = "gen_abstract_keywords"

    GEN_EN_ABSTRACT = "gen_en_abstract"
    GEN_EN_KEYWORDS = "gen_en_keywords"
    GEN_EN_TITLE = "gen_en_title"

    GEN_ACKNOWLEDGEMENT = "gen_acknowledgement"

    REFINE_TITLE = "refine_title"
    GEN_KEYWORDS = "gen_keywords"
    REFINE_REFERENCE = "refine_reference"
    GEN_TOPIC = "gen_topic"
    GEN_APPENDIX = "gen_appendix"
    GEN_APPENDIX_ITEM = "gen_appendix_item"

    REFINE_CHART = "refine_chart"
    GEN_PIE_CHART = "gen_pie_chart"
    GEN_LINE_CHART = "gen_line_chart"
    GEN_BAR_CHART = "gen_bar_chart"
    GEN_TABLE = "gen_table"
    GEN_FLOW_CHART = "gen_flow_chart"
    GEN_QUADRANT_CHART = "gen_quadrant_chart"
    GEN_SANKEY_DIAGRAM = "gen_sankey_diagram"
    GEN_SCATTER_CHART = "gen_scatter_chart"
    GEN_RADAR_CHART = "gen_radar_chart"
    GEN_ER_DIAGRAM = "gen_er_diagram"
    GEN_CLASS_DIAGRAM = "gen_class_diagram"
    GEN_SEQUENCE_DIAGRAM = "gen_sequence_diagram"
    GEN_STATE_DIAGRAM = "gen_state_diagram"
    GEN_ARCHITECTURE_DIAGRAM = "gen_architecture_diagram"
    GEN_GANTT_DIAGRAM = "gen_gantt_diagram"
    GEN_MINDMAP_DIAGRAM = "gen_mindmap_diagram"
    GEN_TIMELINE_DIAGRAM = "gen_timeline_diagram"
    GEN_PACKET_DIAGRAM = "gen_packet_diagram"
    GEN_KANBAN_DIAGRAM = "gen_kanban_diagram"
    GEN_USECASE_DIAGRAM = "gen_use_case_diagram"
    GEN_TECHNIQUE_ROADMAP_DIAGRAM = "gen_technique_roadmap_diagram"

    GEN_TASK_STATEMENT = "gen_task_statement"
    GEN_THESIS_PROPOSAL_QUERY = "gen_thesis_proposal_query"
    GEN_THESIS_PROPOSAL = "gen_thesis_proposal"
    DETERMINE_MODULES_NEED_REFERENCE = "determine_modules_need_reference"
    GEN_LITERATURE_REVIEW_PLAN = "gen_literature_review_plan"
    GEN_LITERATURE_REVIEW = "gen_literature_review"
    RE_GEN_LITERATURE_REVIEW_PLAN = "re_gen_literature_review_plan"
    GEN_THESIS_MIDTERM_REPORT = "gen_thesis_midterm_report"

    GEN_THESIS_DEFENSE_PPT_PLAN = "gen_thesis_defense_ppt_plan"
    GEN_THESIS_DEFENSE_PPT = "gen_thesis_defense_ppt"
    GEN_THESIS_DEFENSE_QA = "gen_thesis_defense_qa"

    REWRITE = "rewrite"
    CHECK_REWRITE = "check_rewrite"
    GEN_REWRITE_INSTRUCTION = "gen_rewrite_instruction"

    GEN_DEFAULT_QUESTIONNAIRE = "gen_default_questionnaire"
    GEN_CUSTOM_QUESTIONNAIRE = "gen_custom_questionnaire"
    GEN_QUESTIONNAIRE_EXPECTED_RESULT = "gen_questionnaire_expected_result"
    GEN_QUESTION_DISTRIBUTIONS = "gen_question_distributions"
    GEN_QUESTIONNAIRE_REPORT = "gen_questionnaire_report"
    DEDUPLICATE_QUESTIONNAIRE_REPORT = "deduplicate_questionnaire_report"

    GEN_ENGINEERING_PLAN = "gen_engineering_plan"
    GEN_ENGINEERING_SEGMENT = "gen_engineering_segment"
    GEN_ARCH_SEGMENT = "gen_arch_segment"
    GEN_ENGINEERING_TASK_STATEMENT = "gen_engineering_task_statement"
    GEN_ENGINEERING_PROPOSAL = "gen_engineering_proposal"
    GEN_ENGINEERING_SUMMARY = "gen_engineering_summary"
    GEN_ENGINEERING_CONCLUSION = "gen_engineering_conclusion"
    GEN_ENGINEERING_ABSTRACT_KEYWORDS = "gen_engineering_abstract_keywords"

    REFINE_LEAD_PARAGRAPH_WITH_THESIS_SEGMENT = (
        "refine_lead_paragraph_with_thesis_segment"
    )
    REFINE_SEGMENTS_WITH_THESIS_SEGMENT = "refine_segments_with_thesis_segment"
    GEN_CHAPTER_QUERY_WITH_THESIS_SEGMENT = "gen_chapter_query_with_thesis_segment"
    REVIEW_PAPER_WITH_THESIS_SEGMENT = "review_paper_with_thesis_segment"
    REFINE_PAPER_WITH_THESIS_SEGMENT = "refine_paper_with_thesis_segment"
    REVISE_ENGINEERING_PLAN = "revise_engineering_plan"
    REVISE_ENGINEERING_SEGMENT = "revise_engineering_segment"

    # short paper
    GEN_SHORT_SUMMARY = "gen_short_summary"
    GEN_SHORT_PAPER_PLAN = "gen_short_paper_plan"
    GEN_SHORT_SHORT_PLAN = "gen_short_short_plan"
    GEN_SHORT_PLAN_ADDITION = "gen_short_plan_addition"
    GEN_SHORT_SEGMENT = "gen_short_segment"
    GEN_SHORT_ACKNOWLEDGEMENT = "gen_short_acknowledgement"
    GEN_SHORT_ABSTRACT_KEYWORDS = "gen_short_abstract_keywords"
    GEN_SHORT_CONCLUSION = "gen_short_conclusion"

    GEN_INTERNSHIP_REPORT = "gen_internship_report"
    GEN_INTERNSHIP_REPORT_QUERY = "gen_internship_report_query"
    GEN_FAKE_INTERNSHIP_INFO = "gen_fake_internship_info"
    GEN_INTERNSHIP_CONTENT = "gen_internship_content"

    GEN_ACKNOWLEDGEMENT_TOB = "gen_acknowledgement_tob"

    GEN_FOOTNOTE = "gen_footnote"


class WritingPlanContentType(StrEnum):
    Introduction = "绪论"
    Chapter = "正文章节"
    Conclusion = "结论"


class CompletePlanUpdateType(StrEnum):
    UPDATE_DESCRIPTION = "UPDATE_DESCRIPTION"
    UPDATE_WRITING_LENGTH = "UPDATE_WRITING_LENGTH"
    UPDATE_CONTENT_TYPE = "UPDATE_CONTENT_TYPE"


class ReGenPlanComparisonResult(StrEnum):
    Same = "same"
    Different = "different"
    Ignore = "ignore"


class RetrievalReferenceLanguage(StrEnum):
    En = "en"
    Zh = "zh"


class EducationLevel(IntEnum):
    Degree = 1
    Bachelor = 2
    Graduate = 3


class ThesisDefensePPTPageType(StrEnum):
    # 首页
    # 目录页
    # 章节首页
    # 章节内容页
    # 参考文献页
    # 尾页
    Cover = "cover"
    TOC = "toc"
    ChapterCover = "chapter_cover"
    ChapterContent = "chapter_content"
    Reference = "reference"
    End = "end"


class StageModule(StrEnum):
    Toc = "目录"

class SearchEngine(StrEnum):
    Bing = "Bing"
    Qwen = "Qwen search"