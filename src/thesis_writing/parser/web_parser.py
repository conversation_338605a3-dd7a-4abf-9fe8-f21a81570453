import asyncio
import logging

import aiohttp
import certifi
import trafilatura
import trafilatura.downloads
import urllib3
from langchain_text_splitters import MarkdownHeaderTextSplitter
from langchain_text_splitters import RecursiveCharacterTextSplitter
from trafilatura.meta import reset_caches
from urllib3.util import Retry


class WebParser:
    def __init__(self, http_proxy_url: str, http_proxy_auth: str):
        self.black_list = [
            "zhihu.com",
            "www.docin.com"
        ]

        _retry_strategy = urllib3.util.Retry(
            total=1,  # Total number of retries to allow.
            redirect=1,
            connect=0,
            backoff_factor=1,
            status_forcelist=trafilatura.downloads.FORCE_STATUS,
        )
        trafilatura.downloads.RETRY_STRATEGY = _retry_strategy

        self.direct_http_pool = trafilatura.downloads.create_pool(
            retries=_retry_strategy,
            timeout=urllib3.Timeout(connect=1.0, read=3.0),
            maxsize=50,
            ca_certs=certifi.where()
        )

        self.proxy_http_pool = None
        if http_proxy_url and http_proxy_auth:
            self.proxy_http_pool = urllib3.ProxyManager(
                proxy_url=http_proxy_url,
                proxy_headers=urllib3.util.make_headers(proxy_basic_auth=http_proxy_auth),
                retries=_retry_strategy,
                maxsize=20,
                timeout=urllib3.Timeout(connect=1.0, read=3.0, total=4),
                ca_certs=certifi.where()
            )

    async def invoke(self, url: str) -> list[str]:
        if any([black in url for black in self.black_list]):
            return []

        try:
            downloaded = await self.do_fetch_url(url)

            text = trafilatura.extract(downloaded, include_comments=False, include_tables=True,
                                       output_format="markdown")
            reset_caches()

            if text is None:
                return []

            headers_to_split_on = [
                ("#", "Header 1"),
                ("##", "Header 2"),
            ]
            markdown_splitter = MarkdownHeaderTextSplitter(headers_to_split_on=headers_to_split_on, strip_headers=False)
            md_header_splits = markdown_splitter.split_text(text)
            chunk_size = 1000
            chunk_overlap = 200
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=chunk_size, chunk_overlap=chunk_overlap
            )
            splits = text_splitter.split_documents(md_header_splits)
            return [each.page_content for each in splits]
        except Exception as e:
            print(f"Error processing {url}: {str(e)}")

        return []

    async def batch_invoke(self, urls: list) -> list:
        tasks = [self.invoke(url) for url in urls]

        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logging.error(f"asyncio.gather web_fetch failed: {str(e)}")
            return [[]] * len(urls)

        processed_results = []
        for idx, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append([])
            else:
                processed_results.append(result)

        return processed_results

    async def do_fetch_url(self, url, timeout: int = 5):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, ssl=False, timeout=timeout) as response:
                    downloaded = await response.text()
        except Exception as e:
            logging.error(f"Error fetching url {url}: {str(e)}")
            downloaded = None

        try:
            if downloaded is None and self.proxy_http_pool:
                trafilatura.downloads.HTTP_POOL = self.proxy_http_pool
                downloaded = trafilatura.fetch_url(url)
        except Exception as e:
            logging.error(f"Error fetching url with proxy {url}: {str(e)}")
            downloaded = None

        return downloaded


class WebParserFactory:
    _clients = {}

    @classmethod
    def get_client(cls, proxy_url: str, proxy_auth: str) -> WebParser:
        key = f"{proxy_url}-{proxy_auth}"
        if key not in cls._clients:
            cls._clients[key] = WebParser(proxy_url, proxy_auth)

        return cls._clients[key]


def web_parser_factory(proxy_url: str, proxy_auth: str) -> WebParser:
    return WebParserFactory.get_client(proxy_url, proxy_auth)
