from dotenv import load_dotenv
from langfuse.decorators import observe

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.gen_footnote_agent import GenerateFootNoteAgentInput, FootNoteChapter
from thesis_writing.entity.enums import AgentType
from thesis_writing.retriever.index.book_chunk import BookChunk
from thesis_writing.retriever.index.thesis_chunk import ThesisChunk
from thesis_writing.retriever.index.web_chunk import WebChunk

load_dotenv()


@observe(name="test_footnote")
def test_gen_footnote():
    failover_options_list = [
        TestUtil.get_ds_model_options_from_env()
    ]

    agent = AgentFactory.create_agent(AgentType.GEN_FOOTNOTE,
                                      TestUtil.get_qiniu_model_options_from_env(model="qwen3-235b-a22b-instruct-2507", temperature=0.7),
                                      failover_options_list)
    materials_dict = test_gen_materials()
    chapter1 = FootNoteChapter(
        node_id="2.1",
        title="2.1用户需求与角色分析",
        materials=materials_dict,
        segment_content="""
        用户角色和权限的合理划分是保障图书管理系统安全与高效运行的基础。在本系统中，主要定义了管理员和读者两类用户角色，其操作需求与权限范围严格区分，以满足不同用户群体的使用需求。管理员作为系统的最高权限持有者，负责系统管理、图书管理、借阅管理和用户管理等核心功能，而读者则主要进行借阅、查询和个人信息管理等操作。这种权限设计遵循最小授权原则，既保证了系统的安全性，又满足了用户的基本需求。

管理员角色的操作需求与权限范围覆盖了系统的核心管理功能。首先，管理员可以对所有用户账号进行管理，包括用户注册、信息更新、账户注销等操作，确保用户数据的完整性和一致性。其次，管理员负责维护图书信息，包括新增、删除、修改图书条目以及更新出版信息，确保图书数据的准确性与实时性。此外，管理员还需要处理借还书过程中的异常情况，如超期罚款结算和丢失赔偿登记，并能够查询所有用户的借阅记录及图书库存状态。这些操作权限不仅体现了管理员在系统中的主导地位，也为系统运行的稳定性和高效性提供了保障[[5504301]]。

读者角色的操作需求与权限范围则更加注重用户友好性和操作便捷性。读者通过校园卡刷卡验证身份后，可以借阅图书、归还图书，并在系统中查询书籍信息。查询功能支持按书名、作者、分类号等多种条件检索，满足读者多样化的需求。读者还可以访问电子阅览室资源，查看个人借阅历史、当前借阅状态及应付费用。权限设计遵循最小授权原则，确保读者无法访问后台管理界面或删除其他用户的借阅记录，从而保障系统的安全性和数据的完整性[[4308187]]。表2-1列出了管理员和读者两类用户角色的具体操作权限，清晰展示了权限边界。<chart id="表2-1"/>

用户权限设计的原则在于平衡系统安全与用户体验。一方面，通过最小授权原则和基于角色的访问控制（RBAC），确保每个用户只能访问与其角色相关的功能，降低了潜在的安全风险。另一方面，权限设计充分考虑了用户的实际需求，通过合理的功能划分和权限配置，提升了系统的易用性和用户满意度。这种权限设计不仅为后续用例建模提供了依据，也为系统的稳定运行奠定了基础。
        """
    )

    chapter2 = FootNoteChapter(
        node_id="2.2",
        title="2.2系统功能模块定义",
        materials=materials_dict,
        segment_content="""
        图书管理系统的功能模块划分是系统设计的核心环节，其目的是通过合理划分业务功能，实现模块间的松耦合与高内聚，从而提升系统的可扩展性与维护效率。根据业务流程与功能需求，系统通常划分为书籍管理、借阅管理、用户管理和查询功能四大模块。书籍管理模块负责图书元数据的维护，包括书籍名称、ISBN编号、作者、出版社等信息的增删改查操作，并支持图书的标签分类管理与库存状态更新。借阅管理模块则处理图书流通相关的所有操作，如借书、还书、续借、罚款管理等，确保图书资源的高效利用。用户管理模块专注于读者信息的全生命周期管理，包括注册、权限配置、身份认证等，保障系统的安全性与数据一致性。查询功能模块提供多维度检索能力，支持读者按书名、作者、分类号等条件查询图书信息，并允许管理员进行批量数据导入导出操作，便于数据迁移与备份。各模块通过统一的数据接口交互，形成松耦合、高内聚的系统架构，为后续UML建模奠定了基础[[3446382]]。

书籍管理模块是系统的核心功能之一，其主要职责包括维护图书元数据、管理库存状态以及支持标签分类管理。具体而言，该模块支持图书信息的增删改查操作，确保数据的一致性与完整性。在新增书籍时，系统需要录入ISBN、书名、作者、出版社等信息，并指定其分类号与存放位置。在更新图书信息时，系统需提供便捷的编辑功能，以便管理员及时调整图书的出版信息或替换损坏书籍。此外，书籍管理模块还需实时更新图书的库存状态，确保系统数据与实际情况相符，为借阅管理模块提供准确的参考信息。通过这些功能，书籍管理模块能够有效地支持图书馆的日常运营与资源管理。

借阅管理模块是图书管理系统中最频繁使用的功能模块之一，其主要职责包括处理借书、还书、续借及罚款管理等操作。在借书过程中，系统需验证读者身份，检查目标图书的可用性，并创建借阅记录。在还书时，系统会自动检测是否超期并计算应缴罚款，随后更新借阅状态。续借功能允许读者在未逾期的前提下延长借阅周期，而罚款管理模块则支持在线缴纳与结算，同步更新借阅状态。通过这些功能，借阅管理模块能够高效地支持图书的流通管理，提升读者的使用体验[[2579412]]。

用户管理模块负责读者信息的全生命周期管理，包括用户的注册、信息修改、权限配置以及账户状态监控。在用户注册时，系统需录入校园卡号、姓名、密码等信息，并为读者分配初始权限。在后续使用过程中，用户管理模块支持信息修改、权限调整及账户状态监控，确保系统的安全性与数据一致性。此外，该模块还负责处理用户账户的启用与停用操作，如冻结长期未还书的用户账户。通过这些功能，用户管理模块能够有效地保障系统的安全性和用户数据的隐私。

查询功能模块是系统中重要的辅助功能，其主要职责包括支持多维度检索、生成统计报表以及提供数据导出功能。在图书检索方面，该模块支持按书名、作者、分类号、出版年份等条件查找图书，并提供详细的图书信息展示。对于管理员，查询功能模块还支持生成借阅排行榜、馆藏分布等统计报表，辅助决策。同时，该模块还支持批量数据导入导出操作，便于数据迁移与备份。通过这些功能，查询功能模块能够满足用户多样化的信息需求，提升系统的实用性和扩展性。

各功能模块通过统一的数据接口交互，形成松耦合、高内聚的系统架构。书籍管理模块与借阅管理模块通过共享图书信息和库存状态，确保数据的一致性；用户管理模块与查询功能模块则通过用户信息的传递，支持权限控制与数据检索。这种模块化设计不仅降低了模块间的耦合度，还提升了系统的可维护性与扩展性。通过合理划分功能模块并明确其交互关系，图书管理系统能够高效地支持图书馆的日常运营，满足不同用户的需求[[3446382]]。
              """
    )

    agent_input = GenerateFootNoteAgentInput(
        subject="图书管理系统UML设计",
        major="软件工程",
        chapters=[chapter1, chapter2]
    )
    output = agent.invoke(agent_input)

    print(output.model_dump_json(indent=2))


def test_gen_materials():
    materials_dict = {}
    web_chunks = []
    web_chunk_json = {
        "chunk_id": "9ac0f21d-ae86-428d-8827-ec41779e0e7f",
        "temp_id": None,
        "content": "图书管理系统中，用户角色和权限的合理划分是保障系统安全和高效运行的关键。通常，系统会定义管理员、图书管理员、读者等核心角色。管理员拥有最高权限，可管理所有用户账号、配置系统参数、维护数据库及查看全部操作日志；图书管理员负责书籍的编目、借阅处理、库存管理和日常维护，其权限受限于数据修改范围（如仅能编辑自己负责的馆藏）；普通读者则只能查询书目信息、在线预约、续借归还图书，并查看个人借阅记录。权限设置需遵循最小授权原则，例如，读者无法访问后台管理界面或删除其他用户的借阅记录。此外，现代系统常支持基于角色的访问控制（RBAC），允许灵活分配细粒度权限，如将某位图书管理员授予特定区域的书籍管理权，从而实现精细化管控。",
        "refine_content": None,
        "session_id": "fae1c64b-f6c8-46a7-b96b-784f4b8fc8ed",
        "embedding": None,
        "query": "图书管理系统 用户角色 权限设置 功能对照",
        "url": "",
        "title": "图书管理系统用户角色与权限设置详解",
        "search_engine": "Bing",
        "date": "2025-09-18"
    }
    web_chunk = WebChunk(**web_chunk_json)
    web_chunks.append(web_chunk)
    materials_dict["web"] = web_chunks

    book_chunks_json = [
        {
            "chunk_id": "f49b4015-9d25-4a4a-a7a4-1ea868b20c47",
            "temp_id": None,
            "content": "(1) 读者与图书的关系类型。读者与图书之间建立了借还关系，需求分析的结果显示，“每名读者最多可以借阅10本图书”，因此一本图书可以被多个读者借阅，同时每位读者也能借阅多本图书，所以读者与图书之间的借还关系为多对多（n：m），这里空（1）和空（2）应分别填写n和m。\\n\\n(2) 书目与图书之间的关系类型。图书馆针对同一书目的图书可以有多个副本，每本书在系统中都有唯一的图书ID，因此书目与图书之间的关系类型为一对多（1：n），因此空（3）和空（4）应分别填写1和m。\\n\\n(3) 书目与读者之间的关系类型。当某书目的可借图书数量为0时，读者能够进行预约登记；鉴于一名读者可以借阅多种图书，所以书目与读者之间的预约关系类型为多对多（n：m），这里空（5）和空（6）应填写n和m。\\n\\n2) 图书馆管理E-R模型\\n依据需求分析的结果，图书馆管理应包括图书证的注册、注销和挂失管理，因此在图12-13中，管理员和读者实体之间缺少的联系为注册、注销及挂失借书证的管理关系。补充这些缺失的联系后，E-R模型如图12-14所示。\\n{{{{{this is a image}}}}}\\n\\n3) 图书管理逻辑结构设计\\n根据得到的E-R图概念设计，转换为图书管理系统的主要关系模式如下，请完善“借还记录”和“预约登记”关系中的缺失部分。注意：时间格式为“年.月.日 时：分：秒”，请标明读者和书目关系模式的主键，以及图书、借还记录与预约登记关系模式的主键和外键。\\n管理员（工号，姓名，权限）\\n读者（姓名，年龄，工作单位，借书证号，电话，E-mail）\\n书目（ISBN号，书名，作者，出版商，出版年月，册数，工号）\\n图书（图书ID，ISBN号，存放位置，状态，工号）\\n借还记录（（a），借出时间，应还时间，归还时间）\\n预约登记（（b），预约时间，预约期限，图书ID）\\n借书证管理（借书证号，使用状态，开始时间，结束时间，工号）\\n\\n1) 问题分析\\n在读者借书时，图书管理员需登记借书证号、所借图书ID、借出时间和应还时间；在归还书籍时，图书管理员会在相应的借书信息中记录归还时间，因此借还记录关系中的空（a）应填写“借书证号，图书ID”。\\n在读者进行预约登记时，需记录借书证号、所需借阅图书的ISBN号和预约时间等。目前的预约登记关系中已经包含了预约时间、预约期限和图书ID信息，但还需记录预约的读者和书的ISBN号，因此预约登记关系模式中的空（b）应填写“借书证号，ISBN号”。",
            "refine_content": None,
            "keywords": "读者；图书；书目；多对多；一对多；E-R模型；借还记录；预约登记",
            "keywords_embedding": None,
            "summary": "段落描述了图书馆管理系统中读者、图书和书目之间的多对多和一对多关系，以及借还记录和预约登记关系模式的完善。",
            "summary_embedding": None,
            "embedding": None,
            "tags": [],
            "book_id": "22d81563-c0ea-4ee7-a2ed-279b1ee33bfb",
            "publish_date": "2018-11-26",
            "genre": "",
            "language": "zh",
            "subject": "",
            "book_name": "软件设计师教程",
            "chunk_hierarchy_path": "软件设计师教程\n第12章软件系统分析与设计\n12.2数据库分析与设计\n12.2.7案例分析"
        },
        {
            "chunk_id": "d81436fd-118d-4f00-908c-3599bd73fb8a",
            "temp_id": None,
            "content": "从2.4.1节的示例可以看出，在获取用例的过程中，可能会存在三个不同层次的用例，这些用例的范围各不相同。\\n● 概述级用例的范围涵盖整个企业。\\n● 用户目标级用例的范围限定在系统的边界内。\\n● 子功能级用例的范围则聚焦于某个子系统或组件。\\n在2.4.1节中提到的图书馆图书管理系统是由图书管理员使用的，而读者则通过图书管理员进行借书和还书的操作，如图2.19所示。\\n{{{{{this is a image}}}}}\\n图2.19 用例范围\\n在完整的图书馆系统中，系统包含图书管理系统以及图书管理员，读者则作为系统的外部使用者。在这种情况下，系统的范围是整个图书馆。\\n而在图书管理系统中，图书管理员则成为其外部使用者。\\n以上示例表明，用例的范围会影响系统的边界。用例范围的不同意味着系统的边界也有所不同，因此外部执行者可能也会随之改变。例如，如果用例范围被设定为整个图书馆（即企业范围），则外部执行者为读者；相反，如果用例范围局限在图书管理系统（即软件系统），那么相应的外部执行者则是图书管理员。",
            "refine_content": None,
            "keywords": "用例范围；概述级用例；用户目标级用例；子功能级用例；外部执行者",
            "keywords_embedding": None,
            "summary": "用例有不同的层次和范围，包括概述级、用户目标级和子功能级。用例范围影响系统的边界和外部执行者。例如，图书馆系统的范围可以是整个图书馆或仅限于图书管理系统，这决定了外部执行者是读者还是图书管理员。",
            "summary_embedding": None,
            "embedding": None,
            "tags": [],
            "book_id": "712b5ea1-29d7-4ec1-8f2b-e461bf5dac75",
            "publish_date": "2006-06-01",
            "genre": "",
            "language": "zh",
            "subject": "",
            "book_name": "UML基础与应用",
            "chunk_hierarchy_path": "UML基础与应用\n第2章 用例图\n2.4 用例的粒度和范围\n2.4.2 用例的范围"
        },
        {
            "chunk_id": "9dacc2d8-955b-42bc-8b99-11b831821acc",
            "temp_id": None,
            "content": "根据前述系统的目标与功能需求，结合图书馆的业务特性与管理职能，对系统进行了如下分析。\\n1. 借还书业务流程\\n通过对图书馆借还书业务的调研，明确了借还书的业务流程，如图10.2所示。\\n2. 数据流程分析\\n本系统涉及以下外部实体：\\n读者：可以浏览图书馆提供的书籍介绍和查询信息。登录系统后，可以进行续借和预约。同时，读者还能查询自己的资料，包括借书情况、超期书籍以及是否有罚款等。\\n管理员：帮助读者完成借书、还书等功能，并可以查阅系统所提供的所有信息，为管理者的决策与管理控制提供所需的数据。\\n{{{{{this is a image}}}}}\\n图10.2 借还书业务流程图\\n系统管理员：负责用户注册、注销、信息更新及罚款处理等借书前的相关事务。\\n基于上述分析，绘制出图书借阅管理系统的高层数据流程图，如图10.3所示。\\n{{{{{this is a image}}}}}\\n图10.3 图书借阅管理系统高层数据流程图\\n对高层数据流程图进一步细化和分解，得到图书借阅管理系统的数据流程图，如图10.4所示。读者可以通过登录系统自行进行续借和预约，而借书和还书操作需由管理员协助完成。读者的注册过程则涉及读者与系统管理员。为了对中图进行进一步细化，对图中五个处理进行分解，得到了图10.5至图10.9所示的数据流程底图。\\n{{{{{this is a image}}}}}\\n图10.4 图书借阅管理系统中层数据流程图\\n{{{{{this is a image}}}}}\\n图10.5 注册处理数据流程底图\\n{{{{{this is a image}}}}}\\n图10.6 借书处理数据流程底图\\n{{{{{this is a image}}}}}\\n图10.7 预约处理数据流程底图\\n{{{{{this is a image}}}}}\\n图10.8 还书处理数据流程底图\\n{{{{{this is a image}}}}}\\n图10.9 续借处理数据流程底图\\n3. 数据分析\\n数据分析的主要内容是识别实体对象及其数据属性。在本例中有读者、图书管理员及书籍这三类实体对象。读者与书籍之间是一对多的关系：每位读者可以借阅多本书籍，而每本书籍仅能借给一位读者。\\n数据属性即在数据实体与数据之间关系所具有的特征值。例如，读者的属性包括：借书卡编号、姓名、系别、身份证号、联系电话、密码以及止借标志等；书籍的属性则包括：图书编码、书名、作者、出版社、出版日期、库存量与借阅状态等。归还日期与续借标记则为借阅关系的相关属性。",
            "refine_content": None,
            "keywords": "借还书业务流程；读者；管理员；系统管理员；数据流程图；实体对象；数据属性",
            "keywords_embedding": None,
            "summary": "描述了图书借阅管理系统的业务流程、角色定义及数据属性，明确了借还书流程，定义了读者、管理员和系统管理员的角色及其功能，指出了实体对象及其属性。",
            "summary_embedding": None,
            "embedding": None,
            "tags": [],
            "book_id": "673cc0de-8ab3-4520-9db3-7e24e9ddf734",
            "publish_date": "",
            "genre": "",
            "language": "zh-hans",
            "subject": "",
            "book_name": "管理信息系统（第3版）",
            "chunk_hierarchy_path": "管理信息系统（第3版）\n第10章管理信息系统开发实例\n10.1 图书借阅管理系统的分析与设计\n10.1.3 系统分析"
        }
    ]
    materials_dict['book'] = []
    for book in book_chunks_json:
        materials_dict['book'].append(BookChunk(**book))

    thesis_json = {
        "chunk_id": "45ed63df-9579-4a7d-b9bf-4d322dc2b3df",
        "temp_id": None,
        "content": "系统角色分析可通过用例图来说明，展示本平台涉及的角色和用户功能。在本文实现的凸轮轴瑕疵检测系统中，主要有两类角色：管理员和员工。他们可以使用平台进行实时查看、历史查询以及编辑等操作。管理员负责统一管理员工的账号。本文实现的凸轮轴用例图如图4.8和图4.9所示。\\n\\n工程模块包括编辑模块、数据模块、设置模块和视图模块。\\n\\n图4.9展示了员工的用例，包括用户添加、用户删除和用户信息修改。\\n\\n图4.10则展示了管理员的用例，包括用户添加、用户删除和用户信息修改。由于平台的主要使用者是后厨管理者，因此管理员共有这三个用例。",
        "refine_content": None,
        "keywords": "系统角色；管理员；员工；用例图；编辑模块；数据模块；设置模块；视图模块",
        "keywords_embedding": None,
        "summary": "凸轮轴瑕疵检测系统中有管理员和员工两类角色，各自具有不同的用例和功能。系统包括编辑、数据、设置和视图四个模块。",
        "summary_embedding": None,
        "embedding": None,
        "tags": [],
        "thesis_id": "f52ca791-553e-4daf-ac97-9a106ddfa166",
        "subject": "软件工程",
        "title": "基于深度学习的凸轮轴图像瑕疵识别方法研究及软件开发",
        "title_embedding": None,
        "author": "叶孟财",
        "year": "2023",
        "college": "浙江师范大学",
        "thesis_keywords": "凸轮轴；瑕疵检测；凸轮轴检测系统；机器学习；计算机视觉",
        "chunk_hierarchy_path": "基于深度学习的凸轮轴图像瑕疵识别方法研究及软件开发\n第4章 基于深度学习的凸轮轴图像瑕疵识别软件研发\n4.6 需求分析\n4.6.2 系统角色分析",
        "chunk_hierarchy_path_embedding": None
    }
    materials_dict['thesis'] = [
        ThesisChunk(**thesis_json)
    ]

    return materials_dict
