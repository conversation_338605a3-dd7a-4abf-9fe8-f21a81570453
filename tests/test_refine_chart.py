import json

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.chart.refine_chart_agent import RefineChartAgentInput
from thesis_writing.agent.gen_plan_addition_agent import GeneratePlanAdditionAgentResponse
from thesis_writing.entity.enums import AgentType


def test_refine_chart():
    agent_options = TestUtil.get_aliyun_model_options_from_env("qwen-max-latest")
    agent_options.temperature = 0.7
    agent = AgentFactory.create_agent(AgentType.REFINE_CHART, agent_options)
    plan = "[{\"node_id\": \"1\", \"title\": \"1 绪论\", \"content_type\": \"绪论\", \"length\": \"3000\", \"children\": [{\"node_id\": \"1.1\", \"title\": \"1.1 研究背景\", \"description\": \"阐述供应链金融在中小企业融资中的重要作用，分析传统供应链金融模式中存在的信息不对称、信任缺失和效率低下的问题。结合区块链技术的特性，提出其在供应链金融中的应用潜力，引出研究主题。\", \"length\": \"800\", \"children\": []}, {\"node_id\": \"1.2\", \"title\": \"1.2 研究意义\", \"description\": \"从理论和实践两个层面分析研究的重要性，强调区块链技术在供应链金融中的创新价值，以及对金融生态优化的推动作用。\", \"length\": \"700\", \"children\": []}, {\"node_id\": \"1.3\", \"title\": \"1.3 研究方法\", \"description\": \"介绍论文采用的研究方法，包括文献分析法、案例研究法和模型设计法，说明数据来源和研究工具的选择。\", \"length\": \"600\", \"children\": []}, {\"node_id\": \"1.4\", \"title\": \"1.4 论文创新点\", \"description\": \"概述论文的创新之处，包括区块链技术在供应链金融中的具体应用场景分析，以及对技术挑战的深入探讨。\", \"length\": \"900\", \"children\": []}]}, {\"node_id\": \"2\", \"title\": \"2 文献综述\", \"content_type\": \"正文章节\", \"length\": \"3500\", \"children\": [{\"node_id\": \"2.1\", \"title\": \"2.1 国内外研究现状\", \"description\": \"综述国内外关于区块链技术与供应链金融结合的研究进展，分析现有研究的侧重点和不足之处，为本文提供理论支撑。\", \"length\": \"2500\", \"children\": []}, {\"node_id\": \"2.2\", \"title\": \"2.2 研究空白\", \"description\": \"明确本文的研究切入点和创新点，指出当前研究中尚未充分探讨的问题，如技术挑战和解决方案。\", \"length\": \"1000\", \"children\": []}]}, {\"node_id\": \"3\", \"title\": \"3 理论基础\", \"content_type\": \"正文章节\", \"length\": \"4000\", \"children\": [{\"node_id\": \"3.1\", \"title\": \"3.1 区块链技术核心特征\", \"description\": \"阐述区块链技术的去中心化、不可篡改性和智能合约等特性，分析其在供应链金融中的适用性。\", \"length\": \"1500\", \"children\": []}, {\"node_id\": \"3.2\", \"title\": \"3.2 供应链金融运作流程\", \"description\": \"分析供应链金融的基本运作模式和关键环节，明确传统模式中的痛点。\", \"length\": \"1000\", \"children\": []}, {\"node_id\": \"3.3\", \"title\": \"3.3 区块链技术在供应链金融中的应用\", \"description\": \"探讨区块链技术如何通过提升信息透明度、降低交易成本和增强信任机制来优化供应链金融生态。\", \"length\": \"1500\", \"children\": []}]}, {\"node_id\": \"4\", \"title\": \"4 核心章节\", \"content_type\": \"正文章节\", \"length\": \"8500\", \"children\": [{\"node_id\": \"4.1\", \"title\": \"4.1 案例分析\", \"description\": \"通过实际案例展示区块链技术在供应链金融中的应用效果，如基于智能合约的自动支付系统和分布式账本的信息共享平台。\", \"length\": \"3000\", \"children\": []}, {\"node_id\": \"4.2\", \"title\": \"4.2 模型设计\", \"description\": \"设计基于区块链技术的供应链金融模型，验证其在提高融资效率、减少欺诈风险和优化资金流转方面的成效。\", \"length\": \"3000\", \"children\": []}, {\"node_id\": \"4.3\", \"title\": \"4.3 技术挑战分析\", \"description\": \"深入探讨区块链技术在供应链金融应用中面临的挑战，包括技术成熟度不足、标准化程度低、隐私保护问题以及高昂的部署成本。\", \"length\": \"2500\", \"children\": []}]}, {\"node_id\": \"5\", \"title\": \"5 结论\", \"content_type\": \"结论\", \"length\": \"1000\", \"children\": [{\"node_id\": \"5.1\", \"title\": \"5.1 研究总结\", \"description\": \"概括论文的主要发现和结论，重申区块链技术在供应链金融中的应用价值。\", \"length\": \"500\", \"children\": []}, {\"node_id\": \"5.2\", \"title\": \"5.2 未来展望\", \"description\": \"展望区块链技术在未来供应链金融中的发展趋势，强调其在推动金融创新与产业升级中的重要作用。\", \"length\": \"500\", \"children\": []}]}]"

    agent_input = RefineChartAgentInput(
        subject="基于纳米结构的锂离子电池正极材料性能优化",
        major="储能材料工程技术储能材料工程技术",
        keywords="纳米结构,锂离子电池正极材料,电化学性能优化,表面改性技术,NCA材料",
        summary="xxx",
        writing_plan=plan,
        current_plan_title="3.3 综合分析",
        chart_type="表格",
        chart_title="性能对比表"
    )
    result = agent.invoke(agent_input)
    print(json.dumps(result.model_dump(exclude_none=True), indent=2, ensure_ascii=False))

def test_plan_addition():
    plan_addition = "{\"additions\": \"{\\\"step1\\\":\\\"根据用户的修改，更改图表计划\\\",\\\"step2\\\":\\\"根据用户的修改，更改图表计划\\\",\\\"additions\\\":[{\\\"node_id\\\":\\\"2.1\\\",\\\"first_level_type\\\":\\\"表格\\\",\\\"type\\\":\\\"表格\\\",\\\"id\\\":\\\"表2-1\\\",\\\"title\\\":\\\"实践教学体系现状表\\\",\\\"description\\\":\\\"表格展示北京邮电大学网络工程专业的实践课程设置、实践资源分配、校企合作现状等信息。行表示不同的实践教学要素（如课程、资源、合作项目等），列表示具体内容（如课程名称、资源类型、合作企业等）。\\\",\\\"purpose\\\":\\\"\\\",\\\"retrieve_url\\\":\\\"https://www.wtbu.edu.cn/jwb/2017/1225/c2875a11953/page.htm\\\",\\\"retrieve_title\\\":\\\"关于应用型人才实践教学体系构建的实施意见 - WTBU 关于应用型人才实践教学体系构建的实施意见 - WTBU 关于应用型人才实践教学体系构建的实施意见 - WTBU 关于应用型人才实践教学体系构建的实施意见 - WTBU\\\",\\\"retrieve_content\\\":\\\"4.多种课堂形式联动\\\\n除实验、实习实训、课程设计、课程论文、专业综合能力实践(含毕业论文、毕业设计)等实践性教学环节外，还应加强学生的“第二课堂”联动，同时，将校企合作育人、创新创业训练与教育纳入其中，把实践教学内容贯穿于“多课堂”。\\\\n5.制订各阶段实践性教学总体计划与要求\\\\n形成与理论教学相互配合，以基本层次为基础，综合层次为重点，注重综合应用和创新能力的培养，开放式管理，形成开放式、多样化、四年不断线的实践教学体系（见下图）。实践教学计划与大纲要明确各阶段学生做什么、怎样做、完成什么、达到什么标准等制定具体量化的内容。\\\\n实践教学体系结构图\\\\n**三、实践教学管理体系**\\\\n1.组织管理\\\\n由教务部对实践教学进行宏观管理，制定相应的管理办法和措施。各学院具体负责实践教学的组织与实施工作。\\\\n2.运行管理\\\\n各专业要制定独立、完整的实践教学计划，并根据实践教学计划和人才培养方案编制实践课程标准，编写实践教学指导书，规范实践教学的考核办法，保证实践教学的质量。结合行业的实际特点与企业的实际需求，安排毕业实习、企业定岗实习、毕业设计(论文)等环节。 专业管理委员会成员由学院领导、骨干教师、校外实践基地领导、企业相关人员组成，主要任务是根据我校的定位及专业的人才培养方案，制定各专业的实践教学体系。各系可设主管实践教学的副主任，与教务部、实验教学中心、学院、实验指导教师和实验室管理员构成完整的实践教学组织体系。教务部、实验教学中心负责实践教学的宏观管理、实验室、实训室的管理、实践教学任务的落实、检查与考核；各系负责实践课程标准、实践指导书的编写，校外实践基地的建设与管理。 各专业在制定实践课程标准时注意各课程内容的优化配合，避免重复或脱节；增加实训和设计性、综合性实验的比重，使实践课真正发挥培养学生动手能力和创造能力的作用。倡导每一个实践教学环节均配套实践指导书。  \\\\n**二、实践教学内容体系**  \\\\n实践教学的内容是实践教学目标任务的具体化，将实践教学环节(实验、实习、实训、课程设计、社会实践、毕业设计、科技竞赛、创新创业等)通过合理配置，构建成以培养学生“实践能力和就业能力”为目标，按“基础层次、综合层次、创新层次”三个层次，循序渐进地开放式安排实践教学内容，将实践教学的目标和任务具体落实到各个实践教学环节中，让学生在实践教学中掌握必备的、完整的、系统的技能。  \\\\n1. 打造相对独立的实践教学内容体系  \\\\n要改变过分依附理论教学的状况，探索建立相对独立的实践教学体系，实践教学在教学计划中应占有较大比重，各专业方向实践教学学时不少于总学时学分的30％，保证学生有足够的时间进行实践能力训练。  \\\\n2. 构建“递进开放式”的实践教学层次  \\\\n实践教学内容体系按“基础层次、综合层次、创新层次”三大层次构建。基础层次侧重操作性，专业技能注重技术应用性，综合层次强调综合实践性，注重“产品”与“项目”教学，创新层次注重创新制作和新技术应用，侧重于创新创业能力的培养。  \\\\n3. 搭建符合我校实际需要的实践教学“五大平台”  \\\\n构建合理的符合我校实际需要的实践教学平台。做好“五个平台”建设工作，即基础实践平台、校内实验实训平台、校外实习实训平台、校内大学生创新创业平台、校企合作协同创新育人平台。  \\\\n基础实践平台。以军事训练、社会调查、认知实习和基础实验为主要实践内容载体，对学生进行基础实践感知，以及基础实践能力培养和训练。  \\\\n校内实验实训平台。以综合性、设计性实验、开放创新实验、课程设计、项目实训、学科竞赛、第二课堂为主要实践内容载体，使学生从最基础的实验验证，逐步过渡到综合能力的培养和训练。  \\\\n校外实习实训平台。以生产实习、社会实践、专业实习、毕业实习为主要内容载体，使学生贴近基层、贴近岗位，亲身体验，重点在于学生解决工程实际问题能力的培养和动手能力的训练。 对实践性教学环节应做到6个落实：计划落实、大纲落实、指导教师落实、经费落实、场所落实、考核落实。抓好4个环节：准备工作环节、初期安排落实环节、中期开展检查环节和结束阶段的成绩评定及工作总结环节。  \\\\n制度管理  \\\\n制定一系列关于实验(实训)、实习、毕业论文(设计)和学科竞赛等方面的实践教学管理文件，以保障实践教学环节的顺利开展。  \\\\n实践教学文件和管理制度包括实践教学计划，实践教学课程大纲和教材，实践指导书、实验报告等实践教学文件和各实践教学环节管理制度。  \\\\n**四、实践教学保障体系**  \\\\n实践教学保障体系主要包含：以具有一定生产、管理经验的“双师型”教师为主体的师资队伍；较完备、先进的设备设施，仿真的实践教学环境；实践教学经费保证以及具有实践教学特色的环境四个重要方面。  \\\\n1.师资队伍建设  \\\\n学校在制定具体的师资队伍建设规划时，应重点加强对现有教师的培训方向和培养方法的研究，建立符合应用性本科教育特点的师资进修和企业实践制度，建立具有“教师资格证书”与“职业技能证书”的教师“双资格证书”制度；用政策引导和鼓励教师深入到行业企业一线熟悉生产，参与科研和技术开发；吸引社会实践经验丰富的专家、工程技术人员作为各专业的企业导师；造就一支高水平的“双师型”师资队伍。  \\\\n2.实习实训基地建设  \\\\n各学院在现有实验实训室的基础上，根据教学计划和人才培养方案，制定出实验实训室建设规划，保证各专业必修实践课100％的开出率。制定实验实训室建设规划时要统筹规划，优化配置。  \\\\n（1）实验实训室建设注重仿真性、先进性和完整性，尽可能仿真或模拟职业环境，特别是专业实验实训室能够直接接触生产一线已经成熟且广泛使用的技术，模拟岗位环境，使教学环境和条件接近生产一线。  \\\\n（2）加强校外实践教学基地的建设，基本目标为每学年每个专业的学生要到校外实习实践至少一次，每个专业基本配置为3-5个实习实践基地，各学院根据此目标结合专业特点和学生规模，制订校外实习实践教学基地建设规划。  \\\\n（3）建立校外实践教学基地由各学院根据各个专业实践教学的需要选择，既能满足实践教学需要又交通相对便利的单位，建立校外实践教学基地。使校内实训基地、校外实习实践基地互为补充，实现功能最优化。\\\"},{\\\"node_id\\\":\\\"2.2\\\",\\\"first_level_type\\\":\\\"数据图\\\",\\\"type\\\":\\\"折线图\\\",\\\"id\\\":\\\"图2-1\\\",\\\"title\\\":\\\"高校网络工程专业实践教学体系现状折线图\\\",\\\"description\\\":\\\"高校网络工程专业实践教学体系现状折线图。X轴表示时间，Y轴表示实践课程设置、实践资源分配、校企合作程度等方面的变化情况。折线图将展示自2010年以来，高校网络工程专业实践教学体系在这三个方面的发展趋势，包括课程设置的增加、资源分配的优化以及校企合作程度的提升。通过折线图，可以清晰地看出各项指标的变化趋势和阶段性成果。\\\",\\\"purpose\\\":\\\"\\\",\\\"retrieve_url\\\":\\\"无\\\",\\\"retrieve_title\\\":\\\"无\\\",\\\"retrieve_content\\\":\\\"无\\\"},{\\\"node_id\\\":\\\"2.2\\\",\\\"first_level_type\\\":\\\"表格\\\",\\\"type\\\":\\\"表格\\\",\\\"id\\\":\\\"表2-2\\\",\\\"title\\\":\\\"实践教学主要问题及其严重程度\\\",\\\"description\\\":\\\"表格展示实践教学中存在的主要问题（如理论与实践脱节、资源分配不均、学生动手能力不足等）及其严重程度评分（如低、中、高）。行表示不同问题，列表示问题描述和严重程度。\\\",\\\"purpose\\\":\\\"\\\",\\\"retrieve_url\\\":\\\"https://wenku.baidu.com/view/7e7fdaa9c181e53a580216fc700abb68a982adb2.html\\\",\\\"retrieve_title\\\":\\\"实践教学存在问题及措施(3篇)_百度文库 我国高校实践教学存在的问题、原因及对策 - renrendoc.com 三、我国高校实践性教学中普遍存在的几个问题_挂云帆 我国高校实践教学存在的问题、原因及对策 - renrendoc.com\\\",\\\"retrieve_content\\\":\\\"实践教学过程中，存在诸多问题，如实践资源不足、实践环节设计不合理、实践教学质量不高、实践教学与理论教学脱节等。1. 实践资源不足 （1）实践基地数量有限。许多高校的实践教学基地数量不足，难以满足学生实践需求。部分高校实践教学基地建设滞后，设施设备陈旧，无法满足现代化实践教学要求。 （2）实践经费投入不足。部分高校对实践教学的经费投入不足，导致实践教学资源匮乏，影响了实践教学质量。2. 实践环节设计不合理 （1）实践课程设置与专业培养目标脱节。部分高校实践课程设置不合理，与专业培养目标不符，导致学生实践能力培养不全面。 （2）实践教学内容单一。部分高校实践教学环节设计单一，缺乏创新性和实践性，无法激发学生的学习兴趣。3. 实践教学质量不高 （1）实践教学师资力量不足。部分高校实践教学师资力量薄弱，缺乏实践经验丰富的教师，导致实践教学水平不高。 （2）实践教学评价体系不完善。部分高校实践教学评价体系不完善，难以全面评价学生的实践能力和综合素质。4. 实践教学与理论教学脱节 （1）实践教学与理论教学时间安排不合理。部分高校实践教学与理论教学时间安排不合理，导致实践教学时间不足。 （2）实践教学与理论教学内容脱节。部分高校实践教学与理论教学内容脱节，导致学生难以将理论知识应用于实践。 我国高校实践教学存在的基本问题可以归纳为师资问题、教学条件问题、教学内容与方法问题、管理问题、社会支持问题等方面。 当前我国高校实践性教学中普遍存在一些问题：一是思想观念陈旧。没有按照科学教育发展观的要求及时转变教育观念，重理论轻实践的倾向比较严重，没有充分认识社会需求对人才素质的深刻变化，习惯于传统的教育操作模式，畏惧困难和麻烦，不愿承担风险，对开展实践性教学的重要性紧迫性认识不到位。二是师资队伍不足。当前，我国高校教师队伍多是从事理论教学的，本身就比较缺乏实践教学经验，而担任实践教学任务的教师被安放在教辅系列、处在边缘化地位，导致实践性教学师资队伍严重缺乏。三是经费保障有限。实践性教学需要更多的经费支持和更充分的设施设备保障。我国高校中绝大多数经费短缺，负债过重，运转困难，从而导致实践性教学的硬件条件严重不足。四是人才培养模式老化。没有按照经济全球化和市场化的总趋势及时调整人才培养模式，专业设置、课程建设严重滞后。五是教学方法偏颇。“填鸭式”教学者多，应试教育痕迹突出；学生独立观察、分析、解决的问题少，动手操作能力、综合分析能力训练不足；考试考察要求死记硬背的内容多，实践性内容少。六是制度建设滞后。没有系统的实践性教学大纲，即使有约束力也不强，导致实践性教学随意性强。 有效利用率低，严重影响学生实验技能、综合能力和创新能力培养，还影响学科之间的渗透和科研协作等。能真正起到培养学生实践能力的基地少之又少。实践教学基地缺乏成为制约大学生实践能力的瓶颈之一。实验教学内容陈旧、方法单一、手段落后。单一化、灌输性教学方式，仍然是主要的教学方式。学生缺少主动积极的思考，一旦离开了教师，学生就不知所措。由于缺少规范的实践教学基地，社会实践几近放任自流。高校实践教学管理机制不健全，制度不完善，评价不科学。无法对教师的实践教学能力和学生的实践能力做出公正而又科学的评价，严重抑制了学生开展实践活动和教师从事实践教学的积极性。\\\"},{\\\"node_id\\\":\\\"2.2\\\",\\\"first_level_type\\\":\\\"数据图\\\",\\\"type\\\":\\\"柱状图\\\",\\\"id\\\":\\\"图2-2\\\",\\\"title\\\":\\\"实践教学问题严重程度柱状图\\\",\\\"description\\\":\\\"柱状图展示实践教学中存在的主要问题及其严重程度。X轴表示不同问题，Y轴表示严重程度评分（如低、中、高）。柱状图的高度表示问题的严重程度。\\\",\\\"purpose\\\":\\\"\\\",\\\"retrieve_url\\\":\\\"无\\\",\\\"retrieve_title\\\":\\\"无\\\",\\\"retrieve_content\\\":\\\"无\\\"},{\\\"node_id\\\":\\\"4.1\\\",\\\"first_level_type\\\":\\\"流程图\\\",\\\"type\\\":\\\"流程图\\\",\\\"id\\\":\\\"图4-1\\\",\\\"title\\\":\\\"校企协同育人模式实施流程图\\\",\\\"description\\\":\\\"流程图展示校企协同育人模式的具体实施步骤，包括共同制定实践教学计划、共建实践基地、联合开发课程等。每个步骤用矩形框表示，步骤之间的顺序用箭头表示。\\\",\\\"purpose\\\":\\\"\\\",\\\"retrieve_url\\\":\\\"无\\\",\\\"retrieve_title\\\":\\\"无\\\",\\\"retrieve_content\\\":\\\"无\\\"},{\\\"node_id\\\":\\\"4.3\\\",\\\"first_level_type\\\":\\\"数据图\\\",\\\"type\\\":\\\"柱状图\\\",\\\"id\\\":\\\"图4-2\\\",\\\"title\\\":\\\"虚拟仿真实验平台效果柱状图\\\",\\\"description\\\":\\\"柱状图展示虚拟仿真实验平台在提升学生动手能力和创新能力方面的效果。X轴表示不同的能力维度（如动手能力、创新能力等），Y轴表示提升效果评分（如低、中、高）。柱状图的高度表示提升效果。\\\",\\\"purpose\\\":\\\"\\\",\\\"retrieve_url\\\":\\\"无\\\",\\\"retrieve_title\\\":\\\"无\\\",\\\"retrieve_content\\\":\\\"无\\\"},{\\\"node_id\\\":\\\"5.2\\\",\\\"first_level_type\\\":\\\"数据图\\\",\\\"type\\\":\\\"柱状图\\\",\\\"id\\\":\\\"图5-1\\\",\\\"title\\\":\\\"优化策略预期效果柱状图\\\",\\\"description\\\":\\\"柱状图展示优化策略的预期效果，包括学生实践能力提升、教学效果改善等。X轴表示不同的效果维度（如实践能力、教学效果等），Y轴表示预期效果评分（如低、中、高）。柱状图的高度表示预期效果。\\\",\\\"purpose\\\":\\\"\\\",\\\"retrieve_url\\\":\\\"无\\\",\\\"retrieve_title\\\":\\\"无\\\",\\\"retrieve_content\\\":\\\"无\\\"}]}\"}"
    plan_addition_response = GeneratePlanAdditionAgentResponse.model_validate_json(
                json.loads(plan_addition)['additions'])
    print(plan_addition_response.find_additions_by_node_id("2.1"))