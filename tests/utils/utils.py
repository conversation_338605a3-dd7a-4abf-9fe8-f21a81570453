import hashlib
import json
import os
import random
import string
from pathlib import Path

from dotenv import load_dotenv

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.gen_plan_agent import GeneratePlanAgentResponse

load_dotenv()
current_dir = Path(__file__).resolve().parent


class TestUtil:

    @staticmethod
    def get_qwen_model_options_from_env() -> AgentOptions:
        api_key: str = os.environ["QWEN_MODEL_API_KEY"]
        base_url: str = os.environ["QWEN_MODEL_BASE_URL"]
        model: str = os.environ["QWEN_MODEL_NAME"]
        temperature: float = float(os.environ["QWEN_MODEL_TEMPERATURE"])
        return AgentOptions(api_key=api_key, base_url=base_url, model=model, temperature=temperature)

    @staticmethod
    def get_writing_model_options_from_env() -> AgentOptions:
        api_key: str = os.environ["WRITING_MODEL_API_KEY"]
        base_url: str = os.environ["WRITING_MODEL_BASE_URL"]
        model: str = os.environ["WRITING_MODEL_NAME"]
        temperature: float = float(os.environ["WRITING_MODEL_TEMPERATURE"])
        return AgentOptions(api_key=api_key, base_url=base_url, model=model, temperature=temperature)

    @staticmethod
    def get_aliyun_model_options_from_env(model=None, temperature: float = 0.5) -> AgentOptions:
        api_key: str = os.environ["ALIYUN_MODEL_API_KEY"]
        base_url: str = os.environ["ALIYUN_MODEL_BASE_URL"]
        model: str = model
        temperature: float = temperature
        return AgentOptions(api_key=api_key, base_url=base_url, model=model, temperature=temperature)

    @staticmethod
    def get_qiniu_model_options_from_env(model=None, temperature: float = 0.5) -> AgentOptions:
        api_key: str = os.environ["QINIU_LLM_API_KEY"]
        base_url: str = os.environ["QINIU_LLM_BASE_URL"]
        model: str = model
        temperature: float = temperature
        return AgentOptions(api_key=api_key, base_url=base_url, model=model, temperature=temperature)

    @staticmethod
    def get_o1_model_options_from_env() -> AgentOptions:
        api_key: str = os.environ["O1_MODEL_API_KEY"]
        base_url: str = os.environ["O1_MODEL_BASE_URL"]
        model: str = os.environ["O1_MODEL_NAME"]
        temperature: float = float(os.environ["O1_MODEL_TEMPERATURE"])
        return AgentOptions(api_key=api_key, base_url=base_url, model=model, temperature=temperature)

    @staticmethod
    def get_ds_model_options_from_env(temperature: float = 1.5) -> AgentOptions:
        api_key: str = os.environ["DEEPSEEK_MODEL_API_KEY"]
        base_url: str = os.environ["DEEPSEEK_MODEL_BASE_URL"]
        model: str = os.environ["DEEPSEEK_MODEL_NAME"]
        temperature: float = temperature
        return AgentOptions(api_key=api_key, base_url=base_url, model=model, temperature=temperature)

    @staticmethod
    def get_gemini_model_options_from_env(temperature: float = 1.5) -> AgentOptions:
        api_key: str = os.environ["GEMINI_MODEL_API_KEY"]
        base_url: str = os.environ["GEMINI_MODEL_BASE_URL"]
        model: str = os.environ["GEMINI_MODEL_NAME"]
        temperature: float = temperature
        return AgentOptions(api_key=api_key, base_url=base_url, model=model, temperature=temperature)

    @staticmethod
    def get_test_resource_path(rel_file_path: str) -> str:
        return str(current_dir.parent / 'test_data' / rel_file_path)

    @staticmethod
    def generate_random_string(length):
        characters = string.ascii_letters + string.digits  # 包含字母和数字的字符集
        random_string = ''.join(random.choice(characters) for _ in range(length))
        return random_string

    @staticmethod
    def hash256(text):
        hash_object = hashlib.sha256(text.encode())
        hash_value = hash_object.hexdigest()

        return hash_value

    @classmethod
    def read_head_data(cls, thesis_subject: str):
        file_path = TestUtil.get_test_resource_path(thesis_subject + "_head.json")
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
            subject = data.get("subject")
            major = data.get("major")
            keywords = data.get("keywords")
            final_summary = data.get("final_summary")
            plan = data.get("final_complete_plan")
            writing_plan_response = GeneratePlanAgentResponse.model_validate({"writing_plan_nodes": plan})

            return subject, major, keywords, final_summary, writing_plan_response

    @classmethod
    def save_head_data(cls, thesis_subject: str, result_dict: dict):
        file_path = TestUtil.get_test_resource_path(thesis_subject + "_head.json")
        with open(file_path, "w") as f:
            json.dump(result_dict, f, ensure_ascii=False)

    @classmethod
    def read_body_content(cls, thesis_subject: str):
        file_path = TestUtil.get_test_resource_path(thesis_subject + "_body.json")
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
            introduction = data.get("introduction")
            main_content = data.get("main_content")

            return introduction, main_content

    @classmethod
    def save_main_content(cls, thesis_subject: str, main_content: dict):
        file_path = TestUtil.get_test_resource_path(thesis_subject + "_body.json")
        with open(file_path, "w") as f:
            json.dump(main_content, f, ensure_ascii=False)

    @classmethod
    def save_tail_data(cls, thesis_subject: str, result_dict: dict):
        file_path = TestUtil.get_test_resource_path(thesis_subject + "_tail.json")
        with open(file_path, "w") as f:
            json.dump(result_dict, f, ensure_ascii=False)
