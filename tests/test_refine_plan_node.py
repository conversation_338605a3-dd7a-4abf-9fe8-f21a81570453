import json
import time

from dotenv import load_dotenv

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.gen_plan_agent import GeneratePlanNode
from thesis_writing.agent.refine_plan_node_agent import RefinePlanNodeAgentInput
from thesis_writing.entity.enums import AgentType

load_dotenv()


def test_refine_plan_node():
    summary = "论文在绪论部分阐述**现代粮食工程中智能干燥技术的重要性**，指出随着农业现代化和粮食需求的增长，**传统干燥技术在效率、能耗和品质控制方面的不足**亟待解决。通过分析国内外智能干燥技术的研究现状，明确**本研究的目标**，即通过引入智能化控制策略和技术手段，优化粮食干燥工艺，提高干燥效率，降低能耗，并确保粮食质量。\\n\\n在**理论基础部分**，论文详细探讨**粮食干燥的基本原理**，包括水分迁移规律、传热传质过程以及影响干燥效率的主要因素。结合智能控制技术，分析**人工智能、物联网及大数据技术在干燥工艺中的应用潜力**，为后续研究提供理论支撑。\\n\\n**实验研究部分**设计了一系列实验，验证**智能干燥技术的实际效果**。通过**温度、湿度、风速等关键参数的动态监测**，构建**基于机器学习的预测模型**，优化干燥曲线。实验结果表明，**智能控制策略能够显著降低能源消耗，同时提高干燥均匀性和粮食品质**。此外，论文还研究了**不同类型粮食（如稻谷、玉米、小麦）在智能干燥技术下的表现差异**，提出针对性的干燥工艺优化方案。\\n\\n在**智能控制系统设计部分**，论文详细描述**系统的架构与功能**，包括**传感器网络、数据采集与处理模块、智能算法集成以及人机交互界面**。通过**硬件和软件的协同设计**，实现**对干燥过程的实时监控与动态调整**，确保**系统运行的稳定性和可靠性**。\\n\\n最后，论文总结**研究成果**，指出**智能干燥技术在现代粮食工程中的广泛应用前景**，能够有效提升粮食干燥的效率和品质，降低生产成本。同时，论文展望了**未来研究方向**，如**多模态数据融合、自适应控制策略的进一步优化以及在更大规模粮食干燥场景中的推广应用**。"
    outline = "[{\"node_id\": \"1\", \"title\": \"1 绪论\", \"content_type\": \"绪论\", \"length\": \"2000\", \"children\": [{\"node_id\": \"1.1\", \"title\": \"1.1 引言\", \"description\": \"介绍研究背景，包括粮食需求增长与农业现代化的趋势，强调传统干燥技术在效率、能耗和品质控制上的不足，指出智能干燥技术的重要性和研究意义。\", \"length\": \"500\", \"children\": []}, {\"node_id\": \"1.2\", \"title\": \"1.2 国内外研究现状\", \"description\": \"综述国内外智能干燥技术的发展历程、关键技术及应用情况，分析现有研究的不足之处，明确本研究的创新点。\", \"length\": \"800\", \"children\": []}, {\"node_id\": \"1.3\", \"title\": \"1.3 研究目标、内容及技术路线\", \"description\": \"\", \"length\": \"700\", \"children\": []}]}, {\"node_id\": \"2\", \"title\": \"2 理论基础\", \"content_type\": \"正文章节\", \"length\": \"3000\", \"children\": [{\"node_id\": \"2.1\", \"title\": \"2.1 粮食干燥基本原理\", \"description\": \"阐述粮食干燥过程中水分迁移、传热传质的规律，分析影响干燥效率的主要因素，如温度、湿度、风速等。\", \"length\": \"1000\", \"children\": []}, {\"node_id\": \"2.2\", \"title\": \"2.2 传统干燥技术的局限性\", \"description\": \"分析传统干燥技术在效率、能耗和品质控制方面的不足。\", \"length\": \"800\", \"children\": []}, {\"node_id\": \"2.3\", \"title\": \"2.3 智能控制技术在粮食干燥中的应用\", \"description\": \"探讨人工智能、物联网、大数据等技术在干燥工艺中的应用潜力，为后续研究提供理论支持。\", \"length\": \"1200\", \"children\": []}]}, {\"node_id\": \"3\", \"title\": \"3 实验研究\", \"content_type\": \"正文章节\", \"length\": \"5000\", \"children\": [{\"node_id\": \"3.1\", \"title\": \"3.1 实验设计与方法\", \"description\": \"详细描述实验设计，包括所用材料、设备、方法及数据采集方式，说明关键参数（温度、湿度、风速）的动态监测方法。\", \"length\": \"1200\", \"children\": []}, {\"node_id\": \"3.2\", \"title\": \"3.2 基于机器学习的预测模型构建\", \"description\": \"介绍机器学习模型的选择与构建过程，包括模型训练、验证与优化，说明模型在干燥过程中的应用价值。\", \"length\": \"1200\", \"children\": []}, {\"node_id\": \"3.3\", \"title\": \"3.3 实验结果与分析\", \"description\": \"分析实验结果，包括温度、湿度、风速等参数对干燥效率和粮食质量的影响，验证智能控制策略的优越性。\", \"length\": \"1200\", \"children\": []}, {\"node_id\": \"3.4\", \"title\": \"3.4 不同粮食类型的干燥性能分析\", \"description\": \"研究不同类型粮食（如稻谷、玉米、小麦）在智能干燥技术下的表现差异，提出针对性的干燥工艺优化方案。\", \"length\": \"1400\", \"children\": []}]}, {\"node_id\": \"4\", \"title\": \"4 智能控制系统设计\", \"content_type\": \"正文章节\", \"length\": \"5000\", \"children\": [{\"node_id\": \"4.1\", \"title\": \"4.1 控制系统架构\", \"description\": \"描述智能控制系统的整体架构，包括传感器网络、数据采集与处理模块、智能算法集成、人机交互界面等功能。\", \"length\": \"1200\", \"children\": []}, {\"node_id\": \"4.2\", \"title\": \"4.2 硬件设计\", \"description\": \"详细说明硬件设计，包括传感器的选择与布置、通信模块、执行机构等，确保系统稳定性和可靠性。\", \"length\": \"1200\", \"children\": []}, {\"node_id\": \"4.3\", \"title\": \"4.3 软件设计\", \"description\": \"介绍软件设计，包括数据采集与处理模块、智能算法集成、人机交互界面的实现，说明软件的功能与实现方式。\", \"length\": \"1200\", \"children\": []}, {\"node_id\": \"4.4\", \"title\": \"4.4 系统测试与验证\", \"description\": \"通过实际测试验证系统性能，包括稳定性、可靠性、控制精度等，确保系统满足设计要求。\", \"length\": \"1400\", \"children\": []}]}, {\"node_id\": \"5\", \"title\": \"5 成果分析与讨论\", \"content_type\": \"正文章节\", \"length\": \"3000\", \"children\": [{\"node_id\": \"5.1\", \"title\": \"5.1 研究成果总结\", \"description\": \"总结研究发现，包括智能干燥技术在提高干燥效率、降低能耗、保证粮食质量方面的贡献。\", \"length\": \"800\", \"children\": []}, {\"node_id\": \"5.2\", \"title\": \"5.2 技术经济性分析\", \"description\": \"分析智能干燥技术在成本、效益等方面的经济性，探讨其推广应用的可行性。\", \"length\": \"1000\", \"children\": []}, {\"node_id\": \"5.3\", \"title\": \"5.3 存在的问题与改进建议\", \"description\": \"指出研究中存在的问题，如技术实施难度、成本限制等，提出改进建议。\", \"length\": \"1200\", \"children\": []}]}, {\"node_id\": \"6\", \"title\": \"6 结论与展望\", \"content_type\": \"结论\", \"length\": \"2000\", \"children\": [{\"node_id\": \"6.1\", \"title\": \"6.1 结论\", \"description\": \"总结研究成果，强调智能干燥技术在现代粮食工程中的重要作用。\", \"length\": \"1000\", \"children\": []}, {\"node_id\": \"6.2\", \"title\": \"6.2 展望\", \"description\": \"展望未来研究方向，如多模态数据融合、自适应控制策略的优化以及智能干燥技术在更大规模应用场景中的推广。\", \"length\": \"1000\", \"children\": []}]}]"
    node_list = [GeneratePlanNode(**node) for node in json.loads(outline)]

    refine_title = "1.3 研究目标、内容及技术路线"
    refine_title = "2.2 传统干燥技术的局限性"
    writing_plan = json.dumps(
        [node.model_dump(include={'title', 'description', 'children'}) for node in node_list],
        ensure_ascii=False, indent=1)
    begin_time = time.time()
    agent = AgentFactory.create_agent(AgentType.REFINE_PLAN_NODE, TestUtil.get_qwen_model_options_from_env())
    refine_plan_node_input = RefinePlanNodeAgentInput(
        subject="智能干燥技术在粮食",
        major="粮食工程",
        keywords="干燥技术,智能",
        summary=summary,
        writing_plan=writing_plan,
        refine_title=refine_title
    )
    agent_output = agent.invoke(refine_plan_node_input)
    print("elapsed time:", time.time() - begin_time)
    # then
    print(json.dumps(agent_output.model_dump(exclude_none=True), indent=2, ensure_ascii=False))
