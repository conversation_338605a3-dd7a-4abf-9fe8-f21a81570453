import json

import time
from dotenv import load_dotenv

from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.task_statement.gen_task_statement_agent import GenerateTaskStatementAgentInput
from thesis_writing.entity.enums import AgentType
from .utils.utils import TestUtil

load_dotenv()


def test_task_statement():
    # given
    major = "工商管理"
    subject = "企业转型策略研究"
    keywords = "中小企业, 企业转型, 策略"
    final_summary = "论文在绪论部分介绍研究背景和意义，强调中小企业在经济转型期的重要性及其面临的挑战，指出企业转型是应对市场变化和提升竞争力的关键途径。研究方法包括文献分析、案例研究和战略分析工具的应用。论文结构分为五个章节，依次探讨企业转型的必要性、转型前的内外部环境分析、转型策略的选择与制定、策略实施的保障措施以及研究结论与展望。\n\n 第二章详细分析中小企业在当前经济环境下的发展现状和挑战，包括宏观经济政策、市场竞争态势、客户需求变化和技术进步等因素的影响。通过PEST模型和波特五力模型，评估中小企业在行业中的外部环境，揭示政策支持、市场需求和技术革新的机遇，以及竞争压力和替代品威胁的挑战。这一部分为后续的策略选择奠定基础。\n\n 第三章进行中小企业内部环境分析，运用SWOT模型，全面评估企业的优势、劣势、机会和威胁。论文特别强调中小企业在灵活性、响应速度和创新能力方面的优势，同时指出在资金、技术和管理方面的不足。通过对内外部环境的综合分析，为企业转型提供科学依据。\n\n 第四章提出企业转型的具体策略，包括产品与服务创新、市场拓展、组织结构调整和技术创新。论文详细阐述了每项策略的实施路径和预期效果，例如通过引入新技术和优化产品线，提升企业的竞争力；通过拓展新市场，增加收入来源；通过调整组织结构，提高运营效率。这些策略旨在帮助中小企业实现从传统模式向现代化、高附加值模式的转变。\n\n第五章讨论转型策略的实施保障措施，提出资金保障、人才引进与培养、企业文化建设和风险控制等关键措施。资金保障方面，建议企业通过多渠道融资和成本控制确保转型的资金需求；人才引进与培养方面，建议建立科学的人才引进和培养机制，提高员工的专业技能和综合素质；企业文化建设方面，倡导构建积极向上的企业文化和激励机制，激发员工的积极性和创造力；风险控制方面，建议企业建立健全的风险管理体系，预防和应对转型过程中可能出现的各种风险。\n\n最后，论文在结论部分总结研究的主要发现，指出中小企业通过科学合理的转型策略可以有效应对市场变化，实现可持续发展。同时，论文还对未来的研究方向提出建议，强调对不同行业和地区的中小企业转型进行深入探索，以提供更多实证支持和理论指导。"
    toc = "[{\"title\": \"一、课题简介\"}, {\"title\": \"二、主要任务与目标\"}, {\"title\": \"三、主要内容\"}, {\"title\": \"四、基本要求\"}, {\"title\": \"五、输出成果\"}]"
    # toc = "一、本毕业设计（论文）课题应达到的目的\n二、本毕业设置（论文）课题任务的内容和要求（包括原始数据、技术要求、工作内容要求等）\n三、对本毕业设计（论文）课题成果的要求（包括毕业设计论文、图表、失误样品等）\n四、进度计划安排"

    begin_time = time.time()
    agent = AgentFactory.create_agent(AgentType.GEN_TASK_STATEMENT, TestUtil.get_qwen_model_options_from_env())
    outline = toc
    gen_input = GenerateTaskStatementAgentInput(
        subject=subject,
        major=major,
        toc=outline,
        keywords=keywords,
        summary=final_summary
    )

    # when
    agent_output = agent.invoke(gen_input)
    print("elapsed time:", time.time() - begin_time)

    # then
    print(json.dumps(agent_output.model_dump(exclude_none=True), indent=2, ensure_ascii=False))
