from dotenv import load_dotenv
from langfuse.decorators import observe

from tests.utils.utils import TestUtil
from thesis_writing.agent.adjust_segment_length.abbreviate_segment_length import AbbreviateSegmentLengthAgentInput
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.entity.enums import AgentType

load_dotenv()

content = """
电子文件存储安全性是数字档案馆建设中至关重要的环节，其核心在于通过技术手段和管理措施确保电子文件的完整性和真实性。在数字档案馆中，电子文件存储面临多种安全威胁，包括数据丢失、篡改和非法访问等，这些问题不仅影响档案资源的可用性，还可能对机构信誉和社会信任造成严重损害。因此，构建高效、安全的存储体系是档案信息资源管理的重要任务之一。

电子文件存储安全的核心技术主要包括加密、身份认证和区块链存证等。加密技术通过将数据转化为不可读的密文，确保未经授权的用户无法访问或篡改数据。对称加密和非对称加密因其不同的适用场景，在存储安全中发挥着重要作用。身份认证技术则通过验证用户身份，限制对敏感数据的访问权限，从而降低非法操作的风险。区块链存证技术利用去中心化和不可篡改的特性，为电子文件的存储和流转提供可信保障。中国国家数字档案馆在存储安全实践中，采用了分布式存储与区块链存证相结合的技术方案，不仅解决了海量数据的存储问题，还通过区块链技术确保了电子文件的真实性与完整性[[7202264]]。

在存储方案的选择上，不同技术路径展现出各自的优劣。表4-2系统比较了本地加密存储、云存储和区块链存证等方案的安全性能。本地加密存储在数据完整性和防篡改能力方面表现突出，但访问控制强度相对较弱；云存储具备高可扩展性和灵活性，但在数据安全性和隐私保护方面存在一定风险；区块链存证则在防篡改能力和可信性方面具有显著优势，但其技术复杂性和存储成本较高。通过对比分析可以发现，综合运用多种技术方案，能够更好地平衡安全性和经济性，满足数字档案馆的多样化需求。<table id="表4-2" title="电子文件存储方案安全性能表">| 存储方案       | 数据完整性 | 防篡改能力 | 访问控制强度 | 隐私保护 | 技术成熟度 |
| -------------- | ---------- | ---------- | ------------ | -------- | ---------- |
| 本地加密存储   | ★★★★★      | ★★★★☆      | ★★★☆☆        | ★★★★☆    | ★★★★★      |
| 云存储         | ★★★★☆      | ★★★☆☆      | ★★★★☆        | ★★☆☆☆    | ★★★★☆      |
| 区块链存证     | ★★★★★      | ★★★★★      | ★★★★☆        | ★★★★★    | ★★★☆☆      |</table>

中国国家数字档案馆在存储安全实践中的成功经验，为电子文件管理提供了有益启示。首先，该馆采用了多层次的加密策略，包括对称加密和非对称加密的结合，既保障了数据的安全性，又兼顾了密钥管理的便利性。其次，通过严格的访问控制机制，实现了用户权限的精细化管理，有效降低了非法访问的风险。再次，区块链技术的应用为电子文件的存证和流转提供了可信保障，通过记录每个环节的操作日志，确保了数据的完整性和可追溯性。这些实践表明，技术手段与管理措施的协同作用是提升存储安全性的关键所在[[3488493]]。

尽管数字档案馆在存储安全方面取得了显著进展，但仍面临一些挑战。一方面，随着信息技术的快速发展，传统加密算法的安全性逐渐下降，需要持续更新和优化。另一方面，区块链技术的广泛应用还受限于其高昂的部署成本和复杂的运维要求。未来，可以通过引入人工智能技术，实现存储安全的智能化管理和动态调整，同时加强法规建设，为电子文件存储提供更加完善的法律保障[[695945]]。

综上所述，电子文件存储安全性是数字档案馆建设的重要组成部分，其核心在于通过技术与管理的结合，确保电子文件的完整性和真实性。中国国家数字档案馆的实践经验表明，综合运用多种技术方案，能够有效提升存储安全性。未来，应进一步加强技术创新和法规建设，以应对电子文件管理中的复杂安全挑战，推动档案信息资源管理的持续发展[[1445476]]。

"""


@observe(name="Test Abbreviate Length")
def test_abbreviate_segment_length():
    failover_options_list = [
        TestUtil.get_aliyun_model_options_from_env(model="qwen3-235b-a22b-instruct-2507", temperature=0.7),
        TestUtil.get_ds_model_options_from_env()
    ]

    agent = AgentFactory.create_agent(AgentType.Abbreviate_Segment_Length,
                                      TestUtil.get_aliyun_model_options_from_env(model="qwen3-235b-a22b-instruct-2507",
                                                                                 temperature=0.8),
                                      failover_options_list)
    target_length = int(len(content) * 0.9)
    agent_input = AbbreviateSegmentLengthAgentInput(
        current_content=content,
        current_length=len(content),
        target_length=target_length
    )
    output = agent.invoke(agent_input)

    print(output.model_dump_json(indent=2))
    print(f"原始长度：{len(content)}，目标长度：{target_length}，缩写后长度：{len(output.abbreviated_content)}")
