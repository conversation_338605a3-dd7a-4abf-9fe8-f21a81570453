from dotenv import load_dotenv

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.proposal.determine_modules_need_reference_agent import DetermineModulesNeedReferenceAgentInput
from thesis_writing.entity.enums import AgentType

load_dotenv()


# 创建一个开题报告目录结构作为测试输入
toc = """第一章 绪论
1.1 研究背景
1.2 研究意义
1.3 研究内容
1.4 研究方法
1.5 技术路线

第二章 文献综述
2.1 国内外研究现状
2.2 理论基础
2.3 研究发展现状

第三章 研究方法
3.1 研究设计
3.2 数据收集
3.3 数据分析方法

第四章 实验设计
4.1 实验方案
4.2 实验流程
4.3 实验评估

第五章 预期成果
5.1 理论贡献
5.2 实践意义
5.3 创新点

第六章 研究计划
6.1 时间安排
6.2 人员分工
6.3 资源需求"""


def test():
    agent = AgentFactory.create_agent(AgentType.DETERMINE_MODULES_NEED_REFERENCE,
                                      TestUtil.get_qwen_model_options_from_env())


    determine_modules_input = DetermineModulesNeedReferenceAgentInput(
        toc=toc
    )

    agent_output = agent.invoke(determine_modules_input)

    # 验证输出结果
    assert agent_output.need_reference_modules is not None
    assert isinstance(agent_output.need_reference_modules, list)
    assert len(agent_output.need_reference_modules) > 0

    print(f"需要文献的模块: {agent_output.need_reference_modules}")