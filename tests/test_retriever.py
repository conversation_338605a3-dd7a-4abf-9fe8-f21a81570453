import os

import pytest
from dotenv import load_dotenv

from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.retriever.es_client_factory import es_client_factory
from thesis_writing.retriever.index.base import uuid4
from thesis_writing.retriever.index.user_feed_chunk import UserF<PERSON><PERSON>hunk
from thesis_writing.retriever.reference_retriever import ReferenceRetriever
from thesis_writing.retriever.thesis_chunk_retriever import ThesisChunkRetriever
from thesis_writing.retriever.user_feed_retriever import UserFeedRetriever
from thesis_writing.retriever.web_retriever import WebChunkRetriever
from thesis_writing.retriever.writing_plan_retriever import WritingPlanRetriever

load_dotenv()


def test_plan_retriever():
    es_config = {
        "hosts": os.environ["ELASTICSEARCH_HOSTS"],
        "username": os.environ["ELASTICSEARCH_USERNAME"],
        "password": os.environ["ELASTICSEARCH_PASSWORD"]
    }
    retriever = WritingPlanRetriever(
        embedding_url=os.environ["EMBEDDING_URL"],
        rerank_url=os.environ["RERANK_URL"],
        es_config=es_config)

    major = "工商管理"
    title = "企业转型策略研究"
    keywords = "中小企业"
    result = retriever.search_summary(major, title, keywords, size=5, threshold=0)
    for each in result:
        print(each.title)
    assert len(result) > 0


def test_reference_retriever():
    es_config = {
        "hosts": os.environ["ELASTICSEARCH_HOSTS"],
        "username": os.environ["ELASTICSEARCH_USERNAME"],
        "password": os.environ["ELASTICSEARCH_PASSWORD"]
    }
    retriever = ReferenceRetriever(
        embedding_url=os.environ["EMBEDDING_URL"],
        rerank_url=os.environ["RERANK_URL"],
        es_config=es_config)

    title = "企业转型策略研究"
    result = retriever.search_reference(title, size=5, threshold=0, subject="", lang="")
    for each in result:
        print(each[1])
    assert len(result) > 0


@pytest.mark.asyncio
async def test_thesis_retriever():
    es_config = {
        "hosts": os.environ["ELASTICSEARCH_HOSTS"],
        "username": os.environ["ELASTICSEARCH_USERNAME"],
        "password": os.environ["ELASTICSEARCH_PASSWORD"]
    }
    retriever = ThesisChunkRetriever(
        embedding_url=os.environ["EMBEDDING_URL"],
        rerank_url=os.environ["RERANK_URL"],
        es_config=es_config)

    title = "啊"
    q = "企业转型策略研究"
    result = await retriever.search_thesis_chunk("工商管理", title, "", query=q, size=5, threshold=0.5)
    for each in result:
        print(each.summary)
    assert len(result) > 0


def test_web_retriever():
    es_config = {
        "hosts": os.environ["ELASTICSEARCH_HOSTS"],
        "username": os.environ["ELASTICSEARCH_USERNAME"],
        "password": os.environ["ELASTICSEARCH_PASSWORD"]
    }
    retriever = WebChunkRetriever(
        embedding_url=os.environ["EMBEDDING_URL"],
        rerank_url=os.environ["RERANK_URL"],
        es_config=es_config,
        purge_web_agent_options=AgentOptions(
            base_url=os.environ["ALIYUN_MODEL_BASE_URL"],
            api_key=os.environ["ALIYUN_MODEL_API_KEY"],
            model=os.environ["ALIYUN_MODEL_NAME"],
            temperature=float(os.environ["ALIYUN_MODEL_TEMPERATURE"]),
        ))

    q = "新生代员工 样本选择 方法"
    results = retriever.search_web_chunk(q, session_id="2c528496-a3d8-46cb-ad6c-f0103699caae", size=5, threshold=0.5)
    web_chunks = [result[1] for result in results]
    print(f"get {len(web_chunks)}!")
    for each in web_chunks:
        print(each.content)
        print("-----------------")
    assert len(web_chunks) > 0


@pytest.mark.asyncio
async def test_save_user_feed():
    es_config = {
        "hosts": os.environ["ELASTICSEARCH_HOSTS"],
        "username": os.environ["ELASTICSEARCH_USERNAME"],
        "password": os.environ["ELASTICSEARCH_PASSWORD"]
    }
    retriever = UserFeedRetriever(
        embedding_url=os.environ["EMBEDDING_URL"],
        rerank_url=os.environ["RERANK_URL"],
        es_config=es_config,
        index_name="user_feed_chunk",)

    _es_client = es_client_factory(es_config)

    await retriever.save_user_feed(["内容很长1", "内容很长2"], 33)


@pytest.mark.asyncio
async def test_update_summary():
    es_config = {
        "hosts": os.environ["ELASTICSEARCH_HOSTS"],
        "username": os.environ["ELASTICSEARCH_USERNAME"],
        "password": os.environ["ELASTICSEARCH_PASSWORD"]
    }
    retriever = UserFeedRetriever(
        embedding_url=os.environ["EMBEDDING_URL"],
        rerank_url=os.environ["RERANK_URL"],
        es_config=es_config,
        index_name="user_feed_chunk",)

    # 创建一个唯一的job_id，用于测试
    test_job_id = 1000
    
    try:
        # 1. 先调用save_user_feed生成待更新的数据
        test_materials = ["这是测试材料1", "这是测试材料2", "这是测试材料3"]
        material_ids = await retriever.save_user_feed(test_materials, test_job_id)
        print(f"已保存 {len(material_ids)} 个材料，ID: {material_ids}")
        
        # 2. 获取这些材料对应的chunks
        chunks_map = await retriever.get_user_feeds(test_job_id, material_ids)
        chunks = []
        for material_id, material_chunks in chunks_map.items():
            chunks.extend(material_chunks)
        print(f"获取到 {len(chunks)} 个chunks")
        
        # 3. 更新这些chunks的摘要和关键词
        for i, chunk in enumerate(chunks):
            chunk.title = f"更新后的title{i}"
            chunk.summary = f"更新后的摘要{i}"
            chunk.keywords = f"更新后的关键词{i}"
        
        # 4. 调用update_summary方法更新这些chunks
        await retriever.update_summary(chunks)
        print(f"已更新 {len(chunks)} 个UserFeedChunk的摘要和关键词")
        
        # 5. 验证更新是否成功
        updated_chunks_map = await retriever.get_user_feeds(test_job_id, material_ids)
        updated_chunks = []
        for material_id, material_chunks in updated_chunks_map.items():
            updated_chunks.extend(material_chunks)
        
        # 验证更新后的chunks是否包含新的摘要和关键词
        for i, chunk in enumerate(updated_chunks):
            if i < len(chunks):  # 只验证我们更新过的chunks
                assert chunk.summary.startswith("更新后的摘要"), f"摘要未更新: {chunk.summary}"
                assert chunk.keywords.startswith("更新后的关键词"), f"关键词未更新: {chunk.keywords}"
        
        print("验证成功：摘要和关键词已正确更新")
        
    finally:
        # 6. 清理测试数据
        deleted_count = await retriever.delete_chunks_by_job_id(test_job_id)
        print(f"测试完成，已删除 {deleted_count} 个测试数据")


@pytest.mark.asyncio
async def test_move():
    es_config = {
        "hosts": os.environ["ELASTICSEARCH_HOSTS"],
        "username": os.environ["ELASTICSEARCH_USERNAME"],
        "password": os.environ["ELASTICSEARCH_PASSWORD"]
    }
    
    # 创建ES客户端
    _es_client = es_client_factory(es_config)
    
    try:
        # 创建UserFeedRetriever实例
        retriever = UserFeedRetriever(
            embedding_url=os.environ["EMBEDDING_URL"],
            rerank_url=os.environ["RERANK_URL"],
            es_config=es_config,
            index_name="user_feed_chunk")
            
        # 首先获取总文档数
        result = await _es_client.count(index="user_feed")
        total_docs = result.get("count", 0)
        print(f"Total documents to process: {total_docs}")
            
        batch_size = 20
        from_pos = 0
        total_processed = 0
        
        while True:
            # 使用from+size分页查询
            result = await _es_client.search(
                index="user_feed",
                body={
                    "query": {"match_all": {}},
                    "from": from_pos,
                    "size": batch_size
                }
            )
            
            hits = result.get("hits", {}).get("hits", [])
            if not hits:
                break  # 没有更多数据了
            
            # 将数据转换为UserFeedChunk格式
            user_feed_chunks = []
            for hit in hits:
                source = hit["_source"]
                chunk = UserFeedChunk(
                    session_id=source.get("session_id", uuid4()),
                    material_id=source.get("material_id", uuid4()),
                    chunk_id=uuid4(),
                    content=source.get("content", ""),
                    keywords=source.get("keywords", ""),
                    summary=source.get("summary", ""),
                    embedding=source.get("content_embedding"),
                    keywords_embedding=None,
                    summary_embedding=None,
                    tags=[]
                )
                user_feed_chunks.append(chunk)
            
            # 保存这一批数据
            if user_feed_chunks:
                await retriever.persist_chunks(user_feed_chunks)
                processed_count = len(user_feed_chunks)
                total_processed += processed_count
                print(f"Processed batch of {processed_count} documents. Total processed: {total_processed}/{total_docs}")
            
            # 更新from位置
            from_pos += batch_size
            
            # 如果这批数据少于batch_size，说明已经处理完所有数据
            if len(hits) < batch_size:
                break
                
        print(f"Successfully moved {total_processed} documents to user_feed_chunk index")
        
    finally:
        if _es_client:
            await _es_client.close()


@pytest.mark.asyncio
async def test_move_job_1427():
    es_config = {
        "hosts": os.environ["ELASTICSEARCH_HOSTS"],
        "username": os.environ["ELASTICSEARCH_USERNAME"],
        "password": os.environ["ELASTICSEARCH_PASSWORD"]
    }
    
    # 创建ES客户端
    _es_client = es_client_factory(es_config)
    
    try:
        # 创建UserFeedRetriever实例
        retriever = UserFeedRetriever(
            embedding_url=os.environ["EMBEDDING_URL"],
            rerank_url=os.environ["RERANK_URL"],
            es_config=es_config,
            index_name="user_feed_chunk")
            
        # 首先获取符合条件的文档数
        result = await _es_client.count(
            index="user_feed",
            body={
                "query": {
                    "term": {
                        "session_id": "job_1427"
                    }
                }
            }
        )
        total_docs = result.get("count", 0)
        print(f"Total documents to process for job_1427: {total_docs}")
            
        batch_size = 20
        from_pos = 0
        total_processed = 0
        
        while True:
            # 使用from+size分页查询
            result = await _es_client.search(
                index="user_feed",
                body={
                    "query": {
                        "term": {
                            "session_id": "job_1427"
                        }
                    },
                    "from": from_pos,
                    "size": batch_size
                }
            )
            
            hits = result.get("hits", {}).get("hits", [])
            if not hits:
                break  # 没有更多数据了
            
            # 将数据转换为UserFeedChunk格式
            user_feed_chunks = []
            for hit in hits:
                source = hit["_source"]
                chunk = UserFeedChunk(
                    session_id=source.get("session_id", uuid4()),
                    material_id=source.get("material_id", uuid4()),
                    chunk_id=uuid4(),
                    content=source.get("content", ""),
                    title="",
                    keywords=source.get("keywords", ""),
                    summary=source.get("summary", ""),
                    embedding=source.get("content_embedding"),
                    keywords_embedding=None,
                    summary_embedding=None,
                    tags=[]
                )
                user_feed_chunks.append(chunk)
            
            # 保存这一批数据
            if user_feed_chunks:
                await retriever.persist_chunks(user_feed_chunks)
                processed_count = len(user_feed_chunks)
                total_processed += processed_count
                print(f"Processed batch of {processed_count} documents. Total processed: {total_processed}/{total_docs}")
            
            # 更新from位置
            from_pos += batch_size
            
            # 如果这批数据少于batch_size，说明已经处理完所有数据
            if len(hits) < batch_size:
                break
                
        print(f"Successfully moved {total_processed} documents to user_feed_chunk index")
        
    finally:
        if _es_client:
            await _es_client.close()

    