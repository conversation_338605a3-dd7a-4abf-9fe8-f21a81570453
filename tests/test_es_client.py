import os

import pytest
import time
from dotenv import load_dotenv
from elasticsearch import Elasticsearch

from thesis_writing.retriever.es_client_factory import es_client_factory

load_dotenv()

sleep_seconds = 1  # time to wait for index upgraded, depends on the hardware


@pytest.fixture(scope='session')
def client() -> Elasticsearch:
    # ssh -L 9200:***************:9200 jump-server
    return es_client_factory({
        "hosts": os.environ["ELASTICSEARCH_HOSTS"],
        "username": os.environ["ELASTICSEARCH_USERNAME"],
        "password": os.environ["ELASTICSEARCH_PASSWORD"]
    })


@pytest.fixture(scope='session')
def index_name() -> str:
    return "test-index"


@pytest.fixture(scope='session', autouse=True)
def init_before_all_tests(client: Elasticsearch, index_name: str):
    if client.indices.exists(index=index_name):
        _ = client.indices.delete(index=index_name)
    _ = client.indices.create(index=index_name, body={
        "mappings": {
            "properties": {
                "name": {
                    "type": "text",
                    "index": True
                },
                "content": {
                    "type": "text",
                    "index": True
                },
                "page_count": {
                    "type": "integer",
                    "index": True
                },
                "embedding": {
                    "type": "dense_vector",
                    "dims": 3,
                    "similarity": "cosine"
                }
            }
        }
    })

    _ = client.index(index=index_name, id="test_0", document={
        "name": "name_0",
        "content": "This is a story",
        "page_count": 1,
        "embedding": [1, 2, 3]
    })

    _ = client.index(index=index_name, id="test_1", document={
        "name": "name_1",
        "content": "This is a document",
        "page_count": 2,
        "embedding": [-1, -2, -3]
    })
    time.sleep(sleep_seconds)
    yield
    _ = client.indices.delete(index=index_name)


def test_es_client(client: Elasticsearch, index_name: str):
    result = client.search(index=index_name, body={
        "query": {
            "match": {
                "content": "story"
            }
        }
    })
    assert result["hits"]["hits"][0]["_source"]["name"] == "name_0"

    dsl = {
        "query": {
            "script_score": {
                "query": {
                    "match_all": {}
                },
                "script": {
                    "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                    "params": {
                        "query_vector": [-0.1, 0.2, -0.3]
                    }
                }
            }
        }
    }

    result = client.search(index=index_name, body=dsl)
    assert result["hits"]["hits"][0]["_source"]["name"] == "name_1"

    _ = client.delete(index=index_name, id="test_1")
    time.sleep(sleep_seconds)
    result = client.search(index=index_name, body=dsl)
    assert result["hits"]["hits"][0]["_source"]["name"] == "name_0"
