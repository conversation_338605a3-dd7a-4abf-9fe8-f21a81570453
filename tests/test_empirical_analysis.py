import asyncio

from dotenv import load_dotenv
from langfuse.decorators import observe

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.global_data.empirical_data_agent import *
from thesis_writing.agent.global_data.global_data_agent import AnalysisCommonGlobalDataQueryAgentInput
from thesis_writing.entity.enums import AgentType
from thesis_writing.retriever.retrieve_service import RetrieveService, RetrievalSourceType
import json

load_dotenv()

agent_options = TestUtil.get_qwen_model_options_from_env()
agent_options.temperature = 0.7
agent_options.timeout = 600


def extract_data(query_result, major, subject, keywords, summary, plan, empirical_analysis_plan):
    extracted_query_result = ""
    content = ''
    for each in query_result:
        content += each + '\n'
        if len(content) > 20000:
            input = ExtractEmpiricalAnalysisQueryResultAgentInput(
                subject=subject,
                major=major,
                keywords=keywords,
                summary=summary,
                complete_writing_plan=plan,
                empirical_analysis_plan=empirical_analysis_plan,
                current_date="2022-10-10",
                query_result=content
            )
            agent = ExtractEmpiricalAnalysisQueryResultAgent(agent_options, [])
            output: ExtractEmpiricalAnalysisQueryResultAgentResponse = agent.invoke(input)
            extracted_query_result += output.content + '\n'
            content = ''

    if content != '':
        input = ExtractEmpiricalAnalysisQueryResultAgentInput(
            subject=subject,
            major=major,
            keywords=keywords,
            summary=summary,
            complete_writing_plan=plan,
            empirical_analysis_plan=empirical_analysis_plan,
            current_date="2022-10-10",
            query_result=content
        )
        agent = ExtractEmpiricalAnalysisQueryResultAgent(agent_options, [])
        output: ExtractEmpiricalAnalysisQueryResultAgentResponse = agent.invoke(input)
        extracted_query_result += output.content + '\n'
    return extracted_query_result


@observe(name="test empirical analysis")
def test():
    asyncio.run(do_test())


async def do_test():
    data_path = TestUtil.get_test_resource_path('empirical_analysis/1.json')
    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    major = data['major']
    subject = data['title']
    keywords = data['keywords']
    summary = data['summary']
    plan = json.dumps(data['plan'], indent=2, ensure_ascii=False)

    input = GenEmpiricalAnalysisPlanAgentInput(
        subject=subject,
        major=major,
        keywords=keywords,
        summary=summary,
        complete_writing_plan=plan
    )
    agent = GenEmpiricalAnalysisPlanAgent(agent_options, [])
    output: GenEmpiricalAnalysisPlanAgentResponse = agent.invoke(input)
    empirical_analysis_plan = output.empirical_analysis_plan

    print(output.model_dump_json(indent=2))

    input = GenEmpiricalAnalysisQueryAgentInput(
        subject=subject,
        major=major,
        keywords=keywords,
        research_entities_str="",
        complete_writing_plan=plan,
        empirical_analysis_plan=empirical_analysis_plan
    )
    agent = GenEmpiricalAnalysisQueryAgent(agent_options, [])
    output: GenEmpiricalAnalysisQueryAgentResponse = agent.invoke(input)
    print(output.model_dump_json(indent=2))

    retriever = RetrieveService()

    web_results: List[tuple[str, List]] = await RetrieveService().retrieve_chunks(RetrievalSourceType.Web, output.queries, major, subject, keywords, 0.6)
    paper_results: List[tuple[str, List]] = await RetrieveService().retrieve_chunks(RetrievalSourceType.Paper, output.queries, major, subject, keywords, 0.6)

    query_result = []
    for _, each_list in paper_results or []:
        for chunk in each_list:
            query_result.append(chunk.content)

    thesis_query_result = extract_data(query_result, major, subject, keywords, summary, plan, empirical_analysis_plan)
    print(thesis_query_result)

    query_result = []
    for _, each_list in web_results or []:
        for chunk in each_list:
            query_result.append(chunk.content)
    web_query_result = extract_data(query_result, major, subject, keywords, summary, plan, empirical_analysis_plan)
    print(web_query_result)

    query_result = thesis_query_result + web_query_result

    input = FakeEmpiricalAnalysisDataAgentInput(
        subject=subject,
        major=major,
        keywords=keywords,
        summary=summary,
        complete_writing_plan=plan,
        empirical_analysis_plan=empirical_analysis_plan,
        query_result=query_result
    )
    agent = FakeEmpiricalAnalysisDataAgent(agent_options, [])
    output: FakeEmpiricalAnalysisDataAgentResponse = agent.invoke(input)
    print(output.model_dump_json(indent=2))

@observe(name="TestAnalysisGlobalDataQuery")
def test_analysis_global_data_query():
    subject = "小学教学评价语言的有效性研究综述"
    major = "小学教育"
    keywords = "教学评价语,有效性,小学教育,语言策略,学生发展"
    summary = """
    论文在绪论部分阐述教学评价语在小学教育中的重要性，指出其对学生学习动机、情感发展和学业成就的深远影响，明确研究教学评价语言有效性的必要性。随后，论文对国内外相关文献进行分类整理，首先从评价语言的功能性角度出发，分析其在激励学生、促进课堂互动和反馈教学中的作用。接着，探讨评价语言的有效性影响因素，包括教师的语言能力、评价语的针对性和多样性，以及学生对评价语的理解和接受度。文献还涉及评价语言的设计与优化策略，如基于学生个体差异的个性化评价、非言语性评价的辅助作用，以及评价语的情感化表达。

论文进一步分析当前研究的不足之处，指出现有研究多集中于理论探讨，缺乏针对小学教育实践的实证研究，且对评价语言长期效果的追踪研究较少。最后，论文提出未来研究方向，建议加强对评价语言在不同学科和年级中的适用性研究，探索评价语言与学生心理发展的深层关系，并通过实证研究验证优化策略的实际效果。

结论部分总结研究成果，强调教学评价语言的有效性对小学教育质量提升的重要意义，并呼吁教育实践者和研究者共同努力，推动评价语言在教学中的科学化和精细化应用。
    """
    complete_writing_plan = """
    [
  {
    "node_id": "1",
    "title": "一、引言",
    "description": "阐述教学评价语在小学教育中的重要性，分析其对学生学习动机、情感发展和学业成就的影响，明确研究教学评价语言有效性的必要性。",
    "length": "2500"
  },
  {
    "node_id": "2",
    "title": "一、小学语文教学评价语言研究综述",
    "description": "对国内外相关文献进行分类整理，分析评价语言的功能性作用，探讨其有效性影响因素，涉及设计与优化策略的研究现状。",
    "length": "4000",
    "children": [
      {
        "node_id": "2.1",
        "title": "（一）国内外研究现状",
        "description": "梳理国内外关于小学语文教学评价语言的研究进展，比较不同研究视角和方法，归纳主要成果和特点。",
        "length": "1300"
      },
      {
        "node_id": "2.2",
        "title": "（二）现有研究成果评析",
        "description": "深入分析现有研究成果的贡献与局限，探讨其对小学教育实践的指导意义。",
        "length": "1400"
      },
      {
        "node_id": "2.3",
        "title": "（三）研究趋势与不足",
        "description": "总结当前研究的发展趋势，指出存在的不足之处，如缺乏实证研究和长期效果追踪。",
        "length": "1300"
      }
    ]
  },
  {
    "node_id": "3",
    "title": "二、小学语文教学评价语言现状调查",
    "description": "通过调查设计和结果分析，揭示小学语文教学评价语言的实际使用情况及其对学生的影响。",
    "length": "3500",
    "children": [
      {
        "node_id": "3.1",
        "title": "（一）调查设计",
        "description": "详细介绍调查目的、对象、方法和工具，确保数据收集的科学性和可靠性。",
        "length": "1500"
      },
      {
        "node_id": "3.2",
        "title": "（二）调查结果分析",
        "description": "对调查数据进行统计分析，揭示评价语言使用的现状和存在的问题。",
        "length": "2000"
      }
    ]
  },
  {
    "node_id": "4",
    "title": "三、存在问题及成因分析",
    "description": "分析小学语文教学评价语言中存在的主要问题及其成因，为优化策略提供依据。",
    "length": "3500",
    "children": [
      {
        "node_id": "4.1",
        "title": "（一）主要问题",
        "description": "总结评价语言使用中的典型问题，如针对性不足、情感化表达欠缺等。",
        "length": "1700"
      },
      {
        "node_id": "4.2",
        "title": "（二）成因分析",
        "description": "从教师能力、学生认知特点和学校管理等方面分析问题产生的原因。",
        "length": "1800"
      }
    ]
  },
  {
    "node_id": "5",
    "title": "四、小学语文教学评价语言优化策略",
    "description": "提出优化评价语言的基本原则、具体实施策略和保障机制，推动评价语言的科学化和精细化应用。",
    "length": "5000",
    "children": [
      {
        "node_id": "5.1",
        "title": "（一）基本原则",
        "description": "明确优化评价语言应遵循的原则，如针对性、情感化和多样性。",
        "length": "1200"
      },
      {
        "node_id": "5.2",
        "title": "（二）具体实施策略",
        "description": "提出具体的优化措施，包括个性化评价、非言语性评价和情感化表达等。",
        "length": "2300"
      },
      {
        "node_id": "5.3",
        "title": "（三）保障机制",
        "description": "探讨如何通过教师培训、学校管理和政策支持保障优化策略的实施。",
        "length": "1500"
      }
    ]
  },
  {
    "node_id": "6",
    "title": "五、结论与展望",
    "description": "总结研究成果，强调评价语言有效性的重要性，提出未来研究方向和实践建议。",
    "length": "1500",
    "children": [
      {
        "node_id": "6.1",
        "title": "（一）研究结论",
        "description": "总结论文的主要发现，重申评价语言有效性对小学教育质量提升的意义。",
        "length": "750"
      },
      {
        "node_id": "6.2",
        "title": "（二）未来研究方向",
        "description": "提出未来研究方向，如不同学科和年级的适用性研究，评价语言与学生心理发展的深层关系。",
        "length": "750"
      }
    ]
  }
]

    """
    agent_input = AnalysisCommonGlobalDataQueryAgentInput(
        subject=subject,
        major=major,
        keywords=keywords,
        summary=summary,
        complete_writing_plan=complete_writing_plan
    )
    agent = AgentFactory.create_agent(AgentType.GEN_GLOBAL_DATA_QUERY, agent_options)
    output = agent.invoke(agent_input)
    print(output.model_dump_json(indent=2))


import threading


def test_batch():
    threads = []
    for _ in range(10):
        thread = threading.Thread(target=test_analysis_global_data_query)
        threads.append(thread)
        thread.start()

    for thread in threads:
        thread.join()