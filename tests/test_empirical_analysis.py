import asyncio

from dotenv import load_dotenv
from langfuse.decorators import observe

from tests.utils.utils import TestUtil
from thesis_writing.agent.global_data.empirical_data_agent import *
from thesis_writing.retriever.retrieve_service import RetrieveService, RetrievalSourceType
import json

load_dotenv()

agent_options = TestUtil.get_qwen_model_options_from_env()
agent_options.temperature = 0.7
agent_options.timeout = 600


def extract_data(query_result, major, subject, keywords, summary, plan, empirical_analysis_plan):
    extracted_query_result = ""
    content = ''
    for each in query_result:
        content += each + '\n'
        if len(content) > 20000:
            input = ExtractEmpiricalAnalysisQueryResultAgentInput(
                subject=subject,
                major=major,
                keywords=keywords,
                summary=summary,
                complete_writing_plan=plan,
                empirical_analysis_plan=empirical_analysis_plan,
                current_date="2022-10-10",
                query_result=content
            )
            agent = ExtractEmpiricalAnalysisQueryResultAgent(agent_options, [])
            output: ExtractEmpiricalAnalysisQueryResultAgentResponse = agent.invoke(input)
            extracted_query_result += output.content + '\n'
            content = ''

    if content != '':
        input = ExtractEmpiricalAnalysisQueryResultAgentInput(
            subject=subject,
            major=major,
            keywords=keywords,
            summary=summary,
            complete_writing_plan=plan,
            empirical_analysis_plan=empirical_analysis_plan,
            current_date="2022-10-10",
            query_result=content
        )
        agent = ExtractEmpiricalAnalysisQueryResultAgent(agent_options, [])
        output: ExtractEmpiricalAnalysisQueryResultAgentResponse = agent.invoke(input)
        extracted_query_result += output.content + '\n'
    return extracted_query_result


@observe(name="test empirical analysis")
def test():
    asyncio.run(do_test())


async def do_test():
    data_path = TestUtil.get_test_resource_path('empirical_analysis/1.json')
    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    major = data['major']
    subject = data['title']
    keywords = data['keywords']
    summary = data['summary']
    plan = json.dumps(data['plan'], indent=2, ensure_ascii=False)

    input = GenEmpiricalAnalysisPlanAgentInput(
        subject=subject,
        major=major,
        keywords=keywords,
        summary=summary,
        complete_writing_plan=plan
    )
    agent = GenEmpiricalAnalysisPlanAgent(agent_options, [])
    output: GenEmpiricalAnalysisPlanAgentResponse = agent.invoke(input)
    empirical_analysis_plan = output.empirical_analysis_plan

    print(output.model_dump_json(indent=2))

    input = GenEmpiricalAnalysisQueryAgentInput(
        subject=subject,
        major=major,
        keywords=keywords,
        research_entities_str="",
        complete_writing_plan=plan,
        empirical_analysis_plan=empirical_analysis_plan
    )
    agent = GenEmpiricalAnalysisQueryAgent(agent_options, [])
    output: GenEmpiricalAnalysisQueryAgentResponse = agent.invoke(input)
    print(output.model_dump_json(indent=2))

    retriever = RetrieveService()

    web_results: List[tuple[str, List]] = await RetrieveService().retrieve_chunks(RetrievalSourceType.Web, output.queries, major, subject, keywords, 0.6)
    paper_results: List[tuple[str, List]] = await RetrieveService().retrieve_chunks(RetrievalSourceType.Paper, output.queries, major, subject, keywords, 0.6)

    query_result = []
    for _, each_list in paper_results or []:
        for chunk in each_list:
            query_result.append(chunk.content)

    thesis_query_result = extract_data(query_result, major, subject, keywords, summary, plan, empirical_analysis_plan)
    print(thesis_query_result)

    query_result = []
    for _, each_list in web_results or []:
        for chunk in each_list:
            query_result.append(chunk.content)
    web_query_result = extract_data(query_result, major, subject, keywords, summary, plan, empirical_analysis_plan)
    print(web_query_result)

    query_result = thesis_query_result + web_query_result

    input = FakeEmpiricalAnalysisDataAgentInput(
        subject=subject,
        major=major,
        keywords=keywords,
        summary=summary,
        complete_writing_plan=plan,
        empirical_analysis_plan=empirical_analysis_plan,
        query_result=query_result
    )
    agent = FakeEmpiricalAnalysisDataAgent(agent_options, [])
    output: FakeEmpiricalAnalysisDataAgentResponse = agent.invoke(input)
    print(output.model_dump_json(indent=2))
