import asyncio
from typing import List

import pytest

from thesis_writing.retriever.retrieve_service import RetrieveService, RetrievalSourceType


@pytest.mark.asyncio
async def test_retrieve_web_chunks():
    queries = ['中国古典舞 戏曲 元素 融合 历史阶段 代表性事件', '戏曲艺术 美学特征 程式性 虚拟性 综合性 权威归纳',
               '中国古典舞 身段 手势 眼神 动作 元素 戏曲 来源 术语', '经典中国古典舞作品 戏曲元素 编导 创作年份 院团',
               '中国古典舞 运用京剧 昆曲 表现手法 剧种对照', '现代中国古典舞 创新 戏曲元素 代表性作品 编创理念 近年来',
               '北京舞蹈学院 中国古典舞 教学 戏曲训练 课程设置 内容', '中国古典舞 戏曲 跨界合作 项目案例 合作模式']

    async def retrieve_single(q: str):
        """对单个查询进行搜索"""
        return await RetrieveService().retrieve_web_chunks(q, major="舞蹈学", title="中国古典舞中戏曲元素的融合与传承研究")

    tasks = [retrieve_single(q) for q in queries]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    for q, res in zip(queries, results):
        print(q, res)

@pytest.mark.asyncio
async def test_retrieval_for_segment():
    async def retrieve(source_type: RetrievalSourceType, queries: List[str]):
        return await RetrieveService().retrieve_chunks(source_type, queries, major="软件工程",
                                                       title="2.1用户需求与角色分析", keywords="UML；PlantUML；系统设计")

    result = await asyncio.gather(retrieve(RetrievalSourceType.Web, ["图书管理系统 用户角色 权限设置 功能对照"]))
    print(result)