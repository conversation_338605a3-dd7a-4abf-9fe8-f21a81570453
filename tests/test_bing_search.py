import asyncio
import random
from concurrent.futures import as_completed
from concurrent.futures.thread import ThreadPoolExecutor
import tqdm
from src.thesis_writing.retriever.bing_searcher import BingSearcher2
from thesis_writing.retriever.bing_searcher import <PERSON><PERSON><PERSON>cher


def test_bing_search():
    searcher = BingSearcher()
    results = asyncio.run(searcher.search("Qwen2", 1))

    assert len(results) == 1
    print(results)


def invoke_query(query, size):
    searcher = BingSearcher2()
    results = asyncio.run(searcher.search(query, size))
    assert len(results) == size
    # for result in results:
    #     print(result.title, result.url)

def test_bing_search2():
    queries = ["什么是Qwen2.5", "Qwen2.5是什么", "今天天气怎么样", "Qwen2.5的功能", "Qwen2.5的用途",
               "南京美食", "南京旅游", "南京交通", "南京住宿", "南京景点",
               "北京美食", "北京旅游", "北京交通", "北京住宿", "北京景点",
               "上海美食", "上海旅游", "上海交通", "上海住宿", "上海景点",
               "广州美食", "广州旅游", "广州交通", "广州住宿", "广州景点",
               "天津美食", "天津旅游", "天津交通", "天津住宿", "天津景点"]
    times = 100
    success_count = 0
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(invoke_query, random.choice(queries), 10) for _ in range(times)]
        for future in tqdm.tqdm(as_completed(futures), total=len(futures)):
            try:
                future.result()
                success_count += 1
            except Exception as e:
                print(e)

    assert success_count == times
