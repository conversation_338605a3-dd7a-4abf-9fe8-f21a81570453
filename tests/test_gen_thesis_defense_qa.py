import json

from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.defense.gen_thesis_defense_qa_agent import GenerateThesisDefenseQAAgentInput
from thesis_writing.entity.enums import AgentType
from utils.utils import TestUtil


def test_thesis_defense_QA():
    # given
    subject = "企业转型策略研究"
    main_content, introduction = TestUtil.read_body_content(subject)
    # when
    agent = AgentFactory.create_agent(AgentType.GEN_THESIS_DEFENSE_QA, TestUtil.get_qwen_model_options_from_env())
    gen_input = GenerateThesisDefenseQAAgentInput(
        subject=subject,
        thesis_content=introduction + "\n" + main_content
    )
    agent_output = agent.invoke(gen_input)
    # then
    print(json.dumps(agent_output.model_dump(exclude_none=True), indent=2, ensure_ascii=False))