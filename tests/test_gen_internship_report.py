import json
import time

from langfuse.decorators import observe

from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.internship_report.gen_internship_report import GenerateInternshipReportAgentInput, \
    GenerateInternshipReportQueryAgentInput, GenerateFakeInternshipInfoAgentInput, \
    GenerateInternshipMainContentAgentInput
from thesis_writing.entity.enums import AgentType
from .utils.utils import TestUtil

# given
major = "工程管理"
toc = "[{\"title\": \"实习单位介绍\"}, {\"title\": \"实习目的与要求\"}, {\"title\": \"实习内容\"}, {\"title\": \"实习心得体会\"}, {\"title\": \"实习总结\"}]"


@observe(name="TEST_INTERNSHIP_REPORT")
def test_internship_report():
    begin_time = time.time()
    agent = AgentFactory.create_agent(AgentType.GEN_INTERNSHIP_REPORT,
                                      TestUtil.get_aliyun_model_options_from_env(model="qwen-plus-latest"))
    gen_input = GenerateInternshipReportAgentInput(
        major=major,
        toc=toc
    )

    # when
    agent_output = agent.invoke(gen_input)
    print("elapsed time:", time.time() - begin_time)

    # then
    print(json.dumps(agent_output.model_dump(exclude_none=True), indent=2, ensure_ascii=False))


def test_query():
    begin_time = time.time()
    agent = AgentFactory.create_agent(AgentType.GEN_INTERNSHIP_REPORT_QUERY,
                                      TestUtil.get_aliyun_model_options_from_env(model="qwen3-235b-a22b-instruct-2507"))
    gen_input = GenerateInternshipReportQueryAgentInput(
        career="加油站服务人员",
        toc=toc,
    )

    # when
    agent_output = agent.invoke(gen_input)
    print("elapsed time:", time.time() - begin_time)

    # then
    print(json.dumps(agent_output.model_dump(exclude_none=True), indent=2, ensure_ascii=False))

def test_fake_internship_unit():
    begin_time = time.time()
    agent = AgentFactory.create_agent(AgentType.GEN_FAKE_INTERNSHIP_INFO,
                                      TestUtil.get_ds_model_options_from_env(temperature=1.5))
    gen_input = GenerateFakeInternshipInfoAgentInput(
        major="土木工程",
        internship_unit_hint=""
    )

    # when
    agent_output = agent.invoke(gen_input)
    print("elapsed time:", time.time() - begin_time)

    # then
    print(json.dumps(agent_output.model_dump(exclude_none=True), indent=2, ensure_ascii=False))

@observe(name="TEST_INTERNSHIP_CONTENT")
def test_internship_content():
    completed_content = """
    实习目的与要求
    本次实习旨在将工商企业管理专业的理论知识应用于实际工作场景，深化对组织运营机制的理解，提升数据分析、流程优化与跨部门协调等核心能力；通过参与实习单位日常运营管理事务，掌握企业运作中的关键环节，如KPI监控、流程标准化建设与成本控制辅助，实现理论与实践的深度融合；同时，培养严谨的工作态度、责任意识和团队协作精神，为未来职业发展奠定坚实基础。
    在实习期间，我严格遵守实习单位的各项规章制度，服从工作安排，确保安全生产与信息安全；积极参与分配的各项任务，主动学习岗位所需技能，按时保质完成工作内容；注重与同事之间的沟通协作，尊重不同岗位职责，维护良好的工作关系；坚持实事求是原则，认真记录实习过程与工作成果，确保实习报告内容真实、完整、规范。
    
    实习时间
    本次实习持续时间为三个月，共计约十二周，实习周期安排紧凑，覆盖了企业运营的多个关键阶段，为全面了解和参与实际工作提供了充分的时间保障。
    
    实习心得体会
    通过本次实习，我深刻认识到运营管理不仅是流程的执行，更是企业效率与竞争力的核心支撑，只有通过精细化的数据分析和持续的流程优化，才能实现资源的高效配置；

在实际工作中，我体会到理论知识与实践操作之间的差距，课堂所学的管理模型需要结合企业实际情况灵活调整，例如在流程优化中，不能仅依赖理想化设计，还需考虑人员习惯与系统限制；

我的数据分析能力和办公软件应用水平得到了显著提升，从最初只能进行基础表格操作，到能够独立完成数据清洗、趋势分析与可视化呈现，这一过程增强了我的信息处理能力；

在跨部门沟通中，我学会了如何准确传达需求、倾听对方关切，并在协调中寻找平衡点，这锻炼了我的沟通技巧与服务意识；

同时，我也意识到自身在时间管理与任务优先级判断方面仍有不足，面对多项任务并行时容易陷入细节，今后需进一步提升统筹规划能力；

此次实习让我更加明确未来职业发展方向，我希望继续深耕运营领域，特别是在数据驱动决策与流程自动化方面深入学习，不断提升专业素养，为企业创造更大价值。

总结与展望
本次实习顺利完成，达到了预期目标，我将工商企业管理专业的理论知识有效应用于运营管理实践，在数据处理、流程支持与跨部门协作等方面积累了宝贵经验；

通过参与多项实际工作任务，我不仅掌握了运营工作的基本流程与工具应用，也提升了问题分析与解决能力，增强了对企业整体运作逻辑的理解；

实习过程中，我严格遵守各项要求，积极主动完成工作任务，展现了良好的职业态度与责任感，同时也发现了自身在高效执行与系统思维方面的提升空间；

这段经历让我更加坚定了在运营管理方向发展的信心，未来我将继续加强数据分析、项目管理与系统化思维能力的学习，关注行业前沿趋势，努力成长为具备综合素养的运营管理人才，为企业的可持续发展贡献力量。
    """
    begin_time = time.time()
    agent = AgentFactory.create_agent(AgentType.GEN_INTERNSHIP_CONTENT,
                                      TestUtil.get_ds_model_options_from_env(temperature=1.5))
    gen_input = GenerateInternshipMainContentAgentInput(
        major="工商企业管理",
        career="运营管理实习生",
        completed_content=completed_content,
        toc="""['{\n  "title": "实习内容"\n}']"""
    )

    # when
    agent_output = agent.invoke(gen_input)
    print("elapsed time:", time.time() - begin_time)

    # then
    print(json.dumps(agent_output.model_dump(exclude_none=True), indent=2, ensure_ascii=False))