import json
from collections import defaultdict

from pandas.core.sample import sample

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.questionnaire.gen_question_distributions_agent import GenQuestionDistributionsInput
from thesis_writing.agent.questionnaire.gen_questionnaire_agent import GenerateQuestionnaireAgentResponse, \
    GenerateCustomQuestionnaireAgentInput, GenerateDefaultQuestionnaireAgentInput
from thesis_writing.agent.questionnaire.gen_questionnaire_expected_result_agent import \
    GenQuestionnaireExpectedResultInput
from thesis_writing.agent.questionnaire.gen_questionnaire_report_agent import GenQuestionnaireReportAgentInput, \
    GenQuestionnaireReportAgentResponse
from thesis_writing.entity.enums import AgentType

subject = "大学生新能源汽车购买意愿调研"
expected_results = "调查结果显示，大学生对新能源汽车的关注主要集中在15至20万元的价格区间内，且优先考虑电费和维护成本较低、品牌可信赖、充电便利的新能源汽车型号。环保因素也是影响其购买决策的重要因素。大多数受访者表示倾向于选择电动车而非混合动力车，主要因为电动车在使用成本和环境影响方面更有优势。"
questions_str = """

    "questions": [
        {
            "distributions": [
                0.48,
                0.51,
                0.01
            ],
            "id": "Q1",
            "options": [
                "男",
                "女",
                "其他"
            ],
            "section_type": "background",
            "stem": "您的性别是？",
            "type": "single_choice"
        },
        {
            "distributions": [
                0.85,
                0.08,
                0.035,
                0.025,
                0.01
            ],
            "id": "Q2",
            "options": [
                "18-25岁",
                "26-35岁",
                "36-45岁",
                "46-55岁",
                "56岁以上"
            ],
            "section_type": "background",
            "stem": "您的年龄段是？",
            "type": "single_choice"
        },
        {
            "distributions": [
                0.9,
                0.055,
                0.015,
                0.02,
                0.008,
                0.002
            ],
            "id": "Q3",
            "options": [
                "学生",
                "企业员工",
                "公务员",
                "自由职业者",
                "退休人员",
                "其他"
            ],
            "section_type": "background",
            "stem": "您的职业类型是？",
            "type": "single_choice"
        },
        {
            "distributions": [
                0.2,
                0.79
            ],
            "id": "Q4",
            "options": [
                "是",
                "否"
            ],
            "section_type": "background",
            "stem": "您目前是否拥有汽车？",
            "type": "single_choice"
        },
        {
            "distributions": [
                0.32,
                0.28,
                0.21,
                0.09,
                0.07
            ],
            "id": "Q5",
            "options": [
                "价格",
                "续航里程",
                "充电便利性",
                "品牌口碑",
                "外观设计"
            ],
            "section_type": "core",
            "stem": "如果考虑购买新能源汽车，您最关注的因素是什么？",
            "type": "single_choice"
        },
        {
            "distributions": [
                0.34,
                0.41,
                0.065,
                0.135,
                0.045
            ],
            "id": "Q6",
            "options": [
                "续航不足",
                "充电设施不完善",
                "车型选择有限",
                "维修保养不便",
                "技术不够成熟"
            ],
            "section_type": "core",
            "stem": "您认为当前新能源汽车的主要劣势是什么？",
            "type": "single_choice"
        },
        {
            "distributions": [
                0.52,
                0.43,
                0.029,
                0.041
            ],
            "id": "Q7",
            "options": [
                "纯电动汽车",
                "插电式混合动力汽车",
                "氢燃料电池汽车",
                "传统燃油车"
            ],
            "section_type": "core",
            "stem": "您更倾向于哪种能源类型的汽车？",
            "type": "single_choice"
        },
        {
            "distributions": [
                0.089,
                0.75,
                0.108,
                0.043
            ],
            "id": "Q8",
            "options": [
                "一年内",
                "一到三年内",
                "三到五年内",
                "暂无计划"
            ],
            "section_type": "core",
            "stem": "您计划在未来几年内购买新能源汽车吗？",
            "type": "single_choice"
        },
        {
            "distributions": [
                0.58,
                0.321,
                0.056,
                0.034,
                0.009
            ],
            "id": "Q9",
            "options": [
                "非常关注",
                "比较关注",
                "一般关注",
                "不太关注",
                "完全不关注"
            ],
            "section_type": "core",
            "stem": "您对新能源汽车补贴政策的关注程度如何？",
            "type": "single_choice"
        },
        {
            "distributions": [
                0.095,
                0.189,
                0.405,
                0.229,
                0.082
            ],
            "id": "Q10",
            "options": [
                "非常不合理",
                "不太合理",
                "一般",
                "比较合理",
                "非常合理"
            ],
            "section_type": "core",
            "stem": "您觉得新能源汽车的价格相对于传统燃油车来说是否合理？",
            "type": "single_choice"
        },
        {
            "distributions": [
                0.67,
                0.158,
                0.087,
                0.072,
                0.013
            ],
            "id": "Q11",
            "options": [
                "价格过高",
                "续航里程不足",
                "担心保值率低",
                "基础设施不完善",
                "其他原因"
            ],
            "section_type": "core",
            "stem": "如果您未购买新能源汽车，主要原因是什么？",
            "type": "single_choice"
        },
        {
            "distributions": [
                0.65,
                0.5,
                0.47,
                0.6,
                0.38
            ],
            "id": "Q12",
            "options": [
                "快速充电能力",
                "自动驾驶功能",
                "智能互联体验",
                "高安全性配置",
                "环保材料使用"
            ],
            "section_type": "core",
            "stem": "您希望新能源汽车具备哪些功能或特性？（多选）",
            "type": "multiple_choice"
        },
        {
            "distributions": [
                0.54,
                0.62,
                0.4,
                0.39,
                0.49
            ],
            "id": "Q13",
            "options": [
                "社交媒体",
                "官方网站",
                "线下体验店",
                "朋友推荐",
                "专业评测网站"
            ],
            "section_type": "core",
            "stem": "您会通过哪些渠道获取新能源汽车的相关信息？（多选）",
            "type": "multiple_choice"
        },
        {
            "distributions": [
                0.71,
                0.63,
                0.53,
                0.59,
                0.64
            ],
            "id": "Q14",
            "options": [
                "车辆性能",
                "售后服务",
                "品牌形象",
                "优惠政策",
                "用户评价"
            ],
            "section_type": "core",
            "stem": "您认为哪些因素会影响您最终购买决策？（多选）",
            "type": "multiple_choice"
        },
        {
            "distributions": [
                0.042,
                0.078,
                0.312,
                0.429,
                0.136
            ],
            "id": "Q15",
            "options": [
                "非常没有信心",
                "没有信心",
                "一般有信心",
                "有信心",
                "非常有信心"
            ],
            "section_type": "core",
            "stem": "您对新能源汽车未来发展的信心程度如何？",
            "type": "likert_scale"
        },
        {
            "distributions": [
                0.027,
                0.054,
                0.192,
                0.445,
                0.382
            ],
            "id": "Q16",
            "options": [
                "非常不支持",
                "不支持",
                "中立",
                "支持",
                "非常支持"
            ],
            "section_type": "core",
            "stem": "您对政府推广新能源汽车政策的支持程度如何？",
            "type": "likert_scale"
        },
        {
            "distributions": [
                0.053,
                0.099,
                0.314,
                0.401,
                0.134
            ],
            "id": "Q17",
            "options": [
                "非常不认可",
                "不认可",
                "一般认可",
                "认可",
                "非常认可"
            ],
            "section_type": "core",
            "stem": "您对现有新能源汽车品牌的认可程度如何？",
            "type": "likert_scale"
        },
        {
            "id": "Q18",
            "placeholder": "大多数人希望提升新能源汽车的续航能力和电池技术，同时增强配套充电设施的普及率和实用性。",
            "section_type": "feedback",
            "stem": "您对新能源汽车行业的发展有哪些期望或建议？",
            "type": "open_end"
        },
        {
            "id": "Q19",
            "placeholder": "许多人还提到，降低购车成本和完善售后服务体系对于推广新能源汽车至关重要。",
            "section_type": "feedback",
            "stem": "您是否有其他关于新能源汽车的想法或意见想分享？",
            "type": "open_end"
        }
    ]
"""
qwen_plus_options = TestUtil.get_aliyun_model_options_from_env(model="qwen-plus", temperature=1.5)
deepseek_options = TestUtil.get_ds_model_options_from_env(temperature=1.5)

def test_custom_gen_questionnaire():
    single_num, multiple_num, likert_num, open_end_num = 10, 10, 5, 3

    agent_input = GenerateCustomQuestionnaireAgentInput(basic_info=subject,
                                                        single_choice_nums=single_num,
                                                        multiple_choice_nums=multiple_num,
                                                        likert_scale_nums=likert_num,
                                                        open_end_nums=open_end_num ,
                                                        expected_results=expected_results)
    agent = AgentFactory.create_agent(AgentType.GEN_CUSTOM_QUESTIONNAIRE, deepseek_options)
    output: GenerateQuestionnaireAgentResponse = agent.invoke(agent_input)
    print(output.model_dump()["questions"])
    print(get_questions_preference(output))
    single_nums, multi_nums, likert_nums, open_end_nums = 8, 8, 3, 2
    trimmed_questions = []
    for question in output.questions:
        if question.section_type == 'background':
            trimmed_questions.append(question)
        elif question.section_type == 'core':
            if question.type == 'single_choice' and single_nums:

                trimmed_questions.append(question)
                single_nums -= 1
            elif question.type == 'multiple_choice' and multi_nums:
                trimmed_questions.append(question)
                multi_nums -= 1
            elif question.type == 'likert_scale' and likert_nums:
                trimmed_questions.append(question)
                likert_nums -= 1
        elif question.section_type == 'feedback' and open_end_nums:
            trimmed_questions.append(question)
            open_end_nums -= 1
    output.questions = trimmed_questions
    print(output.model_dump()["questions"])
    print(get_questions_preference(output))

def get_questions_preference(questionnaire: GenerateQuestionnaireAgentResponse):
    """
    获取问卷题目中每种类型的问题数量
    """
    result = defaultdict(int)
    for question in questionnaire.questions:
        if question.section_type != 'background':
            result[question.type] += 1
    return result


def test_default_gen_questionnaire():
    agent_input = GenerateDefaultQuestionnaireAgentInput(basic_info=subject,
                                                         question_nums=18,
                                                         expected_results=expected_results)
    agent = AgentFactory.create_agent(AgentType.GEN_DEFAULT_QUESTIONNAIRE,
                                      TestUtil.get_aliyun_model_options_from_env(model="qwen-max"))
    output: GenerateQuestionnaireAgentResponse = agent.invoke(agent_input)
    print(output.model_dump_json(indent=2))

    print(json.loads(output.model_dump_json(indent=2))['questions'])
    final_output = [q.model_dump() for q in output.questions]
    print(final_output)
    # print(output.model_dump()["questions"])
    # print(len(output.questions))


def test_gen_questionnaire_expected_result():
    subject = "大学生纸质书籍阅读情况"

    agent_input = GenQuestionnaireExpectedResultInput(basic_info=subject)
    agent = AgentFactory.create_agent(AgentType.GEN_QUESTIONNAIRE_EXPECTED_RESULT,
                                      TestUtil.get_ds_model_options_from_env())
    output = agent.invoke(agent_input)
    print(output.model_dump_json(indent=2))


def test_batch():
    for _ in range(10):
        test_custom_gen_questionnaire()
        test_default_gen_questionnaire()


def test_question_distribution():
    question_agent_input = GenQuestionDistributionsInput(title=subject,
                                                         description="",
                                                         expected_results=expected_results,
                                                         samples=200,
                                                         questions=questions_str)
    question_agent = AgentFactory.create_agent(AgentType.GEN_QUESTION_DISTRIBUTIONS,
                                               TestUtil.get_ds_model_options_from_env())
    questions_distributions = question_agent.invoke(question_agent_input)
    print(questions_distributions.model_dump_json(indent=2))


def test_gen_report():
    report_input = GenQuestionnaireReportAgentInput(title=subject,
                                                    description="",
                                                    expected_results=expected_results,
                                                    questions=questions_str,
                                                    samples=200,
                                                    target_audience="大学生")
    report_agent = AgentFactory.create_agent(AgentType.GEN_QUESTIONNAIRE_REPORT,
                                             TestUtil.get_ds_model_options_from_env())
    report: GenQuestionnaireReportAgentResponse = report_agent.invoke(report_input)
    print(report.model_dump_json(indent=2))

def test_gen_expected_result():
    agent_input = GenQuestionnaireExpectedResultInput(basic_info=subject,
                                                      user_input="对于国产车的购买意愿")
    agent = AgentFactory.create_agent(AgentType.GEN_QUESTIONNAIRE_EXPECTED_RESULT,
                                      TestUtil.get_ds_model_options_from_env())
    output = agent.invoke(agent_input)
    print(output.model_dump_json(indent=2))