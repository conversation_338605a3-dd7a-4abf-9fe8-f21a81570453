import json

from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.defense.gen_thesis_defense_ppt_agent import GenerateThesisDefensePPTAgentInput, \
    GenerateThesisDefensePPTAgentResponse
from thesis_writing.entity.enums import AgentType
from utils.utils import TestUtil


def test_thesis_defense():
    # given
    subject = "企业转型策略研究"
    main_content, introduction = TestUtil.read_body_content(subject)
    thesis_content = introduction + "\n" + main_content
    thesis_defense_ppt_plan = [
        "封面",
        "目录",
        "论文的选题背景、意义、价值",
        "研究思路与方法",
        "国内外研究现状",
        "论文框架与内容",
        "论文的创新点或难点",
        "论文研究成果与不足",
        "论文研究未来展望"
        "参考文献",
        "结束语"
    ]

    outline_list = GenerateThesisDefensePPTAgentInput.group_outline(thesis_defense_ppt_plan)
    thesis_defense_ppt = GenerateThesisDefensePPTAgentResponse(analysis=[], contents=[])

    # when
    for outline in outline_list:
        gen_thesis_defense_input = GenerateThesisDefensePPTAgentInput(
            subject=subject,
            thesis_content=thesis_content,
            thesis_defense_ppt=json.dumps(thesis_defense_ppt.model_dump(exclude_none=False), ensure_ascii=False),
            all_outline="\n".join(thesis_defense_ppt_plan),
            outline=outline,
        )
        agent = AgentFactory.create_agent(AgentType.GEN_THESIS_DEFENSE_PPT, TestUtil.get_qwen_model_options_from_env())
        agent_output = agent.invoke(gen_thesis_defense_input)
        thesis_defense_ppt.analysis.extend(agent_output.analysis)
        thesis_defense_ppt.contents.extend(agent_output.contents)

    # then
    print(json.dumps(thesis_defense_ppt.model_dump(exclude_none=True), indent=2, ensure_ascii=False))
