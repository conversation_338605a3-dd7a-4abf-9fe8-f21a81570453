from thesis_writing.parser.web_parser import Web<PERSON><PERSON><PERSON>, web_parser_factory


def test_web_parser_with_proxy():
    _web_parser_proxy_url = "http://t492.kdltps.com:15818"
    _web_parser_proxy_auth = "t13734337041820:vm0obfti"

    parser = web_parser_factory(_web_parser_proxy_url, _web_parser_proxy_auth)
    parser = web_parser_factory(_web_parser_proxy_url, _web_parser_proxy_auth)

   # print(parser.invoke("https://www.pinshiwen.com/yuexie/rwsc/2019050519530.html"))
    print(parser.invoke("https://wenku.baidu.com/view/eeb350b1d938376baf1ffc4ffe4733687f21fc00.html"))


def test_web_parser_without_proxy():
    _web_parser_proxy_url = None
    _web_parser_proxy_auth = None
    parser = web_parser_factory(_web_parser_proxy_url, _web_parser_proxy_auth)
    parser = web_parser_factory(_web_parser_proxy_url, _web_parser_proxy_auth)
    parser = web_parser_factory(_web_parser_proxy_url, _web_parser_proxy_auth)

    print(parser.invoke("https://www.pinshiwen.com/yuexie/rwsc/2019050519530.html"))
    print(parser.invoke("https://www.sohu.com/a/829022075_122066679"))
