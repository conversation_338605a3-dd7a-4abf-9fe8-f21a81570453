from dotenv import load_dotenv
from langfuse.decorators import observe

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.gen_abstraction_agent import GenerateAbstractionAgentInput
from thesis_writing.entity.enums import AgentType

load_dotenv()


@observe(name="test GEN_ABSTRACT_KEYWORDS")
def test():
    agent_options = TestUtil.get_qwen_model_options_from_env()
    agent_options.temperature = 0.8
    agent_options.timeout = 1

    aliyun_options = TestUtil.get_aliyun_model_options_from_env(model="qwen2.5-14b-instruct", temperature=0.7)
    aliyun_options.timeout = 1

    ds_options = TestUtil.get_ds_model_options_from_env()
    ds_options.timeout = 1

    failover_options_list = [
        aliyun_options, ds_options
    ]

    agent = AgentFactory.create_agent(AgentType.GEN_ABSTRACT_KEYWORDS, agent_options, failover_options_list)

    gen_plan_input = GenerateAbstractionAgentInput(
        subject="直播电商行业产品质检的重要性",
        conclusion="直播电商行业产品质检很重要哟",
        introduction=None,
        main_content=None
    )

    agent_output = agent.invoke(gen_plan_input)
    assert agent_output.abstract and agent_output.keywords
