import asyncio
import json
import time

from langfuse.decorators import observe

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.gen_plan_addition_agent import GeneratePlanAdditionAgentInput
from thesis_writing.agent.gen_plan_agent import GeneratePlanAgentInput
from thesis_writing.entity.enums import AgentType


def test_gen_summary():
    asyncio.run(gen_summary())


async def gen_summary():
    # given
    major = "工商管理"
    subject = "企业转型策略研究"
    keywords = "中小企业, 企业转型, 策略"
    final_summary = "论文在绪论部分介绍研究背景和意义，强调中小企业在经济转型期的重要性及其面临的挑战，指出企业转型是应对市场变化和提升竞争力的关键途径。研究方法包括文献分析、案例研究和战略分析工具的应用。论文结构分为五个章节，依次探讨企业转型的必要性、转型前的内外部环境分析、转型策略的选择与制定、策略实施的保障措施以及研究结论与展望。\n\n 第二章详细分析中小企业在当前经济环境下的发展现状和挑战，包括宏观经济政策、市场竞争态势、客户需求变化和技术进步等因素的影响。通过PEST模型和波特五力模型，评估中小企业在行业中的外部环境，揭示政策支持、市场需求和技术革新的机遇，以及竞争压力和替代品威胁的挑战。这一部分为后续的策略选择奠定基础。\n\n 第三章进行中小企业内部环境分析，运用SWOT模型，全面评估企业的优势、劣势、机会和威胁。论文特别强调中小企业在灵活性、响应速度和创新能力方面的优势，同时指出在资金、技术和管理方面的不足。通过对内外部环境的综合分析，为企业转型提供科学依据。\n\n 第四章提出企业转型的具体策略，包括产品与服务创新、市场拓展、组织结构调整和技术创新。论文详细阐述了每项策略的实施路径和预期效果，例如通过引入新技术和优化产品线，提升企业的竞争力；通过拓展新市场，增加收入来源；通过调整组织结构，提高运营效率。这些策略旨在帮助中小企业实现从传统模式向现代化、高附加值模式的转变。\n\n第五章讨论转型策略的实施保障措施，提出资金保障、人才引进与培养、企业文化建设和风险控制等关键措施。资金保障方面，建议企业通过多渠道融资和成本控制确保转型的资金需求；人才引进与培养方面，建议建立科学的人才引进和培养机制，提高员工的专业技能和综合素质；企业文化建设方面，倡导构建积极向上的企业文化和激励机制，激发员工的积极性和创造力；风险控制方面，建议企业建立健全的风险管理体系，预防和应对转型过程中可能出现的各种风险。\n\n最后，论文在结论部分总结研究的主要发现，指出中小企业通过科学合理的转型策略可以有效应对市场变化，实现可持续发展。同时，论文还对未来的研究方向提出建议，强调对不同行业和地区的中小企业转型进行深入探索，以提供更多实证支持和理论指导。"
    education_list = [(1, 8000, 2), (2, 12500, 2), (2, 15000, 3), (3, 30000, 2), (3, 30000, 3)]
    # education_list = [(3, 30000, 3)]
    for education_level, word_count, outline_level in education_list:
        begin_time = time.time()

        agent = AgentFactory.create_agent(AgentType.GEN_PLAN,
                                          TestUtil.get_aliyun_model_options_from_env(model="qwen-plus"),
                                          education_level=education_level)
        gen_plan_input = await GeneratePlanAgentInput.construct_input_with_retrieval(
            major, subject, keywords, final_summary, word_count, outline_level)

        # when
        agent_output = agent.invoke(gen_plan_input)
        agent_output.adjust_plan_writing_length(word_count)
        agent_output.clear_unnecessary_node()
        print("elapsed time:", time.time() - begin_time)

        # then
        assert len(agent_output.writing_plan_nodes) > 0

        print(json.dumps(agent_output.model_dump(exclude_none=True), indent=4, ensure_ascii=False))


subject = "山西小米地理标志品牌传播效果与消费者认知关系研究"
major = "工商管理"
summary = """
论文首先在绪论部分阐述**研究背景和意义**，指出山西小米作为地理标志农产品在品牌化进程中面临传播效果弱、市场认知度低的问题，明确研究目标为揭示品牌传播对消费者认知的影响机制。研究采用文献分析法与实证调查法，构建“传播渠道—信息接触—认知形成”的理论框架。

**文献综述**部分系统梳理农产品地理标志品牌、品牌传播理论与消费者认知模型，界定**品牌传播效果**的多维构成，包括覆盖率、信息一致性与互动性，并明确消费者认知的三个层次：品牌识别、品质感知与信任建立。

第三部分通过内容分析法评估山西小米当前的**品牌传播现状**，发现其在新媒体平台内容同质化严重，传统媒体投放分散，缺乏统一叙事体系。同时设计结构化问卷开展实地调研，回收有效样本用于数据分析。

基于调研数据，论文运用回归分析验证传播渠道强度与消费者认知水平之间的正向关系，结果显示社交媒体传播对品牌识别提升最为显著，而权威认证信息对品质感知影响最大。**感知价值**在传播效果与消费者认知之间起到部分中介作用。

最后，论文提出优化建议：整合传播资源，打造区域公用品牌叙事体系；强化地理标志背书信息输出；针对不同消费群体实施精准传播策略。研究为提升山西小米品牌影响力提供可操作路径，也为同类农产品地理标志品牌建设提供参考依据。
    """
word_count = "8000"


@observe(name="TestShortPlan")
def test_gen_plan():
    agent_input = GeneratePlanAgentInput(
        subject="任务驱动教学法在高中阅读教学中运用的实践研究",
        major="汉语言文学",
        summary=summary,
        word_count=word_count,
        generate_third_level="是",
        introduction_has_subsections=True
    )
    agent = AgentFactory.create_agent(AgentType.GEN_SHORT_PAPER_PLAN, TestUtil.get_ds_model_options_from_env())
    output = agent.invoke(agent_input)
    output.adjust_plan_writing_length(int(word_count))
    print(json.dumps(output.model_dump(exclude_none=True), indent=4, ensure_ascii=False))
    # print(output.get_writing_plan_str())


writing_plan = """
[{"id":"1","title":"第一章 绪论","content_type":"绪论","writing_length":"1200","children":[{"id":"1.1","title":"1.1 研究背景与意义","description":"介绍高中语文阅读教学的现状，分析传统教学模式在激发学生兴趣和培养自主学习能力方面的不足，强调任务驱动教学法的应用价值及其研究意义。","writing_length":700},{"id":"1.2","title":"1.2 研究目标与方法","description":"明确本研究的目标是探讨任务驱动教学法在高中阅读教学中的实践效果，并简要说明采用的研究方法，如课堂观察和问卷调查。","writing_length":500}]},
{"id":"2","title":"第二章 任务驱动教学法的理论基础","content_type":"正文章节","writing_length":"2000","children":[{"id":"2.1","title":"2.1 建构主义学习理论","description":"详细阐述建构主义学习理论的核心观点及其对任务驱动教学法的支撑作用，说明该理论如何促进学生主动构建知识。","writing_length":1000},{"id":"2.2","title":"2.2 多元智能理论","description":"介绍多元智能理论的基本内容，分析其在任务驱动教学法中的应用，强调如何通过多样化的任务设计满足不同学生的智能发展需求。","writing_length":1000}]},
{"id":"3","title":"第三章 任务驱动教学法在高中阅读教学中的实践研究","content_type":"正文章节","writing_length":"4000","children":[{"id":"3.1","title":"3.1 教学设计与实施","writing_length":"2000","children":[{"id":"3.1.1","title":"3.1.1 任务设计","description":"详细描述任务驱动教学法在高中阅读教学中的任务设计过程，包括任务类型、目标及具体步骤，以经典篇目为例说明设计思路。","writing_length":1000},{"id":"3.1.2","title":"3.1.2 课堂实施","description":"阐述任务驱动教学法在课堂中的具体实施过程，包括师生互动、任务推进及课堂氛围，说明如何引导学生主动参与。","writing_length":1000}]},{"id":"3.2","title":"3.2 教学效果与问题分析","writing_length":"2000","children":[{"id":"3.2.1","title":"3.2.1 效果分析","description":"通过课堂观察和问卷调查数据，分析任务驱动教学法对学生阅读兴趣、理解能力和批判性思维的积极影响，展示实践成效。","writing_length":1200},{"id":"3.2.2","title":"3.2.2 问题与反思","description":"指出实践过程中存在的问题，如任务难度设计不当和教师指导不足，并简要反思改进方向。","writing_length":800}]}]},
{"id":"4","title":"第四章 结论","content_type":"结论","writing_length":800,"children":[]}
]
"""


@observe(name="TestShortPlanAddition")
def test_short_plan_addition():
    addition_input = GeneratePlanAdditionAgentInput(
        subject=subject,
        major=major,
        writing_plan=writing_plan,
        global_data_analysis=''
    )
    addition_agent = AgentFactory.create_agent(AgentType.GEN_SHORT_PLAN_ADDITION,
                                               TestUtil.get_ds_model_options_from_env())
    addition_output = addition_agent.invoke(addition_input)
    # print(addition_output)
    print(json.dumps(addition_output.model_dump(exclude_none=True), indent=4, ensure_ascii=False))
