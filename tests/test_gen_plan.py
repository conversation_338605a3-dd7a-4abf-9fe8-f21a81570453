import asyncio
import json
import time

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.gen_plan_agent import GeneratePlanAgentInput

from thesis_writing.entity.enums import AgentType


def test_gen_summary():
    asyncio.run(gen_summary())


async def gen_summary():
    # given
    major = "工商管理"
    subject = "企业转型策略研究"
    keywords = "中小企业, 企业转型, 策略"
    final_summary = "论文在绪论部分介绍研究背景和意义，强调中小企业在经济转型期的重要性及其面临的挑战，指出企业转型是应对市场变化和提升竞争力的关键途径。研究方法包括文献分析、案例研究和战略分析工具的应用。论文结构分为五个章节，依次探讨企业转型的必要性、转型前的内外部环境分析、转型策略的选择与制定、策略实施的保障措施以及研究结论与展望。\n\n 第二章详细分析中小企业在当前经济环境下的发展现状和挑战，包括宏观经济政策、市场竞争态势、客户需求变化和技术进步等因素的影响。通过PEST模型和波特五力模型，评估中小企业在行业中的外部环境，揭示政策支持、市场需求和技术革新的机遇，以及竞争压力和替代品威胁的挑战。这一部分为后续的策略选择奠定基础。\n\n 第三章进行中小企业内部环境分析，运用SWOT模型，全面评估企业的优势、劣势、机会和威胁。论文特别强调中小企业在灵活性、响应速度和创新能力方面的优势，同时指出在资金、技术和管理方面的不足。通过对内外部环境的综合分析，为企业转型提供科学依据。\n\n 第四章提出企业转型的具体策略，包括产品与服务创新、市场拓展、组织结构调整和技术创新。论文详细阐述了每项策略的实施路径和预期效果，例如通过引入新技术和优化产品线，提升企业的竞争力；通过拓展新市场，增加收入来源；通过调整组织结构，提高运营效率。这些策略旨在帮助中小企业实现从传统模式向现代化、高附加值模式的转变。\n\n第五章讨论转型策略的实施保障措施，提出资金保障、人才引进与培养、企业文化建设和风险控制等关键措施。资金保障方面，建议企业通过多渠道融资和成本控制确保转型的资金需求；人才引进与培养方面，建议建立科学的人才引进和培养机制，提高员工的专业技能和综合素质；企业文化建设方面，倡导构建积极向上的企业文化和激励机制，激发员工的积极性和创造力；风险控制方面，建议企业建立健全的风险管理体系，预防和应对转型过程中可能出现的各种风险。\n\n最后，论文在结论部分总结研究的主要发现，指出中小企业通过科学合理的转型策略可以有效应对市场变化，实现可持续发展。同时，论文还对未来的研究方向提出建议，强调对不同行业和地区的中小企业转型进行深入探索，以提供更多实证支持和理论指导。"
    education_list = [(1, 8000, 2), (2, 12500, 2), (2, 15000, 3), (3, 30000, 2), (3, 30000, 3)]
    # education_list = [(3, 30000, 3)]
    for education_level, word_count, outline_level in education_list:
        begin_time = time.time()

        agent = AgentFactory.create_agent(AgentType.GEN_PLAN, TestUtil.get_aliyun_model_options_from_env(model="qwen-plus"),
                                          education_level=education_level)
        gen_plan_input = await GeneratePlanAgentInput.construct_input_with_retrieval(
            major, subject, keywords, final_summary, word_count, outline_level)

        # when
        agent_output = agent.invoke(gen_plan_input)
        agent_output.adjust_plan_writing_length(word_count)
        agent_output.clear_unnecessary_node()
        print("elapsed time:", time.time() - begin_time)

        # then
        assert len(agent_output.writing_plan_nodes) > 0

        print(json.dumps(agent_output.model_dump(exclude_none=True), indent=4, ensure_ascii=False))

def test_gen_plan():
    summary="""
    论文首先从高中语文阅读教学的现状出发，指出传统教学模式在激发学生兴趣、培养自主学习能力方面的不足，强调任务驱动教学法作为一种新型教学模式的应用价值。接着，论文梳理了任务驱动教学法的理论基础，包括建构主义学习理论和多元智能理论，阐明其在促进学生主动参与和深度学习中的优势。

在实践研究部分，论文设计了一套基于任务驱动的教学实施方案，选取高中阅读教学中的经典篇目作为案例，详细描述了任务设计、课堂实施和评价反馈的过程。通过课堂观察和学生问卷调查，论文分析了任务驱动教学法对学生阅读兴趣、理解能力和批判性思维的积极影响，同时指出实践中存在的问题，如任务难度设计不当和教师指导不足。

最后，论文总结了任务驱动教学法在高中阅读教学中的实践成效，并提出优化建议，包括完善任务设计、加强教师培训和构建多元评价体系，以进一步提升教学效果。
    """
    word_count = "12500"
    agent_input = GeneratePlanAgentInput(
        subject="任务驱动教学法在高中阅读教学中运用的实践研究",
        major="汉语言文学",
        summary=summary,
        word_count=word_count,
    )
    agent = AgentFactory.create_agent(AgentType.GEN_PLAN, TestUtil.get_ds_model_options_from_env())
    output = agent.invoke(agent_input)
    output.adjust_plan_writing_length(int(word_count))
    output.clear_unnecessary_node()
    print(json.dumps(output.model_dump(exclude_none=True), indent=4, ensure_ascii=False))
