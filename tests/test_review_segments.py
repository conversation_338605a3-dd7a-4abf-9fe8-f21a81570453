import json
import os
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from typing import <PERSON>ple

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.review_segments_agent import SegmentToReview, ReviewSegmentsAgentInput
from thesis_writing.entity.enums import AgentType


def test_review_paper():
    invoke_llm("test_data/review_paper/1376.txt")


def test_review_paper_multi():
    # given
    folder = "test_data/review_paper/"
    with ThreadPoolExecutor(max_workers=20) as executor:
        futures = []
        for filename in os.listdir(folder):
            f = os.path.join(folder, filename)
            fu = executor.submit(invoke_llm, f)
            futures.append(fu)

        for future in as_completed(futures):
            print(future.result())


def invoke_llm(file_path):
    with open(file_path, "r") as file:
        lines = file.readlines()

    new_lines = []
    empty_line_count = 0
    for line in lines:
        new_lines.append(line)

        if line == "\n":
            empty_line_count += 1
        if empty_line_count == 2:
            break

    paper_dict_arr = json.loads(lines[0])

    main_body_chapter_begin = 0
    main_body_chapter_end = len(paper_dict_arr)
    for chapter_dict in paper_dict_arr:
        if "绪论" in chapter_dict["title"] or "引言" in chapter_dict["title"] or "文献综述" in chapter_dict["title"]:
            main_body_chapter_begin = main_body_chapter_begin + 1
    if "结论" in paper_dict_arr[-1]["title"]:
        main_body_chapter_end = main_body_chapter_end - 1

    review_results = []
    futures = []
    with ThreadPoolExecutor(max_workers=10) as executor:
        chapter_num = main_body_chapter_begin
        for chapter_dict in paper_dict_arr[main_body_chapter_begin:main_body_chapter_end]:
            if "segments" in chapter_dict:
                for segment_dict in chapter_dict["segments"]:
                    fu = executor.submit(review_segment, segment_dict, paper_dict_arr[main_body_chapter_begin:main_body_chapter_end])
                    futures.append(fu)
            else:
                fu = executor.submit(review_segment, chapter_dict, paper_dict_arr[main_body_chapter_begin:main_body_chapter_end])
                futures.append(fu)
            chapter_num += 1

    for future in as_completed(futures):
        review_results.append(future.result())

    new_lines.append("\n\n\n")
    for review_result in review_results:
        new_lines.append(json.dumps(review_result.model_dump(), indent=2, ensure_ascii=False))

    with open(file_path, "w") as f:
        f.writelines(new_lines)

    return file_path


def review_segment(segment_dict, paper_dict):
    agent_input = ReviewSegmentsAgentInput(
        focus_segment=SegmentToReview.model_validate(segment_dict),
        chapter_list=[SegmentToReview.model_validate(chapter) for chapter in paper_dict]
    )
    agent_options = TestUtil.get_aliyun_model_options_from_env("qwen2.5-72b-instruct", 0)
    agent = AgentFactory.create_agent(AgentType.REVIEW_SEGMENTS, agent_options)
    agent_output = agent.invoke(agent_input)

    return agent_output


def test_merge_range():
    folder = "test_data/review_paper/"
    for filename in os.listdir(folder):
        f = os.path.join(folder, filename)
        _do_merge_range(f)


def _do_merge_range(f: str):
    with open(f, "r") as file:
        lines = file.readlines()

    new_lines = []
    content_lines = []
    content_start = False
    content_end = False
    for line in lines:
        if content_end is False:
            new_lines.append(line)

        if line == "{\n":
            content_start = True
            content_lines.append("[{")
        elif line == "}{\n":
            content_lines.append("},{")
        elif line == "}\n" or line == "}":
            content_end = True
            content_lines.append("}]")
        elif content_start is True and content_end is False:
            content_lines.append(line)

    json_arr_str = "".join(content_lines)
    json_arr = json.loads(json_arr_str)

    segment_founds_list = merge_results(json_arr)
    new_lines.append("\n\n")
    new_lines.append(str(segment_founds_list))

    with open(f, "w") as f:
        f.writelines(new_lines)


def merge_results(review_response_list) -> list[set[int]]:
    all_segment_pairs: {Tuple[int, int], int} = {}
    all_segment_founds: list[set] = []
    for segment_review_response in review_response_list:
        segment_founds: set[int] = set()
        for id_pair_str in segment_review_response["results"] or []:
            id_pair = id_pair_str.split(",")
            first_id = int(id_pair[0]) if int(id_pair[0]) < int(id_pair[1]) else int(id_pair[1])
            second_id = int(id_pair[1]) if int(id_pair[0]) < int(id_pair[1]) else int(id_pair[0])

            all_segment_pairs[(first_id, second_id)] = all_segment_pairs.get((first_id, second_id), 0) + 1
            segment_founds.add(first_id)
            segment_founds.add(second_id)

        all_segment_founds.append(segment_founds)

    # 为每个segment_id计数
    segment_id_times = {}
    for segment_founds in all_segment_founds:
        for segment_id in segment_founds:
            segment_id_times[segment_id] = segment_id_times.get(segment_id, 0) + 1

    print(all_segment_founds)

    # 为duplicate_segments_list中每个set算分
    ordered_segment_founds = []
    for segment_founds in all_segment_founds:
        score = 0
        for segment_id in segment_founds:
            score += segment_id_times[segment_id]
        ordered_segment_founds.append((segment_founds, score))
    ordered_segment_founds.sort(key=lambda x: x[1], reverse=True)

    # 按照分数降序，取与其他segment_founds没有交集的segment_founds，或一组有交集的segment_founds中分数最高的那个
    segment_founds_list: list[set[int]] = []
    for segment_founds, score in ordered_segment_founds:
        if len(segment_founds) == 0:
            continue

        if len(segment_founds_list) == 0:
            segment_founds_list.append(segment_founds)
        elif not has_intersection(segment_founds_list, segment_founds):
            segment_founds_list.append(segment_founds)

    print(segment_founds_list)

    # all_segment_pairs排序，按pair出现的次数多少
    all_segment_pairs = sorted(all_segment_pairs.items(), key=lambda x: x[1], reverse=True)

    # 回顾all_segment_pairs，如果某个pair在segment_founds_list中不存在，就把这个pair假如 segment_founds_list中
    for segment_pair, _ in all_segment_pairs:
        found = False
        for segment_founds in segment_founds_list:
            if segment_pair[0] in segment_founds or segment_pair[1] in segment_founds:
                found = True
                break

        if found is False:
            segment_founds_list.append(set(segment_pair))

    print(segment_founds_list)
    return segment_founds_list


def has_intersection(segment_founds_list: list[set[int]], segment_founds: set[int]):
    for segment_founds_set in segment_founds_list:
        if len(segment_founds_set.intersection(segment_founds)) > 0:
            return True

    return False
