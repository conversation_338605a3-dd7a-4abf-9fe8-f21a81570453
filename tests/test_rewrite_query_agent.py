from dotenv import load_dotenv

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.retrieval_analysis.rewrite_query_agent import RewriteQueryAgentInput
from thesis_writing.entity.enums import AgentType

load_dotenv()


def test():
    agent = AgentFactory.create_agent(AgentType.GEN_CHAPTER_QUERY, TestUtil.get_qwen_model_options_from_env())

    gen_plan_input = RewriteQueryAgentInput(
        queries=["直播电商行业产品质检的重要性有哪些理论依据?", "产品质检在直播电商中的应用理论基础是什么？"],
        to_sources=["参考论文", "搜索引擎"]
    )

    agent_output = agent.invoke(gen_plan_input)
    print(f"重写查询：{agent_output}")

    gen_plan_input = RewriteQueryAgentInput(
        queries=["直播电商 产品质量管理 理论模型及实践案例", "播电商 产品质检 国家政策 行业规范"],
        to_sources=["参考论文", "参考书籍"]
    )
    agent_output = agent.invoke(gen_plan_input)
    print(f"重写查询：{agent_output}")

    gen_plan_input = RewriteQueryAgentInput(
        queries=["直播电商行业产品质检的研究现状如何？", "直播电商行业产品质检的现状和常见问题有哪些？"],
        to_sources=["搜索引擎", "参考书籍"]
    )
    agent_output = agent.invoke(gen_plan_input)
    print(f"重写查询：{agent_output}")
