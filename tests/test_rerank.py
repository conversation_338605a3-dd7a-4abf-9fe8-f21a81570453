import os
import random

from dotenv import load_dotenv

from thesis_writing.retriever.rerank_client import RerankClient

load_dotenv()


previous_paragraph_list = [
    "安陵容可以说是完全臣服于男权社会的抗争。她从一开始就企图通过恩宠来改变家族的命运和卑微的出身，所以她拼命去迎合皇帝的喜好，希望在君权下找到些许被关注的荣耀。她会冰嬉、会制香，拥有天生的好嗓音，尽管她不愿意苟延残喘下去，但她无可奈何。嗓音是为了吸引皇帝、冰嬉是为了重获圣宠、制香是为了巩固皇恩，她殚精竭虑，用尽心计直至心力交瘁, 最终却被自己苦苦追求死死捆绑的恩宠而摒弃。",
    "安陵容本性自卑，在她看来甄嬛有卓越的出身，有姣好的面容，皇帝宠爱她，姐妹维护她，而自己饱尝人家苦寒，在嫉妒心的作用下变得面目全非——用香料迷惑富察贵人小产，假意给甄嬛舒痕胶实则添加大量麝香，给甄嬛父亲放老鼠致使甄大人得疫，故意给眉庄报信致使眉庄早产血崩而死。她恨皇上，从来没有真心待过她，不过把她当作豢养的一只家鸟；她恨皇后，把她当棋子当工具，抹杀了她做母亲最后的希望；她恨甄嬛，明明什么都有但却什么都要和她抢，皇上的恩宠、昂贵的物件、姐妹的情谊全都不复存在；她更恨自己，恨自己不争气被人当作垫脚石一次次利用，从未真正为自己活过一秒。正如陵容自杀前所说：“这条命，这口气，从来由不得自己，如今，终于可以由自己做主一回了。”安陵容此生做出的唯一一次选择竟是决定自己的生死。",
    "安陵容出生于一户寻常人家，母亲是苏州的绣娘，靠着一针一线为父亲捐了一个芝麻大的小官。按道理来说县丞的女儿已经好过普通百姓千倍万倍，本可以平安无虞度过此生，但入宫为妃的选择让安陵容的人生彻底改变。安陵容家势力单薄，所以选秀时便被欺凌，好在甄嬛出手解围，机缘巧合之下，鬓边的秋海棠让她成功入了宫门。本以为荣华富贵近在咫尺，但噩梦却由此开启，皇上根本不记得这个“安答应”的存在，她更是在被召幸的那个晚上因为紧张发抖不止被皇帝退货，更是成为了满后宫的笑柄。就连复宠这件事也需要好姐妹的出面，在她眼里这份皇上的恩宠更像是姐妹甄嬛施舍给她的。甄嬛把她给的浮光锦分给了下人，父亲押送路上出事却无人肯帮，皇上说她送的香囊小家子气却把甄嬛送的当宝贝......一桩桩一件件都让安陵容意识到了恩宠和地位的重要性，她为了父亲投靠了皇后一派，急功近利急于求成追求恩宠，将自己推上了一条不归路。"
]
current_paragraph_list = [
    "安陵容本想借皇恩改变家族命运，却深陷男权社会的泥淖。她用尽才艺讨好皇帝，从冰嬉、制香到歌喉，无一不是为了争宠。然而，她越是努力，越是沦为权力的牺牲品。最终，她为之倾尽全力的恩宠，却成了将她推向深渊的利刃。",
    "安陵容，一个出身卑微的女子，在后宫的勾心斗角中逐渐迷失自我。起初，她对甄嬛的嫉妒源于自卑，随着时间的推移，这份嫉妒演变成了扭曲的恨意。她一步步走向深渊，从害人到害己，最终在无尽的悔恨中结束了生命。正如她所说：“这条命，这口气，从来由不得自己，如今，终于可以由自己做主一回了。”安陵容的一生，是一场悲剧，也是对人性复杂性的深刻探讨。",
    "安陵容出生于普通家庭，母亲是苏州的绣娘，凭借一针一线为父亲争得一个芝麻大的小官。理论上，县丞的女儿已经远胜普通百姓，本可安稳度过一生，但她入宫为妃却彻底改变了命运。安陵容家势力薄弱，选秀时受欺凌，幸亏甄嬛出手相救，机缘巧合下，鬓边的秋海棠助她成功入宫。原以为荣华富贵近在眼前，但噩梦随之而来，皇上对“安答应”毫无印象，她在被召幸之夜因紧张而被退回，更成了后宫的笑柄。即使复宠也需依赖好姐妹，皇上的恩宠在她看来更像是甄嬛施舍的。甄嬛把她的浮光锦分给下人，父亲在押送途中遇事无人援救，皇上嫌她送的香囊小气却珍视甄嬛的礼物……这一桩桩一件件让安陵容意识到恩宠和地位的重要，她为了父亲投靠皇后一派，急功近利追求恩宠，把自己推上了不归路。"
]


def test_ranker():
    reranker = RerankClient(base_url=os.environ["RERANK_URL"])
    idx_list = list(range(3))
    random.shuffle(idx_list)
    texts = [previous_paragraph_list[i] for i in idx_list]
    for i in range(3):
        result = reranker.rerank(query=current_paragraph_list[i], texts=texts)
        assert result[0].index == idx_list[i]
