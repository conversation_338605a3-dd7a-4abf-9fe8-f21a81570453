import asyncio
import json
import os

from langfuse.decorators import observe

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.gen_summary_agent import GenerateSummaryAgentInput
from thesis_writing.agent.retrieval_analysis.summarize_text_agent import SummarizeTextAgentInput
from thesis_writing.entity.enums import AgentType


@observe(name="test_gen_summary")
def test_gen_summary():
    asyncio.run(_gen_summary())


async def _gen_summary():
    # given
    major = "工商管理"
    subject = "企业转型策略研究"
    keywords = "中小企业, 企业转型, 策略"
    agent = AgentFactory.create_agent(AgentType.GEN_SUMMARY, TestUtil.get_writing_model_options_from_env())
    gen_summary_input = await GenerateSummaryAgentInput.construct_input_with_retrieval(major, subject, keywords)

    # when
    agent_output = agent.invoke(gen_summary_input)

    # then
    assert agent_output.summary is not None

    summary = agent_output.summary
    print(f"概述长度：{len(summary) - summary.count('*')}")


@observe(name="test_gen_summary_with_feeds")
def test_gen_summary_with_feeds():
    asyncio.run(_gen_summary_with_feeds())


async def _gen_summary_with_feeds():
    # given
    user_feeds = []

    folder = "/Users/<USER>/Downloads/untitled folder"

    # 读取folder目录下的所有txt文件内容
    for filename in os.listdir(folder):
        if filename.endswith(".txt"):
            with open(os.path.join(folder, filename), 'r', encoding='utf-8') as file:
                user_feeds.append(file.read())

    # 创建SummarizeTextAgent实例
    agent = AgentFactory.create_agent(AgentType.SUMMARIZE_TEXT, TestUtil.get_aliyun_model_options_from_env(model="qwen-plus", temperature=0.3))

    # 遍历user_feeds数组，对每个文本调用SummarizeTextAgent进行处理
    summarized_feeds = []
    for content in user_feeds:
        summarize_input = SummarizeTextAgentInput(
            major="工商管理",
            subject="企业转型策略研究",
            keywords="中小企业, 企业转型, 策略",
            origin_content=content)
        agent_output = await agent.ainvoke(summarize_input)
        summarized_feeds.append(agent_output.summary)

    # gen summary
    major = "工商管理"
    subject = "企业转型策略研究"
    keywords = "中小企业, 企业转型, 策略"

    agent = AgentFactory.create_agent(AgentType.GEN_SUMMARY, TestUtil.get_writing_model_options_from_env())
    gen_summary_input = await GenerateSummaryAgentInput.construct_input_with_retrieval(major, subject, keywords, summarized_feeds)

    # when
    agent_output = await agent.ainvoke(gen_summary_input)

    # then
    assert agent_output.summary is not None

    # count how many characters in the summary, except **
    summary = agent_output.summary
    print(f"概述长度：{len(summary) - summary.count('*')}")
