from dotenv import load_dotenv

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.gen_chapter_query_agent import GenerateChapterQueryAgentInput
from thesis_writing.entity.enums import AgentType

load_dotenv()


def test():
    agent = AgentFactory.create_agent(AgentType.GEN_CHAPTER_QUERY, TestUtil.get_qwen_model_options_from_env())

    # A公司
    gen_plan_input = GenerateChapterQueryAgentInput(
        subject="基于springboot的房屋租赁管理系统",
        major="软件工程",
        complete_writing_plan="{\"node_id\": \"1\", \"title\": \"1 绪论\", \"length\": \"2000\", \"children\": [\"{\\\"node_id\\\": \\\"1.1\\\", \\\"title\\\": \\\"1.1 研究背景与意义\\\", \\\"description\\\": \\\"分析当前房屋租赁市场的痛点，如租客与房东之间的信任问题、租赁信息不对称等。探讨技术发展趋势，如移动互联网、云计算等对房屋租赁市场的影响。明确研究的意义，解决现有系统存在的问题。\\\", \\\"length\\\": \\\"500\\\"}\", \"{\\\"node_id\\\": \\\"1.2\\\", \\\"title\\\": \\\"1.2 国内外研究现状\\\", \\\"description\\\": \\\"总结国内外关于房屋租赁管理系统的现有研究，指出研究空白。分析现有研究的优点和不足，强调本研究的创新点。\\\", \\\"length\\\": \\\"500\\\"}\", \"{\\\"node_id\\\": \\\"1.3\\\", \\\"title\\\": \\\"1.3 研究目标与创新点\\\", \\\"description\\\": \\\"明确本研究的主要目标，包括系统功能设计、性能提升和安全性增强。指出研究的创新点，如采用Spring Boot框架和模块化设计。\\\", \\\"length\\\": \\\"500\\\"}\", \"{\\\"node_id\\\": \\\"1.4\\\", \\\"title\\\": \\\"1.4 论文组织结构\\\", \\\"description\\\": \\\"简述各章节内容安排，包括绪论、相关技术综述、系统需求分析、系统设计、系统实现、系统测试和总结与展望。\\\", \\\"length\\\": \\\"500\\\"}\"]}\n{\"node_id\": \"2\", \"title\": \"2 相关技术综述\", \"length\": \"2000\", \"children\": [\"{\\\"node_id\\\": \\\"2.1\\\", \\\"title\\\": \\\"2.1 Spring Boot框架综述\\\", \\\"description\\\": \\\"介绍Spring Boot框架的特点，如自动配置、嵌入式服务器、简化配置等。探讨其在微服务架构中的应用，如服务注册与发现、负载均衡等。\\\", \\\"length\\\": \\\"667\\\"}\", \"{\\\"node_id\\\": \\\"2.2\\\", \\\"title\\\": \\\"2.2 MySQL数据库综述\\\", \\\"description\\\": \\\"介绍MySQL数据库的特点，如速度快、稳定性高、易用性强等。探讨其在系统中的作用与优势，如数据存储、查询效率等。\\\", \\\"length\\\": \\\"667\\\"}\", \"{\\\"node_id\\\": \\\"2.3\\\", \\\"title\\\": \\\"2.3 技术选型理由\\\", \\\"description\\\": \\\"说明选择Spring Boot和MySQL的原因，包括它们的优势、适用场景和与其他技术的比较。\\\", \\\"length\\\": \\\"666\\\"}\"]}\n{\"node_id\": \"3\", \"title\": \"3 系统需求分析\", \"length\": \"2000\", \"children\": [\"{\\\"node_id\\\": \\\"3.1\\\", \\\"title\\\": \\\"3.1 功能需求\\\", \\\"description\\\": \\\"详细描述系统的功能需求，包括用户注册、房源管理、订单管理、评论管理、支付管理等功能。通过用例图展示用户与系统之间的交互。补充信息：图表类型: 用例图\\\\n图表描述: 用例图展示用户与系统之间的交互，包括用户注册、房源管理、订单管理等用例。\\\\n\\\", \\\"length\\\": \\\"667\\\"}\", \"{\\\"node_id\\\": \\\"3.2\\\", \\\"title\\\": \\\"3.2 性能需求\\\", \\\"description\\\": \\\"分析系统的性能需求，包括响应时间、并发能力、吞吐量等指标。通过数据流图展示系统内部的数据流动。补充信息：图表类型: 数据流图\\\\n图表描述: 数据流图展示系统内部的数据流动，包括用户请求、数据处理、数据存储等流程。\\\\n\\\", \\\"length\\\": \\\"667\\\"}\", \"{\\\"node_id\\\": \\\"3.3\\\", \\\"title\\\": \\\"3.3 安全性需求\\\", \\\"description\\\": \\\"讨论系统的安全性需求，包括数据加密、权限管理、日志记录等。通过用例图展示系统在安全方面的具体需求。补充信息：图表类型: 用例图\\\\n图表描述: 用例图展示系统在安全方面的具体需求，包括数据加密、权限管理、日志记录等用例。\\\\n\\\", \\\"length\\\": \\\"666\\\"}\"]}\n{\"node_id\": \"4\", \"title\": \"4 系统设计\", \"length\": \"2000\", \"children\": [\"{\\\"node_id\\\": \\\"4.1\\\", \\\"title\\\": \\\"4.1 总体架构\\\", \\\"description\\\": \\\"描述系统的总体架构，采用模块化设计方法，包括用户管理、房源管理、订单管理等模块。通过架构图展示系统的整体结构。补充信息：图表类型: 架构图\\\\n图表描述: 架构图展示系统的总体架构，包括用户管理、房源管理、订单管理等模块的结构。\\\\n\\\", \\\"length\\\": \\\"667\\\"}\", \"{\\\"node_id\\\": \\\"4.2\\\", \\\"title\\\": \\\"4.2 模块功能划分\\\", \\\"description\\\": \\\"详细说明各模块的功能，包括用户管理、房源管理、订单管理、评论管理、支付管理等。通过功能图展示各模块的具体功能。补充信息：图表类型: 功能图\\\\n图表描述: 功能图展示各模块的具体功能，包括用户管理、房源管理、订单管理等。\\\\n\\\", \\\"length\\\": \\\"667\\\"}\", \"{\\\"node_id\\\": \\\"4.3\\\", \\\"title\\\": \\\"4.3 技术选型理由\\\", \\\"description\\\": \\\"说明选择Spring Boot和MySQL的原因，包括它们的优势、适用场景和与其他技术的比较。\\\", \\\"length\\\": \\\"666\\\"}\"]}\n{\"node_id\": \"5\", \"title\": \"5 系统实现\", \"length\": \"2000\", \"children\": [\"{\\\"node_id\\\": \\\"5.1\\\", \\\"title\\\": \\\"5.1 启动类配置\\\", \\\"description\\\": \\\"介绍Spring Boot启动类的配置方法，包括主类配置、配置文件加载和依赖注入等。\\\", \\\"length\\\": \\\"400\\\"}\", \"{\\\"node_id\\\": \\\"5.2\\\", \\\"title\\\": \\\"5.2 控制器层接口设计\\\", \\\"description\\\": \\\"描述控制器层的功能和接口设计，包括用户管理、房源管理、订单管理等模块的接口设计。\\\", \\\"length\\\": \\\"400\\\"}\", \"{\\\"node_id\\\": \\\"5.3\\\", \\\"title\\\": \\\"5.3 Service层业务逻辑处理\\\", \\\"description\\\": \\\"详细讲解Service层的业务逻辑，包括用户管理、房源管理、订单管理等模块的业务逻辑。\\\", \\\"length\\\": \\\"400\\\"}\", \"{\\\"node_id\\\": \\\"5.4\\\", \\\"title\\\": \\\"5.4 Repository层数据访问实现\\\", \\\"description\\\": \\\"介绍数据访问的方法，包括用户管理、房源管理、订单管理等模块的数据访问实现。\\\", \\\"length\\\": \\\"400\\\"}\", \"{\\\"node_id\\\": \\\"5.5\\\", \\\"title\\\": \\\"5.5 数据库集成方案\\\", \\\"description\\\": \\\"说明与MySQL数据库的集成方案，包括数据库连接配置、数据模型设计和数据操作实现。\\\", \\\"length\\\": \\\"400\\\"}\"]}\n{\"node_id\": \"6\", \"title\": \"6 系统测试\", \"length\": \"2000\", \"children\": [\"{\\\"node_id\\\": \\\"6.1\\\", \\\"title\\\": \\\"6.1 测试环境\\\", \\\"description\\\": \\\"描述测试所需的硬件和软件配置，包括服务器、数据库、浏览器等。\\\", \\\"length\\\": \\\"667\\\"}\", \"{\\\"node_id\\\": \\\"6.2\\\", \\\"title\\\": \\\"6.2 功能测试\\\", \\\"description\\\": \\\"设计测试用例，验证系统的各项功能，包括用户注册、房源管理、订单管理、评论管理、支付管理等。\\\", \\\"length\\\": \\\"667\\\"}\", \"{\\\"node_id\\\": \\\"6.3\\\", \\\"title\\\": \\\"6.3 性能测试\\\", \\\"description\\\": \\\"评估系统的性能指标，包括响应时间、并发能力、吞吐量等，确保系统满足预期设计目标。\\\", \\\"length\\\": \\\"666\\\"}\"]}\n{\"node_id\": \"7\", \"title\": \"7 总结与展望\", \"length\": \"2000\", \"children\": [\"{\\\"node_id\\\": \\\"7.1\\\", \\\"title\\\": \\\"7.1 工作总结\\\", \\\"description\\\": \\\"总结研究成果，包括系统设计、实现和测试的主要内容。指出研究过程中遇到的问题及解决方案。\\\", \\\"length\\\": \\\"1000\\\"}\", \"{\\\"node_id\\\": \\\"7.2\\\", \\\"title\\\": \\\"7.2 未来展望\\\", \\\"description\\\": \\\"展望未来的研究方向，提出进一步改进的建议，如增加新功能、优化性能、改进用户体验等。\\\", \\\"length\\\": \\\"1000\\\"}\"]}\n",
        current_chapter_title="1 绪论",
        chapter_additions="无"
    )

    agent_output = agent.invoke(gen_plan_input)
    print(agent_output.model_dump_json(indent=2))
