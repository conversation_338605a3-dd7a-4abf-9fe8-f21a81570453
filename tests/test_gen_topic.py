from dotenv import load_dotenv

from tests.utils.utils import TestUtil
from thesis_writing.agent.gen_topic_agent import GenerateTopicAgentInput, GenerateTopicAgent

load_dotenv()

def test_gen_summary():

    major = "土木工程"
    count = 3
    comment = '选题方向为办公楼建筑设计分析，重点在办公楼建筑设计，结构设计以及框架结构'
    options = TestUtil.get_aliyun_model_options_from_env(model='qwen3-30b-a3b',temperature=0.5)
    agent = GenerateTopicAgent(options, failover_options_list=[])

    input = GenerateTopicAgentInput(major=major, count=count, comment=comment)
    output = agent.invoke(input)
    print(output.model_dump_json(indent=2))