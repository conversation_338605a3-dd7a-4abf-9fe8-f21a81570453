import json
import time

from dotenv import load_dotenv

from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.literature_review.gen_literature_review_plan_agent import GenerateLiteratureReviewPlanAgentInput
from thesis_writing.entity.enums import AgentType
from utils.utils import TestUtil

load_dotenv()


def test_literature_review():
    # given
    subject = "企业海外投资风险评估模型构建"
    major = "国际经济与贸易"
    keywords = "海外投资，风险评估，模型构建，跨国企业，投资风险"
    summary = "论文首先在绪论部分阐述研究背景与意义，强调在全球化背景下，跨国企业进行海外投资的重要性以及面临的风险挑战。研究方法包括文献综述、案例分析和实证研究，旨在构建一套科学合理的海外投资风险评估模型。文献综述部分系统回顾了现有研究，明确了风险评估的基本概念、分类和评估方法，为后续模型构建奠定理论基础。\n接着，论文详细分析海外投资的风险类型，包括政治风险、经济风险、市场风险、法律风险和社会文化风险等，每种风险的具体表现形式及其对企业的影响。通过案例分析，具体展示跨国企业在不同国家和地区遇到的实际风险问题，进一步丰富了风险识别的视角。\n在风险评估模型构建部分，论文提出了一套综合性的评估框架。模型设计基于层次分析法（AHP），将各类风险指标进行量化，通过专家打分和数据收集，确定各风险指标的权重。模型不仅考虑了宏观层面的风险因素，还结合微观层面的企业内部管理能力，形成一个多维度、多层次的风险评估体系。\n实证分析部分，论文选取若干具有代表性的跨国企业作为样本，应用构建的风险评估模型进行实际测试。通过对样本企业海外投资项目的数据进行分析，验证模型的有效性和可行性。结果表明，模型能够较为准确地反映企业面临的各种风险，为企业制定风险管理策略提供科学依据。\n最后，论文总结研究结论，指出构建的海外投资风险评估模型对提高跨国企业风险防范能力具有重要意义。同时，提出未来研究方向，如引入更多动态因素和扩展样本范围，以进一步优化模型。"

    begin_time = time.time()
    agent = AgentFactory.create_agent(AgentType.GEN_LITERATURE_REVIEW_PLAN, TestUtil.get_qwen_model_options_from_env())

    gen_input = GenerateLiteratureReviewPlanAgentInput(
        subject=subject,
        major=major,
        keywords=keywords,
        summary=summary,
    )

    # when
    agent_output = agent.invoke(gen_input)
    print("elapsed time:", time.time() - begin_time)

    # then
    print(json.dumps(agent_output.model_dump(exclude_none=True), indent=2, ensure_ascii=False))
