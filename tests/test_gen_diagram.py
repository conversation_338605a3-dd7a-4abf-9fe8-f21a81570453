import json

from dotenv import load_dotenv

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.chart.gen_chart_agent import GenerateScatterChartAgentResponse
from thesis_writing.agent.chart.gen_diagram_agent import GenerateDiagramAgentInput
from thesis_writing.entity.enums import AgentType

load_dotenv()

subject = "学生信息管理系统"
major = "计算机科学与技术"
keywords = "学生信息管理系统,数据库管理,Web开发技术,系统架构设计,敏捷开发"
segment_title = "2.1 物联网感知技术"
segment_content = f"""
“学生信息管理系统” 功能性需求包括以下内容：
        （1）系统管理员登录后可以对班级的基本信息进行增加、删除、修改、查询等操作。学校领导登录后可以对班级基本信息进行查询操作。
        （2）教师登录后可以对学生的考试成绩进行录入、删除、修改、查询等操作。学生登录后可以对考试成绩进行查询操作。
        （3）学生登录后可以了解所有选修课程的具体信息，可以根据自己的需要选择不同课程。系统管理员登录后可以增加、修改、查询、删除选修课程。
        （4）系统管理员可以对账号进行创建、设置、查看、删除等操作。
"""
gemini_agent_options = TestUtil.get_gemini_model_options_from_env()
deepseek_agent_options = TestUtil.get_ds_model_options_from_env()
agent_input = GenerateDiagramAgentInput(
    subject=subject,
    major=major,
    keywords=keywords,
    # complete_writing_plan=complete_writing_plan,
    segment_title=segment_title,
    segment_content=segment_content,
    chart_title='学生信息管理系统'
)


def test_gen_erdiagram_diagram():
    print("实体关系图")
    agent = AgentFactory.create_agent(AgentType.GEN_ER_DIAGRAM, deepseek_agent_options)
    output = agent.invoke(agent_input)
    mermaid = output.model_dump(exclude_none=True)['diagram']
    print(mermaid)


def test_gen_usecase_diagram():
    print("用例图")
    agent = AgentFactory.create_agent(AgentType.GEN_USECASE_DIAGRAM, gemini_agent_options)
    output = agent.invoke(agent_input)
    mermaid = output.model_dump(exclude_none=True)['diagram']
    print(mermaid)


def test_gen_class_diagram():
    print("类图")
    agent = AgentFactory.create_agent(AgentType.GEN_CLASS_DIAGRAM, gemini_agent_options)
    output = agent.invoke(agent_input)
    mermaid = output.model_dump(exclude_none=True)['diagram']
    print(mermaid)


def test_gen_architecture_diagram():
    print("架构图")
    agent = AgentFactory.create_agent(AgentType.GEN_ARCHITECTURE_DIAGRAM, deepseek_agent_options)
    output = agent.invoke(agent_input)
    mermaid = output.model_dump(exclude_none=True)['diagram']
    print(f"checked: {mermaid}")


def test_gen_sequence_diagram():
    print("时序图")
    agent = AgentFactory.create_agent(AgentType.GEN_SEQUENCE_DIAGRAM, deepseek_agent_options)
    output = agent.invoke(agent_input)
    mermaid = output.model_dump(exclude_none=True)['diagram']
    print(mermaid)


def test_gen_state_diagram():
    print("状态图")
    agent = AgentFactory.create_agent(AgentType.GEN_STATE_DIAGRAM, deepseek_agent_options)
    output = agent.invoke(agent_input)
    mermaid = output.model_dump(exclude_none=True)['diagram']
    print(mermaid)


def test_gen_gantt_diagram():
    print("甘特图")
    agent = AgentFactory.create_agent(AgentType.GEN_GANTT_DIAGRAM, gemini_agent_options)
    output = agent.invoke(agent_input)
    mermaid = output.model_dump(exclude_none=True)['diagram']
    print(mermaid)


def test_gen_timeline_diagram():
    print("时间线图")
    agent = AgentFactory.create_agent(AgentType.GEN_TIMELINE_DIAGRAM, gemini_agent_options)
    output = agent.invoke(agent_input)
    mermaid = output.model_dump(exclude_none=True)['diagram']
    print(mermaid)


def test_gen_kanban_diagram():
    print("看板图")
    agent_input.segment_content = """
            待办事项	进行中（开发）	测试中	待验收	已完成
用户注册模块开发（优先级高）	客户信息管理模块开发（张三）	销售报表功能测试（李四）	用户权限分配验收	首页界面设计（完成）
客户分类功能需求分析	聊天功能集成（王五）	登录页面UI测试（赵六）	邮件通知功能验收	数据库迁移（完成）
报表导出功能开发				错误日志模块（完成）
            """
    agent = AgentFactory.create_agent(AgentType.GEN_KANBAN_DIAGRAM, deepseek_agent_options)
    output = agent.invoke(agent_input)
    mermaid = output.model_dump(exclude_none=True)['diagram']
    print(mermaid)


def test_gen_flowchart_diagram():
    print("流程图")
    agent_input.chart_title = "教师登记成绩流程图"
    agent_input.segment_content = "登录系统 → 选择班级与课程 → 进入成绩录入界面 → 手动/批量录入成绩 → 保存草稿 → 检查数据 → 提交审核 → 审核通过后通知学生 → 完成  "
    agent = AgentFactory.create_agent(AgentType.GEN_FLOW_CHART, deepseek_agent_options)
    output = agent.invoke(agent_input)
    mermaid = output.model_dump(exclude_none=True)['diagram']
    print(mermaid)


def test_scatter():
    print("散点图")
    scatter_data = {
        "thought": "...",
        "x_axis_label": "经度",
        "y_axis_label": "纬度",
        "x_axis_data": ["67.42", "171.13", 131.76, 107.76, 28.08, 28.08, 10.46, 155.91, 108.2, 127.45],
        "y_axis_data": [-86.29, 84.58, 59.84, -51.78, -57.27, -56.99, -35.24, 4.46, -12.25, -37.58],
        "value": [
            {
                "label": "价值量1",
                "data": [58, 41, 91, 59, 79, 14, 61, 61, 46, 61]
            },
            {
                "label": "价值量2",
                "data": [12, 46, 16, 30, 18, 48, 27, 13, 34, 23]
            },
        ]
    }
    scatter_response = GenerateScatterChartAgentResponse.model_validate_json(json.dumps(scatter_data))
    scatter_response.render_html()


def test_flowchart1():
    print("流程图")
    subject = "浅析辽源市旅游资源开发现状及对策"
    major = "旅游管理"
    keywords = "旅游资源开发，辽源市，综合评价，对策建议，旅游业发展"
    segment_title = "4.2 数据收集与处理"
    segment_content = """
    辽源市旅游资源评价的数据收集与处理是确保评价结果可靠性和客观性的关键环节。数据来源可分为第一手资料和第二手资料。第一手资料是为了特定研究目的而专门收集的，这类数据可以通过问卷调查、野外考察或实验室研究获得，这些资料通常是其他途径所无法获取的。第一手资料的优点是保持一致性较好，但收集成本较高且耗时较长，通常需要地方政府的协助。收集第一手资料的主要方法包括调查法和观察法。常用的调查法包括抽样调查、重点调查、典型调查以及普查。这些方法可以通过个人面谈、小组讨论、电话询问、邮寄调查表或混合形式进行。常见的观察法则包括直接观察法、实际痕迹测量法和行为记录法。相对而言，抽样调查法在第一手资料的获取中应用较为广泛，尤其是通过填写调查表的方式进行的抽样调查，这种方式可以有效地收集各方面的第一手资料，且所需时间较少。进行抽样调查时，设计调查表格是最重要的步骤之一[17]。
第二手资料则包括科研档案、地图、统计报表、人口普查数据等。收集第二手资料能够节省时间和费用，并且有助于更加准确和有针对性地进行第一手资料的收集。然而，第二手资料往往与调查目的、口径及方法不完全吻合，并且在时间性和精准性方面可能达不到要求。因此，在使用第二手资料时，必须明确材料的来源，并了解其目的、口径和可比性。第二手资料的主要来源有四个：第一是企业内部材料，这是进行具体研究的重要数据来源；第二是各种旅游报纸、杂志和调研专辑，如《中国旅游报》、《旅游学刊》、《旅游调研》和《中国旅游统计年鉴》等；第三是国际及区域旅游组织和专业旅游市场调研机构的年报及其他资料，例如，世界旅游组织（WTO）在马德里的资料，亚洲太平洋地区旅游协会（PATA）在曼谷的资料，美洲旅行代理人协会（ASTA）在华盛顿的资料，欧洲旅游委员会（ETC）的资料，国际会议协会（ICCA）在巴黎的资料，国际旅馆协会（IHA）在巴黎的资料，以及美国数据中心（U.S. Data Center）在华盛顿的统计资料；第四是网络信息，包括各国或地区官方网站上的旅游信息、游客博客及旅游论坛等。近年来，越来越多的旅游统计数据在各地方和政府网站上发布，加之游客在博客和论坛等网络空间所发表的信息，成为了学者研究的关注点。
数据采集方法主要包括问卷调查和实地考察。问卷调查法是管理学定量研究中最常见的方法之一，其优点在于节省时间和精力、易于量化结果、便于统计和分析、适合大规模调查且对被调查者干扰较小。若量表的信度和效度较高，问卷调查能够获得高质量的研究数据。在正式调研前，对相关变量的概念进行了明确界定。问卷设计时，参考了国内外高信效度的成熟量表，并采用回译法翻译国外量表，结合旅游企业的实际情况进行适当调整。问卷的信度与效度也通过实证数据得到了验证。实地考察法则通过实地走访、现场观察和深度访谈等方式，获取旅游资源的详细信息和实际情况。实地考察可以提供更为直观和真实的资料，但耗时较长且成本较高。
数据处理流程包括数据清洗、数据录入和数据汇总等步骤。数据清洗是指对收集到的数据进行筛选、校正和删除无效或错误数据的过程，确保数据的准确性和一致性。数据录入是指将清洗后的数据输入计算机系统，以便进行进一步的分析和处理。数据录入时应注意数据的格式和结构，确保数据的完整性和可读性。数据汇总则是将录入的数据进行分类、整理和统计，形成可供分析的数据库。具体步骤包括：首先，对收集到的数据进行初步审核，剔除明显错误或不完整的数据；其次，对数据进行分类和编码，确保数据的一致性和可比性；再次，将编码后的数据输入计算机系统，生成数据库；最后，对数据库进行统计分析，形成最终的评价结果[18]。
    """
    agent_input = GenerateDiagramAgentInput(
        subject=subject,
        major=major,
        keywords=keywords,
        segment_title=segment_title,
        segment_content=segment_content,
        chart_title=""
    )
    agent = AgentFactory.create_agent(AgentType.GEN_FLOW_CHART, deepseek_agent_options)
    output = agent.invoke(agent_input)
    mermaid = output.model_dump(exclude_none=True)['diagram']
    print(mermaid)

def test_mindmao():
    print("思维导图")
    agent_input = GenerateDiagramAgentInput(
        subject="电脑操作系统分类",
        major="计算机科学与技术",
        keywords="操作系统，分类，Windows，Linux，Unix，Mac OS",
        segment_title="操作系统分类",
        segment_content="CCC",
        chart_title="电脑操作系统分类"
    )
    agent = AgentFactory.create_agent(AgentType.GEN_MINDMAP_DIAGRAM, deepseek_agent_options)
    output = agent.invoke(agent_input)
    diagram = output.model_dump(exclude_none=True)['diagram']
    print(diagram)

