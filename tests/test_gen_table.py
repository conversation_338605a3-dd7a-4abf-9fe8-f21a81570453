from dotenv import load_dotenv
from langfuse.decorators import observe

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.chart.gen_chart_agent import GenerateChartAgentInput
from thesis_writing.entity.enums import AgentType

load_dotenv()


@observe(name="test GEN_TABLE")
def test_gen_table():
    failover_options_list = [
        TestUtil.get_aliyun_model_options_from_env(model="qwen3-32b", temperature=0.7),
        TestUtil.get_ds_model_options_from_env()
    ]

    agent = AgentFactory.create_agent(AgentType.GEN_TABLE, TestUtil.get_qwen_model_options_from_env(),
                                      failover_options_list)

    agent_input = GenerateChartAgentInput(
        subject="基于校园大数据的学生信息管理平台的设计研究",
        major="计算机科学与技术",
        keywords="数据挖掘,高校学生管理,学生画像,行为分析",
        complete_writing_plan="[{\"node_id\": \"1\", \"title\": \"1 绪论\", \"content_type\": \"绪论\", \"length\": \"1500\", \"children\": [{\"node_id\": \"1.1\", \"title\": \"1.1 研究背景\", \"description\": \"简要介绍现代高等教育机构面临的学生数量激增带来的管理挑战，传统管理模式的困境，以及基于校园大数据的学生信息管理平台的重要性。\", \"length\": \"400\", \"children\": []}, {\"node_id\": \"1.2\", \"title\": \"1.2 研究目的与意义\", \"description\": \"说明研究的目的，强调基于校园大数据的学生信息管理平台如何优化高校学生管理，提高管理效率和服务质量。\", \"length\": \"350\", \"children\": []}, {\"node_id\": \"1.3\", \"title\": \"1.3 国内外研究现状\", \"description\": \"综述国内外在学生信息管理和数据挖掘技术方面的研究现状，指出现有研究的不足之处。\", \"length\": \"350\", \"children\": []}, {\"node_id\": \"1.4\", \"title\": \"1.4 研究内容与方法\", \"description\": \"简要介绍研究内容，包括平台设计原则与架构、学生画像构建、个性化服务推荐机制等，说明研究方法，如数据挖掘、聚类分析、关联规则挖掘等。\", \"length\": \"400\", \"children\": []}]}, {\"node_id\": \"2\", \"title\": \"2 相关技术综述\", \"content_type\": \"正文章节\", \"length\": \"2000\", \"children\": [{\"node_id\": \"2.1\", \"title\": \"2.1 数据挖掘技术\", \"description\": \"介绍数据挖掘的基本概念、常用算法及其在教育领域的应用，如聚类分析、关联规则挖掘等。\", \"length\": \"700\", \"children\": []}, {\"node_id\": \"2.2\", \"title\": \"2.2 学生画像技术\", \"description\": \"介绍学生画像的基本概念、构建方法及其在教育管理中的应用，如个性化服务推荐、学生行为分析等。\", \"length\": \"700\", \"children\": []}, {\"node_id\": \"2.3\", \"title\": \"2.3 数据安全性和隐私保护\", \"description\": \"讨论如何在平台设计中保障数据的安全性和用户的隐私，如数据加密、匿名化处理等。\", \"length\": \"600\", \"children\": []}]}, {\"node_id\": \"3\", \"title\": \"3 平台设计与架构\", \"content_type\": \"正文章节\", \"length\": \"3500\", \"children\": [{\"node_id\": \"3.1\", \"title\": \"3.1 设计原则与目标\", \"description\": \"介绍平台的设计原则与目标，如易用性、可扩展性、安全性等。\", \"length\": \"500\", \"children\": []}, {\"node_id\": \"3.2\", \"title\": \"3.2 数据收集模块\", \"description\": \"介绍如何从校园一卡通系统、教务系统等多个渠道获取原始数据，包括数据接口设计、数据传输协议等。\", \"length\": \"800\", \"children\": []}, {\"node_id\": \"3.3\", \"title\": \"3.3 数据处理模块\", \"description\": \"介绍数据清洗、转换和规范化的方法，确保数据质量，包括数据预处理技术、数据质量评估等。\", \"length\": \"1000\", \"children\": []}, {\"node_id\": \"3.4\", \"title\": \"3.4 数据分析模块\", \"description\": \"介绍如何采用数据挖掘技术对学生行为模式进行分析，形成学生画像，包括聚类分析、关联规则挖掘等。\", \"length\": \"800\", \"children\": []}, {\"node_id\": \"3.5\", \"title\": \"3.5 结果呈现模块\", \"description\": \"介绍如何通过可视化界面展示分析结果，供管理人员参考决策，包括界面设计、用户交互设计等。\", \"length\": \"400\", \"children\": []}]}, {\"node_id\": \"4\", \"title\": \"4 学生画像构建\", \"content_type\": \"正文章节\", \"length\": \"4500\", \"children\": [{\"node_id\": \"4.1\", \"title\": \"4.1 聚类分析\", \"description\": \"介绍如何通过聚类分析识别学生的行为模式和偏好，包括聚类算法的选择、参数设置、聚类结果的评估等。\", \"length\": \"1000\", \"children\": []}, {\"node_id\": \"4.2\", \"title\": \"4.2 关联规则挖掘\", \"description\": \"介绍如何通过关联规则挖掘发现学生行为之间的关联关系，包括关联规则的生成、评估和应用等。\", \"length\": \"1000\", \"children\": []}, {\"node_id\": \"4.3\", \"title\": \"4.3 学习状况分析\", \"description\": \"分析学生的学习状况，包括课程成绩、出勤情况、在线学习行为等，形成学习行为画像。\", \"length\": \"800\", \"children\": []}, {\"node_id\": \"4.4\", \"title\": \"4.4 社交活动分析\", \"description\": \"分析学生的社交活动，包括社团参与、校园活动参加情况等，形成社交活动画像。\", \"length\": \"700\", \"children\": []}, {\"node_id\": \"4.5\", \"title\": \"4.5 生活消费分析\", \"description\": \"分析学生的生活消费，包括餐饮、购物、娱乐等消费行为，形成生活消费画像。\", \"length\": \"500\", \"children\": []}, {\"node_id\": \"4.6\", \"title\": \"4.6 个性化服务推荐机制\", \"description\": \"提出一套基于学生画像的个性化服务推荐机制，根据不同学生群体的需求，提供定制化的学习资源、心理健康辅导等服务。\", \"length\": \"500\", \"children\": []}]}, {\"node_id\": \"5\", \"title\": \"5 平台实现与测试\", \"content_type\": \"正文章节\", \"length\": \"4000\", \"children\": [{\"node_id\": \"5.1\", \"title\": \"5.1 技术选型\", \"description\": \"介绍平台选用的技术栈，如数据库、前端框架、后端语言等，说明选择这些技术的原因。\", \"length\": \"800\", \"children\": []}, {\"node_id\": \"5.2\", \"title\": \"5.2 开发环境\", \"description\": \"介绍开发工具、开发流程、版本控制等，说明开发环境的搭建过程。\", \"length\": \"800\", \"children\": []}, {\"node_id\": \"5.3\", \"title\": \"5.3 部署方案\", \"description\": \"介绍平台在某高校的实际部署方案，包括服务器配置、网络架构等，说明部署过程中的注意事项。\", \"length\": \"800\", \"children\": []}, {\"node_id\": \"5.4\", \"title\": \"5.4 测试结果\", \"description\": \"分析平台实施前后的管理效率变化，如学生管理效率、服务满意度等，提供具体的测试数据和图表。\", \"length\": \"1600\", \"children\": []}]}, {\"node_id\": \"6\", \"title\": \"6 结论与展望\", \"content_type\": \"结论\", \"length\": \"1500\", \"children\": [{\"node_id\": \"6.1\", \"title\": \"6.1 研究成果\", \"description\": \"总结基于校园大数据的学生信息管理平台的主要研究成果，强调平台在优化高校学生管理、提高管理效率和服务质量方面的作用。\", \"length\": \"700\", \"children\": []}, {\"node_id\": \"6.2\", \"title\": \"6.2 研究不足与局限\", \"description\": \"指出研究中的不足和局限，如数据来源有限、算法优化空间等，说明这些不足对研究结果的影响。\", \"length\": \"400\", \"children\": []}, {\"node_id\": \"6.3\", \"title\": \"6.3 未来研究方向\", \"description\": \"提出未来的研究方向，包括引入更多外部数据源、探索更先进的数据处理技术等，以期不断优化平台功能，更好地服务于高校学生管理工作。\", \"length\": \"400\", \"children\": []}]}]",
        segment_title="4.3 学习状况分析",
        segment_content="学习状况分析是基于校园大数据的学生信息管理平台的重要组成部分，旨在通过分析学生的课程成绩、出勤情况、在线学习行为等数据，形成学习行为画像。具体来说，我们将学习状况分为优秀、良好、中等、较差四类。优秀学生指的是课程成绩在90分以上且出勤率在95%以上的学生；良好学生指的是课程成绩在80-89分且出勤率在90%以上的学生；中等学生指的是课程成绩在70-79分且出勤率在80%以上的学生；较差学生指的是课程成绩在70分以下且出勤率在80%以下的学生。\\n\\n学习行为与学生成绩之间的关系是分析的重点。研究表明，学生在线学习行为与最终成绩之间存在显著的相关性。具体而言，学生在线测试成绩、作业完成情况和课程音视频学习情况对成绩有显著影响。在线测试成绩反映了学生对课程内容的掌握程度，作业完成情况则体现了学生的学习态度和努力程度，课程音视频学习情况则反映了学生的学习资源利用率。通过皮尔逊相关系数分析法、k均值聚类算法及决策树归纳算法进行数据分析，结果表明，这些行为特征对学生的最终成绩具有显著预测作用[[27]]。\\n\\n通过具体案例，可以进一步验证学习行为与学生成绩之间的关系。例如，某高校利用教育数据挖掘技术对网络教学平台上的学生学习行为数据进行了分析，发现成绩上升的学生通常会先学习微课再收藏，而成绩下降的学生则相反。具体分析表明，学生在课程中的行为模式对其学习成绩有直接影响。例如，积极观看课程音视频、按时完成作业、参与在线讨论的学生，其成绩普遍较高。而那些不积极学习、缺课较多、作业完成质量低的学生，则成绩普遍较低[[27]]。\\n\\n为了更好地了解学生的学习状况，本文采用饼图展示了学生学习状况的分布情况（见图4-1）。饼图的不同扇形区域代表不同的学习状况，如优秀、良好、中等、较差等，扇形区域的大小表示该学习状况的学生比例。从图4-1可以看出，大多数学生的学习状况为良好和中等，而优秀和较差的学生比例相对较小。\\n\\n基于以上分析，本文提出了一套基于学习行为画像的个性化服务推荐机制。通过识别学生的学习行为模式和偏好，可以为每位学生提供定制化的学习资源和辅导策略。例如，针对学习动机较低的学生，可以提供更多的学习资源和激励措施；针对学习效果不佳的学生，可以通过教师的个性化辅导和学习计划调整，帮助其提高学习效果。此外，通过持续的学情分析，可以及时发现学生在学习过程中遇到的问题，并提供针对性的解决方案，从而促进学生的全面发展。\\n\\n图4-1：学生学习状况分布\\n\\n<figure title=\"图4-1 学生学习状况分布",
        chart_info=f"""\
            ID：图4-1
            标题：学生学习状况分布
            描述：学生学习状况分布饼图。饼图的不同扇形区域代表不同的学习状况（如优秀、良好、中等、较差等），扇形区域的大小表示该学习状况的学生比例。 
            目的：通过饼图直观展示学生学习状况的分布情况，为个性化服务推荐机制的制定提供依据。
            """
    )
    output = agent.invoke(agent_input)

    print(output.model_dump_json(indent=2))