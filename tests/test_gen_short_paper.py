import json

from langfuse.decorators import observe

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.gen_abstraction_agent import GenerateAbstractionAgentInput
from thesis_writing.agent.gen_acknowledge_agent import GenerateAcknowledgeAgentInput
from thesis_writing.agent.gen_segment_agent import GenerateSegmentAgentInput
from thesis_writing.agent.gen_summary_agent import GenerateSummaryAgentInput
from thesis_writing.entity.enums import AgentType

subject = "任务驱动教学法在高中阅读教学中运用的实践研究"
major = "汉语言文学"
summary = """
    论文首先从高中语文阅读教学的现状出发，指出传统教学模式在激发学生兴趣、培养自主学习能力方面的不足，强调任务驱动教学法作为一种新型教学模式的应用价值。接着，论文梳理了任务驱动教学法的理论基础，包括建构主义学习理论和多元智能理论，阐明其在促进学生主动参与和深度学习中的优势。

在实践研究部分，论文设计了一套基于任务驱动的教学实施方案，选取高中阅读教学中的经典篇目作为案例，详细描述了任务设计、课堂实施和评价反馈的过程。通过课堂观察和学生问卷调查，论文分析了任务驱动教学法对学生阅读兴趣、理解能力和批判性思维的积极影响，同时指出实践中存在的问题，如任务难度设计不当和教师指导不足。

最后，论文总结了任务驱动教学法在高中阅读教学中的实践成效，并提出优化建议，包括完善任务设计、加强教师培训和构建多元评价体系，以进一步提升教学效果。
    """
word_count = "8000"

writing_plan = """
{"writing_plan_nodes": [
        {
            "node_analysis": "绪论部分需简要介绍高中语文阅读教学的现状及其问题，引出任务驱动教学法的应用价值。内容包括研究背景、研究意义及研究方法，避免设置二级节点，直接以整体形式呈现。重点突出传统教学模式的不足以及任务驱动教学法的优势，为后续章节奠定理论基础。",
            "id": "1",
            "title": "第一章 绪论",
            "content_type": "绪论",
            "writing_length": 300,
            "children": []
        },
        {
            "node_analysis": "本章为论文的核心部分，需详细描述任务驱动教学法在高中阅读教学中的实践过程。首先梳理任务驱动教学法的理论基础，包括建构主义学习理论和多元智能理论；其次设计具体的教学实施方案，选取经典篇目进行案例分析，描述任务设计、课堂实施和评价反馈的全过程；最后通过课堂观察和问卷调查分析教学效果，指出存在的问题。",
            "id": "2",
            "title": "第二章 任务驱动教学法在高中阅读教学中的实践研究",
            "content_type": "正文章节",
            "writing_length": "4500",
            "children": [
                {
                    "id": "2.1",
                    "title": "2.1 任务驱动教学法的理论基础",
                    "content_type": "正文章节",
                    "description": "本节阐述任务驱动教学法的理论基础，重点介绍建构主义学习理论和多元智能理论，分析其在促进学生主动参与和深度学习中的作用。内容需简洁明了，为后续实践提供理论支撑。",
                    "writing_length": 1000
                },
                {
                    "id": "2.2",
                    "title": "2.2 任务驱动教学法的实施方案设计",
                    "content_type": "正文章节",
                    "writing_length": "1500",
                    "children": [
                        {
                            "id": "2.2.1",
                            "title": "2.2.1 任务设计的具体方法",
                            "content_type": "正文章节",
                            "description": "详细描述任务设计的具体方法，包括任务目标、类型及内容设计，结合高中阅读教学的特点，提出适合学生的任务模式。",
                            "writing_length": 750
                        },
                        {
                            "id": "2.2.2",
                            "title": "2.2.2 课堂实施与评价反馈",
                            "content_type": "正文章节",
                            "description": "介绍任务驱动教学法的课堂实施过程，包括教师引导、学生参与及互动环节；同时说明评价反馈机制的设计与实施效果。",
                            "writing_length": 750
                        }
                    ]
                },
                {
                    "id": "2.3",
                    "title": "2.3 教学效果分析与问题反思",
                    "content_type": "正文章节",
                    "description": "通过课堂观察和学生问卷调查的数据分析任务驱动教学法对学生阅读兴趣、理解能力和批判性思维的积极影响；同时反思实践中存在的问题，如任务难度设计不当和教师指导不足。",
                    "writing_length": 2000
                }
            ]
        },
        {
            "node_analysis": "结论部分总结任务驱动教学法在高中阅读教学中的实践成效，简要概括其对学生阅读能力提升的积极影响，并提出优化建议，包括完善任务设计、加强教师培训和构建多元评价体系。避免设置二级节点，直接以整体形式呈现。",
            "id": "3",
            "title": "第三章 结论",
            "content_type": "结论",
            "writing_length": 600,
            "children": []
        }
    
    ]
}
"""


@observe(name="TestShortSegment")
def test_short_segment():
    segment_input = GenerateSegmentAgentInput(
        subject=subject,
        major=major,
        complete_writing_plan=writing_plan,
        segment_writing_plan="""
        {
            "node_analysis": "绪论部分需简要介绍高中语文阅读教学的现状及其问题，引出任务驱动教学法的应用价值。内容包括研究背景、研究意义及研究方法，避免设置二级节点，直接以整体形式呈现。重点突出传统教学模式的不足以及任务驱动教学法的优势，为后续章节奠定理论基础。",
            "id": "1",
            "title": "第一章 绪论",
            "content_type": "绪论",
            "writing_length": 300,
            "children": []
        }
        """
    )
    segment_agent = AgentFactory.create_agent(AgentType.GEN_SHORT_SEGMENT,
                                               TestUtil.get_aliyun_model_options_from_env(model="qwen3-235b-a22b-instruct-2507", temperature=0.7))
    segment_output = segment_agent.invoke(segment_input)
    # print(addition_output)
    print(json.dumps(segment_output.model_dump(exclude_none=True), indent=4, ensure_ascii=False))

@observe(name="TestShortAcknowledgement")
def test_short_acknowledgement():
    ack_input = GenerateAcknowledgeAgentInput(
        subject=subject,
        complete_writing_plan=writing_plan
    )
    ack_agent = AgentFactory.create_agent(AgentType.GEN_SHORT_ACKNOWLEDGEMENT, TestUtil.get_ds_model_options_from_env())
    ack_output = ack_agent.invoke(ack_input)
    # print(addition_output)
    print(json.dumps(ack_output.model_dump(exclude_none=True), indent=4, ensure_ascii=False))

@observe(name="TestShortAbstract")
def test_short_acknowledgement():
    abstract_input = GenerateAbstractionAgentInput(
        subject="膜增生性肾小球肾炎病案分析"
    )
    abstract_agent = AgentFactory.create_agent(AgentType.GEN_SHORT_ABSTRACT_KEYWORDS, TestUtil.get_ds_model_options_from_env())
    abstract_output = abstract_agent.invoke(abstract_input)
    # print(addition_output)
    print(json.dumps(abstract_output.model_dump(exclude_none=True), indent=4, ensure_ascii=False))

@observe(name="TestShortSummary")
def test_short_summary():
    summary_input = GenerateSummaryAgentInput(
        subject=subject,
        major=major
    )
    summary_agent = AgentFactory.create_agent(AgentType.GEN_SHORT_SUMMARY, TestUtil.get_ds_model_options_from_env())
    summary_output = summary_agent.invoke(summary_input)
    # print(addition_output)
    print(json.dumps(summary_output.model_dump(exclude_none=True), indent=4, ensure_ascii=False))