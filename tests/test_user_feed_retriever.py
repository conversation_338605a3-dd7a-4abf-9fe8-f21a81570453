import os
from typing import List

import pytest
from unittest.mock import AsyncMock, patch

from dotenv import load_dotenv

from thesis_writing.retriever.user_feed_retriever import UserFeedRetriever
from thesis_writing.retriever.index.user_feed_chunk import UserFeedChunk

load_dotenv()


@pytest.mark.asyncio
async def test_get_user_feeds():
    es_config = {
        "hosts": os.environ["ELASTICSEARCH_HOSTS"],
        "username": os.environ["ELASTICSEARCH_USERNAME"],
        "password": os.environ["ELASTICSEARCH_PASSWORD"]
    }
    retriever = UserFeedRetriever(
        embedding_url=os.environ["EMBEDDING_URL"],
        rerank_url=os.environ["RERANK_URL"],
        es_config=es_config,
        index_name="user_feed_chunk")

    # Call the get_user_feeds method
    user_feeds = await retriever.get_user_feeds(job_id=1843, material_ids=["0608bdcf-3ddf-44c8-a50c-8aff80492eec", "639ec4ef-04ef-4525-b190-1f58e613ddd8"])

    # Assert that the user_feeds is a list of tuples
    assert isinstance(user_feeds, list)
    assert all(isinstance(user_feed, tuple) for user_feed in user_feeds)


@pytest.mark.asyncio
async def test_search_user_feeds():
    es_config = {
        "hosts": os.environ["ELASTICSEARCH_HOSTS"],
        "username": os.environ["ELASTICSEARCH_USERNAME"],
        "password": os.environ["ELASTICSEARCH_PASSWORD"]
    }
    retriever = UserFeedRetriever(
        embedding_url=os.environ["EMBEDDING_URL"],
        rerank_url=os.environ["RERANK_URL"],
        es_config=es_config,
        index_name="user_feed_chunk"
    )

    title = "模型的数学框架"
    queries = ["老年糖尿病合并衰弱患者 Fried标准 评估指标 定义 测量方法 意义"]
    # Call the get_user_feeds method
    for query in queries:
        user_feeds: List[tuple[float, UserFeedChunk]] = await retriever.search_chunks(job_id=1427, title=title, query=query)
        print(f"Query: {query}")
        for score, user_feed in user_feeds:
            print(f"Score: {score}, User Feed summary: {user_feed.summary}, Content: {user_feed.content}")
