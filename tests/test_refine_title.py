import pytest
from dotenv import load_dotenv

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.refine_title_agent import RefineTitleAgentInput
from thesis_writing.entity.enums import AgentType

load_dotenv()
@pytest.mark.parametrize(
    "subject", [
        "数字化转型背景下企业内部控制管理策略研究",
        "人力资源管理对组织绩效的作用与影响机制研究",
        "基于日志的学生成绩预警系统"
    ]
)
def test_refine_title(subject):
    # 生成全文概述
    agent = AgentFactory.create_agent(AgentType.REFINE_TITLE, TestUtil.get_qwen_model_options_from_env())
    refine_title_input = RefineTitleAgentInput(
        subject=subject
    )
    agent_output = agent.invoke(refine_title_input)
    print(f"初始概述：{agent_output}")
