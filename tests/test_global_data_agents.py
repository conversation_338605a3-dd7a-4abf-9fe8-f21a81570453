import asyncio
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed

import json
import os
from typing import List

from langfuse.decorators import observe

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.gen_plan_agent import GeneratePlanNode
from thesis_writing.agent.global_data.analysis_core_elements_agent import AnalysisCoreElementsAgentInput, \
    AnalysisCoreElementsAgentResponse
from thesis_writing.agent.global_data.global_data_agent import \
    AnalysisCommonGlobalDataQueryAgentInput, SummarizeCommonGlobalDataAgentInput, \
    AnalysisCommonGlobalDataQueryResponse, ExtractCommonGlobalDataQueryResultAgentInput, \
    ExtractCommonGlobalDataQueryResultAgentResponse
from thesis_writing.entity.enums import AgentType
from thesis_writing.retriever.retrieve_service import RetrieveService, RetrievalSourceType


def test_gen_global_data_multi():
    folder = "test_data/global_data2/"
    futures = []
    with Thread<PERSON>oolExecutor(max_workers=4) as executor:
        for filename in os.listdir(folder):
            f = os.path.join(folder, filename)
            fu = executor.submit(do_analysis_and_gen_global_data, f)
            futures.append(fu)

    for future in as_completed(futures):
        r = future.result()
        print(f"done {r}")


@observe(name="Test Global Data Agent")
def test_gen_global_data():
    do_analysis_and_gen_global_data("test_data/global_data2/859.txt")


def do_analysis_and_gen_global_data(file_path):
    test_data_dict = parse_test_data_file(file_path)

    analysis_entities_output = do_analysis_core_elements(test_data_dict)
    # analysis_data_queries_output = do_analysis_data_queries(test_data_dict, analysis_entities_output)
    # extract_data_output = asyncio.run(
    #     do_extract_query_result(test_data_dict, analysis_entities_output, analysis_data_queries_output))
    # summarize_global_data_output = do_summarize_global_data(test_data_dict, analysis_entities_output,
    #                                                         analysis_data_queries_output,
    #                                                         extract_data_output)

    text = json.dumps(test_data_dict, indent=2, ensure_ascii=False) + \
           "\n\nAnalysis entities: \noutput:\n" + json.dumps(analysis_entities_output.model_dump(), indent=2,
                                                             ensure_ascii=False)

    # text += "\n\nAnalysis queries: \noutput:\n" + json.dumps(analysis_data_queries_output.model_dump(), indent=2,
    #                                                 ensure_ascii=False)
    # text += "\n\nExtract queries reslut: \noutput:\n" + json.dumps(extract_data_output.model_dump(), indent=2,
    #                                                               ensure_ascii=False)
    # text += "\n\nSummarize global_data: \noutput:\n" + json.dumps(summarize_global_data_output.model_dump(), indent=2, ensure_ascii=False) + "\n\n"

    with open(file_path, "w") as file:
        file.write(text)

    return file_path


def parse_test_data_file(file_path) -> dict:
    with open(file_path, "r") as file:
        lines = file.readlines()

    content_lines = []
    for line in lines:
        content_lines.append(line)
        if line.startswith("}"):
            break

    return json.loads("\n".join(content_lines))


def get_writing_plan_str(test_data_dict):
    nodes: List[GeneratePlanNode] = []
    for dict in test_data_dict["writing_plan_nodes"]:
        nodes.append(GeneratePlanNode(**dict))

    return json.dumps([node.model_dump(exclude_none=True) for node in nodes], ensure_ascii=False)


def do_analysis_core_elements(test_data_dict):
    agent_input = AnalysisCoreElementsAgentInput(
        subject=test_data_dict["subject"],
        major=test_data_dict["major"],
        summary=test_data_dict["summary"],
        plan_str=get_writing_plan_str(test_data_dict)
    )

    agent_options = TestUtil.get_ds_model_options_from_env(temperature=1.0)
    agent = AgentFactory.create_agent(AgentType.ANALYZE_CORE_ELEMENTS, agent_options)
    agent_response = agent.invoke(agent_input)

    return agent_response


def do_analysis_data_queries(test_data_dict, analysis_entity_output):
    agent_input = AnalysisCommonGlobalDataQueryAgentInput(
        subject=test_data_dict["subject"],
        major=test_data_dict["major"],
        complete_writing_plan=get_writing_plan_str(test_data_dict),
        user_feeds_overview=test_data_dict["user_feeds_overview"],
        research_entities_str=", ".join(analysis_entity_output.research_entities)
    )

    agent_options = TestUtil.get_ds_model_options_from_env(temperature=0.5)
    agent = AgentFactory.create_agent(AgentType.GEN_GLOBAL_DATA_QUERY, agent_options)

    agent_response = agent.invoke(agent_input)

    return agent_response


async def do_extract_query_result(test_data_dict, analysis_entities_output: AnalysisCoreElementsAgentResponse,
                                  analysis_data_queries_output: AnalysisCommonGlobalDataQueryResponse):
    web_query_results: List[tuple[str, List]] = \
        await RetrieveService().retrieve_web_chunks(analysis_data_queries_output.web_queries, test_data_dict["major"],
                                                    test_data_dict["subject"], False)

    paper_query_results: List[tuple[str, List]] = \
        await RetrieveService().retrieve_chunks(RetrievalSourceType.Paper, analysis_data_queries_output.web_queries,
                                                test_data_dict["major"], test_data_dict["subject"], "", 5599)

    retrieval_chunks = []
    for query, chunks in paper_query_results or []:
        retrieval_chunks.extend([f"{chunk.content} \n来源：{chunk.author} {chunk.year} {chunk.title}" for chunk in chunks] if chunks else [])

    for query, chunks in web_query_results or []:
        retrieval_chunks.extend([f"{chunk.content} \n来源：{chunk.title} - 网络检索结果" for chunk in chunks] if chunks else [])

    if not retrieval_chunks:
        return None

    agent_input = ExtractCommonGlobalDataQueryResultAgentInput(
        subject=test_data_dict["subject"],
        major=test_data_dict["major"],
        complete_writing_plan=get_writing_plan_str(test_data_dict),
        research_entities_str=", ".join(analysis_entities_output.research_entities),
        global_data_str=analysis_data_queries_output.get_global_data_desc(),
        retrieval_materials=retrieval_chunks
    )

    agent_options = TestUtil.get_ds_model_options_from_env(temperature=0.5)
    agent = AgentFactory.create_agent(AgentType.EXTRACT_GLOBAL_DATA_QUERY_RESULT, agent_options)

    agent_response = agent.invoke(agent_input)

    return agent_response


def do_summarize_global_data(test_data_dict, analysis_entities_output: AnalysisCoreElementsAgentResponse,
                             analysis_data_queries_output: AnalysisCommonGlobalDataQueryResponse,
                             extract_data_queries_output: ExtractCommonGlobalDataQueryResultAgentResponse):
    summarize_input = SummarizeCommonGlobalDataAgentInput(
        subject=test_data_dict["subject"],
        major=test_data_dict["major"],
        complete_writing_plan=get_writing_plan_str(test_data_dict),
        research_entities_str=", ".join(analysis_entities_output.research_entities),
        global_data_str=analysis_data_queries_output.get_global_data_desc(),
        extracted_content=extract_data_queries_output.useful_content
    )

    agent_options = TestUtil.get_ds_model_options_from_env(temperature=0.5)
    failover_options = [TestUtil.get_aliyun_model_options_from_env(model="qwen-plus", temperature=0.7)]
    agent = AgentFactory.create_agent(AgentType.SUMMARIZE_GLOBAL_DATA, agent_options, failover_options)

    agent_response = agent.invoke(summarize_input)
    return agent_response


@observe(name="Test EXTRACT_GLOBAL_DATA_QUERY_RESULT")
def test_extract():
    summarize_input = ExtractCommonGlobalDataQueryResultAgentInput(
        subject="社区失能老人抑郁、焦虑症状现状调查及心理护理干预方案构建",
        major="护理学",
        complete_writing_plan="a",
        core_element_str="a",
        global_data_str="a",
        retrieval_materials=["a"]
    )

    # read content in /test_data/tet.txt
    with open("test_data/test.txt", "r") as file:
        lines = file.readlines()

    summarize_input.set_msg("\n".join(lines))

    agent_options = TestUtil.get_ds_model_options_from_env(temperature=0.5)
    agent = AgentFactory.create_agent(AgentType.EXTRACT_GLOBAL_DATA_QUERY_RESULT, agent_options)

    agent_response = agent.invoke(summarize_input)

    print(json.dumps(agent_response.model_dump(), indent=2, ensure_ascii=False))


@observe(name="Test SUMMARIZE_GLOBAL_DATA")
def test_summ():
    summarize_input = SummarizeCommonGlobalDataAgentInput(
        subject="社区失能老人抑郁、焦虑症状现状调查及心理护理干预方案构建",
        major="护理学",
        complete_writing_plan="a",
        research_entities_str="a",
        global_data_str="a",
        extracted_content="a"
    )

    # read content in /test_data/tet.txt
    with open("test_data/test2.txt", "r") as file:
        lines = file.readlines()

    summarize_input.set_msg("\n".join(lines))

    agent_options = TestUtil.get_ds_model_options_from_env(temperature=0.5)
    agent = AgentFactory.create_agent(AgentType.SUMMARIZE_GLOBAL_DATA, agent_options)

    agent_response = agent.invoke(summarize_input)

    print(json.dumps(agent_response.model_dump(), indent=2, ensure_ascii=False))
