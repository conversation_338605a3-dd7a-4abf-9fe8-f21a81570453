import pytest
import time

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.gen_keywords_agent import GenerateKeywordsAgentInput
from thesis_writing.entity.enums import AgentType

options = TestUtil.get_ds_model_options_from_env()
agent = AgentFactory.create_agent(AgentType.GEN_KEYWORDS, options)

@pytest.mark.parametrize(
    "subject, major,keywords_num", [
        ("大洋公司舒雅花苑项目进度管理的研究", "", "5个"),
        ("浙西农村小学低段留守儿童课外阅读现状与对策研究", "小学教育","8个"),
        ("基于单片机的黑板粉尘监测清除装置设计", "电气工程及其自动化","10个"),
        ("基于校园大数据的学生信息管理平台的设计研究", "计算机科学与技术", "6个"),
        ("土木工程施工中的质量控制与安全管理", "无","1到10个"),
        ("T镗床主轴箱传动设计与尾柱设计", "机械制造及其自动化","3到5个"),
        ("微创技术在小儿外科手术中的安全性与有效性", "无","1个"),
        ("小米公司财务风险评价与控制策略研究", "会计学","4到8个"),
    ]
)
def test_title(subject, major,keywords_num):
    start = time.time()
    generate_keys_input = GenerateKeywordsAgentInput(
        subject=subject,
        major=major,
        keywords_num=keywords_num,
    )
    agent_output = agent.invoke(generate_keys_input)
    end = time.time()
    print((end - start), subject, agent_output.model_dump_json())
