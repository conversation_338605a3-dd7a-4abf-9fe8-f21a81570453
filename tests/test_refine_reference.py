import json
import time

from dotenv import load_dotenv

from tests.utils.utils import TestUtil
from thesis_writing.agent.base.agent_factory import AgentFactory
from thesis_writing.agent.refine_reference_agent import RefineReferenceAgentInput
from thesis_writing.entity.enums import AgentType

load_dotenv()


def test_refine_title():
    references = [
        "杨立新,李怡雯.网约车聚合平台经营者的注意义务与侵权责任[J].法律适用,2022,(06):3-15.",
        "崔彦.基于安全保障前提下网约车平台的义务与责任[J].法制博览,2021,(16):48-49.",
        "乔睿,程国华.网约车聚合模式规范发展问题研究[J].青海交通科技,2021,33(05):7-11+26.",
        "崔弘.数据仓库建设方案浅析[C]//天津市电子学会.第三十六届中国（天津）2022’IT、网络、信息技术、电子、仪器仪表创新学术会议论文集.长城(天津)质量保证中心有限公司;,2022:4.DOI:10.26914/c.cnkihy.2022.014980.",
        "王伟. 人工智能在医疗诊断中的应用研究[J]. 计算机科学, 2021, 48(3): 45-52.",
        "刘洋. 新能源汽车产业发展趋势分析[N]. 中国科技报, 2022-05-12(03).",
        "李强. 机器学习基础[M]. 2版. 北京: 清华大学出版社, 2020.",
        "王鹏. 区块链技术在金融领域的应用[C]. //李华. 2021年国际金融科技会议论文集, 北京: 中国金融出版社, 2021: 123-130.",
        "张磊. 基于深度学习的自然语言处理技术研究[D]. 北京: 北京大学, 2021.",
        "王伟. 中国人工智能产业发展报告[R]. 北京: 中国信息通信研究院, 2022.",
        "王磊. 人工智能技术发展趋势分析[EB/OL]. 2022[2023-10-01]. https://www.example.com/ai-trends.",
        "Rui C .Two step vehicle trajectory reconstruction strategy based on an improved outlier detection and data smoothing algorithm[C]//Chang’an University (China),2024:",
        "CHEN S ,ZHOU G ,AN X .An efficient indexing structure for multi-dimensional range query[J].Frontiers of Computer Science,2021,(04):171-173."
    ]
    for reference in references:
        begin_time = time.time()
        agent = AgentFactory.create_agent(AgentType.REFINE_REFERENCE, TestUtil.get_qwen_model_options_from_env())
        refine_reference_input = RefineReferenceAgentInput(
            reference=reference
        )
        agent_output = agent.invoke(refine_reference_input)
        print("elapsed time:", time.time() - begin_time)
        # then
        print(f"初始引用名称：{reference}")
        print(json.dumps(agent_output.model_dump(exclude_none=True), indent=2, ensure_ascii=False))
