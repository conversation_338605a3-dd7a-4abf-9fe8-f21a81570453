import os
from docx import Document
import zipfile
import os

def replace_placeholders_in_word(template_path, output_path, replacements):
    doc = Document(template_path)

    def replace_text_with_style(paragraph, replacements):
        for placeholder, content in replacements.items():
            content = str(content) if content else ""
            full_placeholder = f"【【{placeholder}】】"
            placeholder_len = len(full_placeholder)
            runs_text = "".join([run.text for run in paragraph.runs])
            if full_placeholder in runs_text:
                start_index = runs_text.find(full_placeholder)
                end_index = start_index + placeholder_len
                current_index = 0
                first_run_index = -1
                runs_to_clear = []

                for i, run in enumerate(paragraph.runs):
                    run_len = len(run.text)
                    if first_run_index == -1 and start_index >= current_index and start_index < current_index + run_len:
                        first_run_index = i

                    if first_run_index != -1 and current_index + run_len > start_index and current_index < end_index:
                      runs_to_clear.append(run)

                    current_index += run_len

                if first_run_index != -1:
                  runs_to_clear[0].text = runs_to_clear[0].text[:start_index-sum(len(r.text) for r in paragraph.runs[:first_run_index])] + (content if content else "")

                  for run in runs_to_clear[1:]:
                      run.text = ""

    for paragraph in doc.paragraphs:
        replace_text_with_style(paragraph, replacements)

    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    replace_text_with_style(paragraph, replacements)

    doc.save(output_path)

def compress_word_files_to_zip(word_files, zip_file_path):
    with zipfile.ZipFile(zip_file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file in word_files:
            if os.path.exists(file):
                zipf.write(file, arcname=os.path.basename(file))
            else:
                print(f"文件不存在，跳过: {file}")