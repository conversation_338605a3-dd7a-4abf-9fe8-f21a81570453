import streamlit as st
import asyncio

from topic import topic
from thesis_proposal import thesis_proposal
from task_statement import task_statement

import dotenv
dotenv.load_dotenv()

st.set_page_config(page_title="论文写作助手", layout="wide")

MENU_OPTIONS = {
    "选题推荐": topic,
    "开题报告": thesis_proposal,
    "任务书": task_statement,
}

async def render_menu():
    choice = st.sidebar.selectbox("导航菜单", list(MENU_OPTIONS.keys()))
    if asyncio.iscoroutinefunction(MENU_OPTIONS[choice]):
        await MENU_OPTIONS[choice]()
    else:
        MENU_OPTIONS[choice]()

if __name__ == "__main__":
    asyncio.run(render_menu())
