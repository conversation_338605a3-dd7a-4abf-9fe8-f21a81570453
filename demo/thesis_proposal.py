import asyncio

import streamlit as st

from thesis_writing.agent.proposal.gen_thesis_proposal_query_agent import GenerateThesisProposalQueryAgent, GenerateThesisProposalQueryAgentInput, GenerateThesisProposalQueryAgentResponse
from thesis_writing.agent.proposal.gen_thesis_proposal_agent import GenerateThesisProposalAgent, GenerateThesisProposalAgentInput, GenerateThesisProposalAgentResponse
from thesis_writing.agent.base.agent_options import AgentOptions
from thesis_writing.agent.gen_summary_agent import GenerateSummaryAgentInput, GenerateSummaryAgentResponse, GenerateSummaryAgent

from langfuse.decorators import langfuse_context, observe
import os
from openpyxl import load_workbook
from io import BytesIO
import datetime
from utils import replace_placeholders_in_word, compress_word_files_to_zip
from pathlib import Path
from thesis_writing.utils.reference import ReferenceManager, ThesisReference
from thesis_writing.retriever.retrieve_service import RetrieveService
import re
from pydantic import BaseModel, Field

import dotenv
dotenv.load_dotenv()

options = AgentOptions(
    base_url=os.environ["DEEPSEEK_MODEL_BASE_URL"],
    api_key=os.environ["DEEPSEEK_MODEL_API_KEY"],
    model=os.environ["DEEPSEEK_MODEL_NAME"],
    temperature=float(os.environ["DEEPSEEK_MODEL_TEMPERATURE"])
)
failover_options = None

class ProposalData(BaseModel):
    summary: str = Field(..., title="全文概要")
    modules: dict[str, str] = Field(..., title="开题报告模块和内容")
    reference: str = Field(..., title="参考文献")

async def get_proposal_reference(subject: str, queries: list[str]) -> ReferenceManager:
    manager = ReferenceManager()
    if not queries:
        return manager

    reference_list = []
    for query in queries:
        reference_result = await RetrieveService().search_thesis_reference(query=query, subject=subject, size=10,
                                                                        threshold=0.6)
        reference_list.extend(reference_result)
    reference_list = {reference.title: (score, reference) for score, reference in reference_list}.values()
    reference_list = sorted(reference_list, key=lambda x: x[0], reverse=True)[:30]
    manager.extend_references([
        ThesisReference(id=ref.id, title=ref.title, summary=ref.summary, year=ref.year, source=ref.source,
                        authors=ref.authors, citation=ref.citation) for _, ref in reference_list])
    return manager

def replace_markdown_output(text):
    return re.sub(r'(#{3,4} |[*]{2})', '', text)

def replace_references_index(agent_output: GenerateThesisProposalAgentResponse, references):
    id_sequence_map = {reference.id: reference.sequence for reference in references if reference.sequence}
    for each in agent_output.thesis_proposal:
        if each.chapter_content:
            for ref_id, sequence in id_sequence_map.items():
                each.chapter_content = each.chapter_content.replace(f"[[{ref_id}]]", "")
    return agent_output

def gen_proposal_retrieve_queries(subject: str,
                                       summary: str) -> list[str]:
    agent = GenerateThesisProposalQueryAgent(options, failover_options_list=[])
    query_input = GenerateThesisProposalQueryAgentInput(
        subject=subject,
        summary=summary
    )
    result: GenerateThesisProposalQueryAgentResponse = agent.invoke(query_input)
    return result.queries

def gen_summary(major: str, subject: str, keywords: str):
    agent = GenerateSummaryAgent(options, failover_options_list=[])
    input = GenerateSummaryAgentInput.construct_input_with_retrieval(major, subject, keywords)
    response: GenerateSummaryAgentResponse = agent.invoke(input)
    return response.summary.replace("**", " ")

@observe(capture_input=False, capture_output=False)
async def gen_proposal(major: str, subject: str, keywords: str, summary: str, sections: list[str]) -> ProposalData | None:
    langfuse_context.update_current_trace(
        name="GenerateThesisProposal",
        tags=["2B业务"]
    )
    try:
        if not summary:
            summary = gen_summary(major, subject, keywords)
        queries = gen_proposal_retrieve_queries(subject, summary)
        reference_manager = await get_proposal_reference(subject, queries)
        gen_thesis_proposal_input = GenerateThesisProposalAgentInput(
            subject=subject,
            toc='\n'.join(sections),
            summary=summary,
            reference=reference_manager.to_detail_str(),
        )
        agent = GenerateThesisProposalAgent(options, failover_options_list=[])
        agent_output: GenerateThesisProposalAgentResponse = agent.invoke(gen_thesis_proposal_input)
        reference_manager.collect_ref(agent_output.main_content())
        for each in agent_output.thesis_proposal:
            each.chapter_content = replace_markdown_output(each.chapter_content)
        agent_output = replace_references_index(agent_output, reference_manager.references)
        reference_text = '\n'.join(
            [f"[{each.sequence if each.sequence else index + 1}] {each.citation}" for index, each in
             enumerate(sorted(reference_manager.references,
                              key=lambda reference: (reference.sequence if reference.sequence else 999)))])
        return ProposalData(
            summary=summary,
            modules={each.chapter_title if ' ' not in each.chapter_title else each.chapter_title.split(' ')[1] : each.chapter_content for each in agent_output.thesis_proposal},
            reference=reference_text
        )

    except Exception as e:
        print(f"Error in generating thesis proposal for {subject}: {e}")

async def process_thesis_proposal(input_file, timestamp, template_file, overwrite=True, max_workers=4):
    wb = load_workbook(input_file)
    sheet = wb.active

    headers = [cell.value for cell in sheet[1]]
    headers_count = len(headers)

    if "专业" not in headers or "选题" not in headers or "关键词" not in headers or "开题报告模块" not in headers or "学号" not in headers:
        raise ValueError("Excel 文件必须包含 '学号'、'专业'、'选题'、'关键词' 和 '开题报告模块' 列。")

    student_id_idx = headers.index("学号")
    major_idx = headers.index("专业")
    subject_idx = headers.index("选题")
    keywords_idx = headers.index("关键词")
    summary_idx = headers.index("全文概要") if "全文概要" in headers else None
    modules_idx = headers.index("开题报告模块")

    if not summary_idx:
        summary_idx = headers_count
        headers_count += 1
        sheet.cell(row=1, column=summary_idx + 1, value="全文概要")

    sections = set()
    for row in sheet.iter_rows(min_row=2, max_row=sheet.max_row):
        sections_text = row[modules_idx].value
        if sections_text:
            sections.update(sections_text.replace("；", ";").split(";"))

    sections_idx_map = dict()
    for section in sections:
        if section not in headers:
            section_idx = headers_count
            headers_count += 1
            sheet.cell(row=1, column=section_idx + 1, value=section)
            sections_idx_map[section] = section_idx
        else:
            sections_idx_map[section] = headers.index(section)

    reference_index = headers.index("参考文献") if "参考文献" in headers else None
    if reference_index is None:
        reference_index = headers_count
        headers_count += 1
        sheet.cell(row=1, column=reference_index + 1, value="参考文献")

    tasks = []
    for row in sheet.iter_rows(min_row=2, max_row=sheet.max_row):
        major = row[major_idx].value
        subject = row[subject_idx].value
        keywords = row[keywords_idx].value
        sections_text = row[modules_idx].value
        sections = sections_text.replace("；", ";").split(";") if sections_text else []
        if summary_idx < 0:
            summary = None
        else:
            summary = row[summary_idx].value
        if major and subject and sections:
            tasks.append((row, major, subject, keywords, summary, sections))

    async def process_row(row, major, subject, keywords, summary, sections, summary_idx, sections_idx_map, reference_index, overwrite):
        result = await gen_proposal(major, subject, keywords, summary, sections)
        if result:
            summary = result.summary
            modules = result.modules
            reference = result.reference
            if overwrite or not row[summary_idx].value:
                row[summary_idx].value = summary
            for section, idx in sections_idx_map.items():
                if section in modules.keys():
                    if overwrite or not row[idx].value:
                        row[idx].value = modules[section]
            if overwrite or not row[reference_index].value:
                row[reference_index].value = reference

    async def process_tasks(tasks, summary_idx, sections_idx_map, reference_index, overwrite):
        await asyncio.gather(*[
            process_row(row, major, subject, keywords, summary, sections, summary_idx, sections_idx_map, reference_index, overwrite)
            for row, major, subject, keywords, summary, sections in tasks
        ])

    await process_tasks(tasks, summary_idx, sections_idx_map, reference_index, overwrite)

    if template_file:
        output_dir = Path("output") / f"开题报告_{timestamp}"
        output_dir.mkdir(exist_ok=True, parents=True)
        headers = [cell.value for cell in sheet[1]]
        files = []
        for row in sheet.iter_rows(min_row=2, max_row=sheet.max_row):
            if not row[subject_idx].value:
                continue
            subject = row[subject_idx].value
            student_id = str(row[student_id_idx].value)
            data = {headers[i]: cell.value for i, cell in enumerate(row)}
            word_filename = output_dir / f"{student_id}-{subject}.docx"
            replace_placeholders_in_word(template_file, word_filename, data)
            files.append(word_filename)

        proposal_zip_file = Path("output") / f"开题报告_{timestamp}.zip"
        compress_word_files_to_zip(files, proposal_zip_file)
        if output_dir.exists():
            for file in output_dir.glob("*"):
                file.unlink()
            output_dir.rmdir()

    return wb, str(proposal_zip_file)

async def thesis_proposal():
    st.title("开题报告")

    uploaded_file = st.file_uploader(
        "上传选题 Excel 文件（需包含“学号”、“专业”、“选题”、“关键词”和“开题报告模块”列）", type=["xlsx"]
    )
    uploaded_template_file = st.file_uploader(
        "上传开题报告模板 Word 文件", type=["docx"]
    )
    overwrite = True

    if st.button("生成开题报告"):
        if uploaded_file:
            try:
                with st.spinner("正在生成开题报告..."):
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_wb, proposal_zip_filename = await process_thesis_proposal(uploaded_file, timestamp,  uploaded_template_file, overwrite)

                    st.session_state["proposal_excel_file"] = output_wb
                    st.session_state["proposal_zip_file"] = proposal_zip_filename
                    st.session_state["proposal_processed_filename"] = f"开题报告_{timestamp}.xlsx"
                    st.session_state["proposal_zip_filename"] = f"开题报告_{timestamp}.zip"

            except Exception as e:
                st.error(f"文件处理失败：{e}")
        else:
            st.warning("请先上传文件")

    if "proposal_excel_file" in st.session_state and "proposal_zip_file" in st.session_state:
        st.success("开题报告生成成功！")
        output = BytesIO()
        st.session_state["proposal_excel_file"].save(output)
        output.seek(0)
        st.download_button(
            label="下载处理后的 Excel",
            data=output,
            file_name=st.session_state["proposal_processed_filename"],
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )

        with open(st.session_state["proposal_zip_file"], "rb") as f:
            st.download_button(
                label="下载开题报告 Word 文件",
                data=f,
                file_name=st.session_state["proposal_zip_filename"],
                mime="application/zip",
            )
