import streamlit as st

from thesis_writing.agent.gen_topic_agent import GenerateTopicAgent, GenerateTopicAgentInput, GenerateTopicAgentResponse, ThesisTopic
from thesis_writing.agent.base.agent_options import AgentOptions
import dotenv

from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from langfuse.decorators import langfuse_context, observe
import os
from openpyxl import load_workbook
from io import BytesIO
from pathlib import Path
import datetime

import dotenv
dotenv.load_dotenv()

options = AgentOptions(
    base_url=os.environ["DEEPSEEK_MODEL_BASE_URL"],
    api_key=os.environ["DEEPSEEK_MODEL_API_KEY"],
    model=os.environ["DEEPSEEK_MODEL_NAME"],
    temperature=float(os.environ["DEEPSEEK_MODEL_TEMPERATURE"])
)

@observe
def generate_topic_by_major(major: str, comment: str):
    langfuse_context.update_current_trace(
        name="GenerateTopic",
        tags=["2B业务"]
    )
    try:
        input = GenerateTopicAgentInput(major=major, count=1, comment=comment)
        agent = GenerateTopicAgent(options=options, failover_options_list=[])
        response: GenerateTopicAgentResponse = agent.invoke(input)
        return response.topics[0]
    except Exception as e:
        print(f"Error in generating topic for {major}: {e}")

def process_topic(input_file, overwrite=True, max_workers=10):
    wb = load_workbook(input_file)
    sheet = wb.active

    headers = [cell.value for cell in sheet[1]]
    headers_count = len(headers)

    if "专业" not in headers or "题目要求" not in headers:
        raise ValueError("Excel 文件必须包含 '专业' 和 '题目要求' 列。")

    major_idx = headers.index("专业")
    comment_idx = headers.index("题目要求")

    topic_idx = headers.index("选题") if "选题" in headers else None
    keywords_idx = headers.index("关键词") if "关键词" in headers else None
    reason_idx = headers.index("选题理由") if "选题理由" in headers else None

    if topic_idx is None:
        topic_idx = headers_count
        headers_count += 1
        sheet.cell(row=1, column=topic_idx + 1, value="选题")
    if keywords_idx is None:
        keywords_idx = headers_count
        headers_count += 1
        sheet.cell(row=1, column=keywords_idx + 1, value="关键词")
    if reason_idx is None:
        reason_idx = headers_count
        headers_count += 1
        sheet.cell(row=1, column=reason_idx + 1, value="选题理由")

    tasks = []
    for row in sheet.iter_rows(min_row=2, max_row=sheet.max_row):
        major = row[major_idx].value
        comment = row[comment_idx].value

        if major:
            tasks.append((row, major, comment))

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {
            executor.submit(generate_topic_by_major, major, comment): row
            for row, major, comment in tasks
        }

        for i, future in enumerate(as_completed(futures)):
            row = futures[future]
            topic: ThesisTopic = future.result()

            if topic:
                if overwrite or not row[topic_idx].value:
                    row[topic_idx].value = topic.title
                if overwrite or not row[keywords_idx].value:
                    row[keywords_idx].value = topic.keywords
                if overwrite or not row[reason_idx].value:
                    row[reason_idx].value = topic.reason

    return wb

def topic():
    st.title("选题推荐")

    uploaded_file = st.file_uploader(
        "上传选题 Excel 文件（需包含“专业”和“题目要求”列）", type=["xlsx"]
    )
    overwrite = True

    if st.button("生成选题"):
        if uploaded_file:
            try:
                with st.spinner("正在生成选题..."):
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_wb = process_topic(uploaded_file, overwrite)
                    st.session_state["excel_file"] = output_wb
                    st.session_state["processed_filename"] = f"选题_{timestamp}.xlsx"

            except Exception as e:
                st.error(f"文件处理失败：{e}")
        else:
            st.warning("请先上传文件")

    if "excel_file" in st.session_state:
        st.success("选题生成成功！")
        output = BytesIO()
        st.session_state["excel_file"].save(output)
        output.seek(0)
        st.download_button(
            label="下载处理后的 Excel",
            data=output,
            file_name=st.session_state["processed_filename"],
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )