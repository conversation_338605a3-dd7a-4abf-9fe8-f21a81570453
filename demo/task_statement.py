import streamlit as st

from thesis_writing.agent.task_statement.gen_task_statement_agent import GenerateTaskStatementAgent, GenerateTaskStatementAgentInput, GenerateTaskStatementAgentResponse
from thesis_writing.agent.base.agent_options import AgentOptions

from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from langfuse.decorators import langfuse_context, observe
import os
from openpyxl import load_workbook
from io import BytesIO
from utils import replace_placeholders_in_word, compress_word_files_to_zip
from pathlib import Path
import dotenv
dotenv.load_dotenv()
import datetime

options = AgentOptions(
    base_url=os.environ["DEEPSEEK_MODEL_BASE_URL"],
    api_key=os.environ["DEEPSEEK_MODEL_API_KEY"],
    model=os.environ["DEEPSEEK_MODEL_NAME"],
    temperature=float(os.environ["DEEPSEEK_MODEL_TEMPERATURE"])
)
failover_options = None

@observe(capture_input=False, capture_output=False)
def gen_task_statement(major: str, subject: str, keywords: str, summary: str, sections: list[str]) -> dict[str, str] | None:
    langfuse_context.update_current_trace(
        name="GenerateTaskStatement",
        tags=["2B业务"]
    )
    try:
        gen_thesis_proposal_input = GenerateTaskStatementAgentInput(
            subject=subject,
            major=major,
            keywords=keywords,
            toc='\n'.join(sections),
            summary=summary,
        )
        agent = GenerateTaskStatementAgent(options, failover_options_list=[])
        agent_output: GenerateTaskStatementAgentResponse = agent.invoke(gen_thesis_proposal_input)
        return {each.title if ' ' not in each.title else each.title.split(' ')[1] : each.chapter_content for each in agent_output.task_statement}

    except Exception as e:
        print(f"Error in generating task statement for {subject}: {e}")

def process_task_statement(input_file, timestamp, template_file, overwrite=True, max_workers=4):
    wb = load_workbook(input_file)
    sheet = wb.active

    headers = [cell.value for cell in sheet[1]]
    headers_count = len(headers)

    if "专业" not in headers or "选题" not in headers or "关键词" not in headers \
        or "任务书模块" not in headers or "全文概要" not in headers or "学号" not in headers:
        raise ValueError("Excel 文件必须包含 '学号'、 '专业'、'选题'、'关键词'、'全文概要' 和 '任务书模块' 列。")

    student_id_idx = headers.index("学号")
    major_idx = headers.index("专业")
    subject_idx = headers.index("选题")
    keywords_idx = headers.index("关键词")
    summary_idx = headers.index("全文概要")
    modules_idx = headers.index("任务书模块")

    sections = set()
    for row in sheet.iter_rows(min_row=2, max_row=sheet.max_row):
        sections_text = row[modules_idx].value
        if sections_text:
            sections.update(sections_text.replace("；", ";").split(";"))

    sections_idx_map = dict()
    for section in sections:
        if section not in headers:
            section_idx = headers_count
            headers_count += 1
            sheet.cell(row=1, column=section_idx + 1, value=section)
            sections_idx_map[section] = section_idx
        else:
            sections_idx_map[section] = headers.index(section)

    tasks = []
    for row in sheet.iter_rows(min_row=2, max_row=sheet.max_row):
        major = row[major_idx].value
        subject = row[subject_idx].value
        keywords = row[keywords_idx].value
        summary = row[summary_idx].value
        sections_text = row[modules_idx].value
        sections = sections_text.replace("；", ";").split(";") if sections_text else []
        if major and subject and summary and sections:
            tasks.append((row, major, subject, keywords, summary, sections))

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {
            executor.submit(gen_task_statement, major, subject, keywords, summary, sections): row
            for row, major, subject, keywords, summary, sections in tasks
        }

        for i, future in enumerate(as_completed(futures)):
            row = futures[future]
            result = future.result()

            if result:
                for section, idx in sections_idx_map.items():
                    if section in result.keys():
                        if overwrite or not row[idx].value:
                            row[idx].value = result[section]

    if template_file:
        output_dir = Path("output") / f"任务书_{timestamp}"
        output_dir.mkdir(exist_ok=True, parents=True)
        headers = [cell.value for cell in sheet[1]]
        files = []
        for row in sheet.iter_rows(min_row=2, max_row=sheet.max_row):
            if not row[subject_idx].value:
                continue
            subject = row[subject_idx].value
            student_id = str(row[student_id_idx].value)
            data = {headers[i]: cell.value for i, cell in enumerate(row)}
            word_filename = output_dir / f"{student_id}-{subject}.docx"
            replace_placeholders_in_word(template_file, word_filename, data)
            files.append(word_filename)

        task_zip_file = Path("output") / f"任务书_{timestamp}.zip"
        compress_word_files_to_zip(files, task_zip_file)
        if output_dir.exists():
            for file in output_dir.glob("*"):
                file.unlink()
            output_dir.rmdir()

    return wb, str(task_zip_file)

def task_statement():
    st.title("任务书")

    uploaded_file = st.file_uploader(
        "上传选题 Excel 文件（需包含“学号”、“专业”、“选题”、“关键词”、”全文概要“和“任务书模块”列）", type=["xlsx"]
    )

    uploaded_template_file = st.file_uploader(
        "上传任务书模板 Word 文件", type=["docx"]
    )

    overwrite = True

    if st.button("生成任务书"):
        if uploaded_file:
            try:
                with st.spinner("正在生成任务书..."):
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_wb, task_zip_filename = process_task_statement(uploaded_file, timestamp, uploaded_template_file, overwrite)

                    st.session_state["task_excel_file"] = output_wb
                    st.session_state["task_zip_file"] = task_zip_filename
                    st.session_state["task_processed_filename"] = f"任务书_{timestamp}.xlsx"
                    st.session_state["task_zip_filename"] = f"任务书_{timestamp}.zip"

            except Exception as e:
                st.error(f"文件处理失败：{e}")
        else:
            st.warning("请先上传文件")

    if "task_excel_file" in st.session_state and "task_zip_file" in st.session_state:
        st.success("任务书生成成功！")
        output = BytesIO()
        st.session_state["task_excel_file"].save(output)
        output.seek(0)
        st.download_button(
            label="下载处理后的 Excel",
            data=output,
            file_name=st.session_state["task_processed_filename"],
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )

        with open(st.session_state["task_zip_file"], "rb") as f:
            st.download_button(
                label="下载任务书 Word 文件",
                data=f,
                file_name=st.session_state["task_zip_filename"],
                mime="application/zip",
            )
