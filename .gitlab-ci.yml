image: python:3.12

stages:
  - deploy

deploy:
  stage: deploy
  tags:
    - python

  script:
    - export all_proxy=http://127.0.0.1:1087
    - pip install build twine poetry
    - export COMMIT_SHA=$(git rev-parse --short HEAD)
    - export VERSION="${CI_COMMIT_TAG}+${COMMIT_SHA}"
    - echo "Setting version to $VERSION"
    - poetry version $VERSION
    - poetry build
    - export all_proxy=
    - TWINE_PASSWORD=${CI_JOB_TOKEN} TWINE_USERNAME=gitlab-ci-token python -m twine upload --repository-url ${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/pypi dist/*

  only:
    - tags
